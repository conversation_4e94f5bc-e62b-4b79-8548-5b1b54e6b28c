<?php

namespace Open\BackBundle\Controller;

use AppBundle\Entity\Setting;
use AppBundle\Entity\SettingsCollection;
use AppBundle\Repository\SettingRepository;
use Open\BackBundle\Events\UpdateSettingsEvent;
use Open\BackBundle\Form\SettingsForm;
use Psr\Cache\CacheItemPoolInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Core\Authorization\AuthorizationCheckerInterface;
use Symfony\Component\Security\Core\Exception\AccessDeniedException;
use Symfony\Contracts\Translation\TranslatorInterface;


class SettingsController extends AbstractController
{
    private const DOMAIN = 'domain';
    private AuthorizationCheckerInterface $authorizationChecker;

    public function __construct(AuthorizationCheckerInterface $authorizationChecker)
    {
        $this->authorizationChecker = $authorizationChecker;
    }

    /**
     * @param $operatorCan
     * Throw 403 if user don't have an Admin role
     */
    private function checkAccess($operatorCan)
    {
        if (!$this->authorizationChecker->isGranted('ROLE_SUPER_ADMIN') && (!$operatorCan || !$this->authorizationChecker->isGranted('ROLE_OPERATOR'))) {
            throw new AccessDeniedException(sprintf('Access Denied to the action %s', 'INDEX'));
        }
    }

    /**
     * Get param from config file
     * @return mixed
     */
    private function getSettingsFromConfig()
    {
        return $this->getParameter('app.settings');
    }

    /**
     * @Route("/settings", name="admin.settings.index")
     * @param Request $request
     *
     * @return Response
     */
    public function indexAction(Request $request): Response
    {
        // Don't build magic admin for these settings (domain settings)
        $toExclude = ['offers'];

        $this->checkAccess(false);

        $domains = $this->getSettingsFromConfig();
        foreach ($domains as $key => $domain) {
            foreach ($toExclude as $excluded) {
                if ($excluded == $key) {
                    unset($domains[$key]);
                }
            }
        }

        return $this->render('OpenBackBundle:settings:index.html.twig', ['domains' => $domains]);
    }

    /**
     * @Route("/settings/{domain}", name="system.settings.domain")
     * @param Request $request
     * @param         $domain
     *
     * @return Response
     */
    public function domainAction(Request $request, $domain)
    {
        $this->checkAccess(false);

        $domains = $this->getSettingsFromConfig();

        if (!isset($domains[$domain])) {
            throw new AccessDeniedException(sprintf('Access Denied'));
        }

        return $this->render(
            'OpenBackBundle:settings:domain.html.twig',
            [
                self::DOMAIN => $domain,
                'groups' => $domains[$domain]
            ]
        );
    }

    /**
     * @Route("/settings/{domain}/{group}", name="system.settings.group")
     */
    public function groupAction(Request $request, TranslatorInterface $translator, SettingRepository $settingRepository, CacheItemPoolInterface $cache_pool, EventDispatcherInterface $dispatcher, $domain, $group)
    {
        $operatorCan = false;
        if ($domain == 'offers') {
            $operatorCan = true;
        }

        // Check if access is granted (403 otherwise)
        $this->checkAccess($operatorCan);

        $domains = $this->getSettingsFromConfig();

        // If the domain doesn't exist then 403
        if (!isset($domains[$domain])) {
            throw new AccessDeniedException(sprintf('Access Denied'));
        }

        // If the group doesn't exist then redirect to the parent domain
        if (!isset($domains[$domain][$group])) {
            return $this->redirectToRoute(
                'admin.settings.domain',
                [
                    self::DOMAIN => $domain
                ]
            );
        }

        $keys = [];

        foreach ($domains[$domain][$group] as $key => $params) {
            $keys[] = $key;
        }


        // Create an empty collection of setting
        $settingsCollection = new SettingsCollection();

        // Find parameters needed for the page
        $settings = $settingRepository->findByDomainAndKeys($domain, $keys);


        // Build a collection with all the settings we want in the form
        $settingsCollection->setSettings($settings);

        // Create the form
        $form = $this->createForm(
            SettingsForm::class,
            $settingsCollection
        );

        $form->handleRequest($request);

        // Handle form submission
        if ($form->isSubmitted() && $form->isValid()) {

            $em = $this->getDoctrine()->getManager();

            /** @var SettingsCollection $data */
            $data = $form->getData();

            $settings = $data->getSettings();

            /** @var Setting $setting */
            foreach ($settings as $setting) {
                $em->persist($setting);
            }

            try {
                $em->flush();

                /** @var Setting $setting */
                foreach ($settings as $setting) {
                    $cachedSetting = $cache_pool->getItem($setting->getFullName());
                    $cachedSetting->set($setting->getValue());
                    $cache_pool->saveDeferred($cachedSetting);
                }

                // Commit defered save
                $cache_pool->commit();

                //dispatch event
                $event = new UpdateSettingsEvent();
                $event->setGroup($group);
                $event->setDomain($domain);
                $dispatcher->dispatch($event, UpdateSettingsEvent::EVENT_NAME);

                $this->addFlash(
                    'success',
                    $translator->trans('system.settings.update_success', [], 'AppBundle')
                );

            } catch (\Exception $e) {
                $this->addFlash(
                    'error',
                    $translator->trans('system.settings.update_error', [], 'AppBundle')
                );
                $this->addFlash(
                    'error',
                    $e->getMessage()
                );
            }
        }

        // Render form
        return $this->render(
            'OpenBackBundle:settings:group.html.twig',
            [
                self::DOMAIN => $domain,
                'group' => $group,
                'form' => $form->createView()
            ]
        );
    }

}