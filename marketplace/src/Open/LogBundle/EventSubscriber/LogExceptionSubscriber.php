<?php

namespace Open\LogBundle\EventSubscriber;

use Open\LogBundle\Service\LogService;
use Open\LogBundle\Utils\EventNameEnum;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\HttpKernel\Event\ExceptionEvent;
use Symfony\Component\HttpKernel\KernelEvents;


class LogExceptionSubscriber implements EventSubscriberInterface
{
    private LogService $logger;

    public function __construct(LogService $logger)
    {
        $this->logger = $logger;
    }

    public static function getSubscribedEvents(): array
    {
        // Catch kernel errors to trigger logs actions
        return array(
            KernelEvents::EXCEPTION => 'onKernelException'
        );
    }

    public function onKernelException(ExceptionEvent $event)
    {
        if ($event->getThrowable() != null) {
            $event->getThrowable()->getCode();

            $this->logger->critical($event->getThrowable()->getMessage(), EventNameEnum::TECHNICAL_ERROR, null, [
                'error_file' => $event->getThrowable()->getFile(),
                'error_name' => get_class($event->getThrowable()),
                'error_code' => $event->getThrowable()->getCode()
            ]);
        } else {
            $this->logger->critical("An unknown error has occurred :", EventNameEnum::TECHNICAL_ERROR, null, [
                'error_file' => 'unknown',
                'error_name' => 'unknown'
            ]);
        }
    }
}