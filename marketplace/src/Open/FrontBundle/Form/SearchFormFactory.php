<?php

namespace Open\FrontBundle\Form;

use AppBundle\Model\OfferSearchResult;
use Open\FrontBundle\Form\Type\FacetFilterType;
use Symfony\Component\Form\FormFactoryInterface;
use Symfony\Component\Form\FormInterface;

class SearchFormFactory
{
    /**
     * @var FormFactoryInterface
     */
    private $formFactoryInterface;

    public function __construct(FormFactoryInterface $formFactoryInterface)
    {
        $this->formFactoryInterface = $formFactoryInterface;
    }

    public function build(array $options= [],OfferSearchResult $offerSearchResult = null, int $defaultHitsPerPage = 27): FormInterface
    {
        $builder = $this->formFactoryInterface->createBuilder(
            SearchForm::class,
            $options,
            [
                'csrf_protection' => false,
                'default_hits_per_page' => $defaultHitsPerPage,
                'allow_extra_fields' => true,
            ]
        );

        if($offerSearchResult && $offerSearchResult->getNbHits() > 0) {
            $builder->add(
                'facetFilters',
                FacetFilterType::class,
                [
                    'facets' => $offerSearchResult->getFacets(),
                ]
            );
        }

        return $builder->getForm();
    }
}
