<?php
/**
 * Created by PhpStorm.
 * User: QAR14123
 * Date: 31/07/2018
 * Time: 09:34
 */

namespace Open\FrontBundle\Form;


use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\HiddenType;
use Symfony\Component\Form\Extension\Core\Type\SubmitType;
use Symfony\Component\Form\Extension\Core\Type\TextareaType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

class IllegalContentForm extends AbstractType
{

    /**
     * @param FormBuilderInterface $builder
     * @param $options
     */
    public function buildForm(FormBuilderInterface $builder, array $options)
    {
        $builder
            ->add(
                'content',
                TextareaType::class,

                array(
                    'label' => 'ticket.common.message',
                    "attr" => array (
                        "class" => "full_width",
                       "required" => "required"
                    ),
                )
            )->add(
                'url',
                HiddenType::class
            )->add(
                'submit',
                SubmitType::class,
                array(
                    "attr" => array (
                        "value" => "submit",
                        'class' => "Button"
                    ),
                    'label' => 'illegal_content.form.save'
                )
            );
    }

    public function configureOptions(OptionsResolver $resolver)
    {
        $resolver->setDefaults([
            'translation_domain' => 'AppBundle',
            'validation_groups' => array('Default')
        ]);
    }
}