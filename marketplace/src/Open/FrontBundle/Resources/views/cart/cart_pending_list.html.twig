{% extends '@OpenFront/base.html.twig' %}
{% trans_default_domain 'AppBundle' %}
{% set pageClass = 'Page--cart_list' %}

{% block title %} {{ 'cart.detail' | trans }}{% endblock %}

{% block stylesheets %}
{% endblock %}

{% block body %}

    {% block searchbar %}
        {% if formSearchBar is defined %}
            {% include('@OpenFront/component/search-bar.html.twig') %}
        {% else %}
            {{ render(controller("Open\\FrontBundle\\Controller\\SearchBarController::formAction")) }}
        {% endif %}
    {% endblock %}

    <div class="Page-cart-inner">
        <div>
            <p>carts</p>
            <table class="table table-hover">
                <tbody>
                {% for cart in carts %}
                    <tr>
                        <td>{{ cart.id }}</td>
                        <td>{{ cart.status }}</td>
                        <td><a href="{{ path('front.cart.validation', {cartId: cart.id}) }}">see cart to validate</a></td>
                    </tr>
                {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
{% endblock %}

{% block javascripts %}
{% endblock %}