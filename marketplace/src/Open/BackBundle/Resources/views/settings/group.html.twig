{% extends '@OpenBack/base.html.twig' %}
{% trans_default_domain 'AppBundle' %}
{% block body %}
    {{ parent() }}

    <div class="row">
        <div class="col-md-12">
            <div class="card">
                    {% set group_title = 'system.settings.' ~ domain ~ '.' ~ group ~ '.title' %}
                <h3 class="card-header">{{ group_title|trans }}</h3>
                <div class="card-body">
                    {{ form_start(form, {'attr': {'class': 'card-text'}}) }}
                    {% form_theme form 'bootstrap_4_layout.html.twig' %}
                    {{ form_widget(form) }}
                    {{ form_end(form) }}
                </div>
            </div>
        </div>
    </div>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const $form = jQuery('#{{ form.vars.id }}').parent('form');
            $form.validate();
        });
    </script>

{% endblock %}