<?php
/**
 * Created by PhpStorm.
 * User: CMA15450
 * Date: 17/07/2017
 * Time: 15:30
 */

namespace AppBundle\Util;


use Symfony\Component\Security\Core\Role\Role;
use Symfony\Component\Security\Core\Role\RoleHierarchyInterface;

class RoleService
{
    private $roleHierarchy;

    /**
     * Constructor
     *
     * @param RoleHierarchyInterface $roleHierarchy
     */
    public function __construct(RoleHierarchyInterface $roleHierarchy)
    {
        $this->roleHierarchy = $roleHierarchy;
    }

    /**
     * isGranted
     *
     * @param string $role
     * @param $user
     * @return bool
     */
    public function isGranted(string $role, $user): bool
    {
        if (in_array($role, $this->roleHierarchy->getReachableRoleNames($user->getRoles()))) {
            return true;
        }
        return false;
    }
}