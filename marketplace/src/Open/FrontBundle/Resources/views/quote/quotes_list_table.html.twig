{% trans_default_domain 'AppBundle' %}

{% set suffix = (suffix is defined and suffix is not empty) ? suffix : 'desktop' %}
{% set quotesStatus = (quotesStatus is defined and quotesStatus is not empty) ? quotesStatus : 'buyer_draft' %}

<table id="all-quotes-{{ suffix }}"
       class="table quotes-table"
       data-link="{{ path('front.quote.detail', {'id':'quoteId'}) }}"
>
    <thead>
    <tr>
        <th>{{ 'quote.list.block.quote_number' | trans }}</th>
        <th>{{ 'quote.list.block.supplier' | trans }}</th>

        {% if quotesStatus == 'buyer_draft' %}
            <th>{{ 'quote.list.block.application_date' | trans }}</th>
        {% endif %}

        {% if quotesStatus == 'buyer_cancelled' %}
            <th>{{ 'quote.list.block.cancelled_date' | trans }}</th>
        {% endif %}

        {% if quotesStatus == 'validated' %}
            <th>{{ 'quote.list.block.validation_date' | trans }}</th>
        {% endif %}

        <th>{{ 'quote.list.block.quote_subject' | trans }}</th>
        <th>{{ 'quote.list.block.quote_status' | trans }}</th>
        <th></th>
    </tr>
    </thead>
    <tbody>
    {# Datatable ajax data #}
    </tbody>
</table>

<div class="quotes-list-empty hide">
    <h6 style="text-align: center">{{ 'quote.list.empty' | trans }}</h6>
</div>
