<?php


namespace Open\BackBundle\Form;


use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\NumberType;
use Symfony\Component\Form\Extension\Core\Type\SubmitType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

class InvoiceEntityThresholdFilter extends AbstractType
{
    private const STATUS_CHOICES = [
        'back.invoice_entity.threshold.status.all' => null,
        'back.invoice_entity.threshold.status.enabled' => 1 ,
        'back.invoice_entity.threshold.status.disabled' => 0,
    ];

    public function buildForm(FormBuilderInterface $builder, array $options)
    {
        $builder
            ->add('name', TextType::class, [
                'attr' => [
                    'class' => 'form-control form-control-sm col-6',
                ],
                'required' => false,
            ])
            ->add('validationThreshold', NumberType::class, [
                'attr' => [
                    'class' => 'form-control form-control-sm',
                ],
                'required' => false,
            ])
            ->add('managerValidation', ChoiceType::class, [
                'attr' => [
                    'class' => 'custom-select custom-select-sm',
                ],
                'choices' => self::STATUS_CHOICES,
                'required' => false,
            ])
            ->add('submit', SubmitType::class, [
                'attr' => [
                    'class' => 'd-none',
                ],
            ]);
    }

    public function configureOptions(OptionsResolver $resolver)
    {
        $resolver->setDefaults([
            'method' => 'GET',
            'translation_domain' => 'AppBundle',
        ]);
    }
}
