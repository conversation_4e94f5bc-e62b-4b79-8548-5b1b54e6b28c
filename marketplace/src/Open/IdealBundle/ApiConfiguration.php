<?php

namespace Open\IdealBundle;

class ApiConfiguration
{
    private string $scheme;
    private string $domain;
    private string $clientId;
    private string $clientSecret;
    private int $timeout;
    private int $nbUserByCall;
    private string $apimKey;

    public function __construct(string $scheme,
                                string $domain,
                                string $clientId,
                                string $clientSecret,
                                string $apimkey,
                                int $timeout,
                                int $nbUserByCall)
    {
        $this->domain = $domain;
        $this->scheme = $scheme;
        $this->clientId = $clientId;
        $this->clientSecret = $clientSecret;
        $this->timeout = $timeout;
        $this->nbUserByCall = $nbUserByCall;
        $this->apimKey = $apimkey;
    }

    public function getUrl()
    {
        return sprintf("%s://%s", $this->getScheme(), $this->getDomain());
    }

    /**
     * @return string
     */
    public function getScheme(): string
    {
        return $this->scheme;
    }

    /**
     * @return string
     */
    public function getDomain(): string
    {
        return $this->domain;
    }

    /**
     * @return string
     */
    public function getClientId(): string
    {
        return $this->clientId;
    }

    /**
     * @return string
     */
    public function getClientSecret(): string
    {
        return $this->clientSecret;
    }

    /**
     * @return int
     */
    public function getTimeout(): int
    {
        return $this->timeout;
    }

    /**
     * @return int
     */
    public function getNbUserByCall(): int
    {
        return $this->nbUserByCall;
    }

    /**
     * @return string
     */
    public function getApimKey(): string
    {
        return $this->apimKey;
    }

    /**
     * @param string $apimKey
     * @return ApiConfiguration
     */
    public function setApimKey(string $apimKey): ApiConfiguration
    {
        $this->apimKey = $apimKey;
        return $this;
    }


}
