<?php

namespace AppBundle\Services;

use AppBundle\Entity\ActiveBuyer;
use AppBundle\Entity\Country;
use AppBundle\Entity\DistantUser;
use AppBundle\Exception\ImportBuyerException;
use AppBundle\Repository\ActiveBuyerRepository;
use AppBundle\Repository\CountryRepository;
use AppBundle\Repository\DistantUserRepository;
use DateTime;
use DateTimeInterface;
use Doctrine\ORM\EntityManager;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\ORMException;
use Generator;
use AppBundle\Entity\BuyerAddress;
use AppBundle\Entity\MetaCart;
use AppBundle\Entity\User;
use AppBundle\Entity\TotalAddress;
use AppBundle\Model\BuyerImportationDataObject;
use AppBundle\Repository\MetaCartRepository;
use League\Csv\Reader;
use Open\LogBundle\Service\LogService;
use Open\LogBundle\Utils\EventNameEnum;

class BuyerImportationService
{
    private Generator $data;
    private BuyerService $buyerService;
    private InvoiceEntityService $invoiceEntityService;
    private SiteService $siteService;
    private MarketPlaceService $marketPlaceService;
    private UserEnablerService $userEnablerService;
    private AddressService $addressService;
    private CountryService $countryService;
    private LogService $logService;
    private EntityManagerInterface $em;
    private MetaCartRepository $metaCartRepository;
    private CountryRepository $countryRepository;
    private DistantUserRepository $distantUserRepository;

    public function __construct(
        BuyerService           $buyerService,
        InvoiceEntityService   $invoiceEntityService,
        SiteService            $siteService,
        UserEnablerService     $userEnablerService,
        AddressService         $addressService,
        CountryService         $countryService,
        MarketPlaceService     $marketPlaceService,
        LogService             $logService,
        EntityManagerInterface $entityManager,
        CountryRepository      $countryRepository,
        MetaCartRepository     $metaCartRepository,
        DistantUserRepository  $distantUserRepository
    )
    {
        $this->buyerService = $buyerService;
        $this->invoiceEntityService = $invoiceEntityService;
        $this->siteService = $siteService;
        $this->userEnablerService = $userEnablerService;
        $this->addressService = $addressService;
        $this->countryService = $countryService;
        $this->marketPlaceService = $marketPlaceService;
        $this->logService = $logService;
        $this->em = $entityManager;
        $this->metaCartRepository = $metaCartRepository;
        $this->countryRepository = $countryRepository;
        $this->distantUserRepository = $distantUserRepository;
    }

    #############################################
    ###  Utility methods used to import buyer
    #############################################

    public function importFromDistantUSer(): int
    {

        // Pass 1 : Record all Buyers
        $startProcessDate = $startPacketDate = new DateTime("now");

        $index = 1;

        gc_enable();
        $distantUsers = $this->distantUserRepository->fetchAll();
        foreach ($distantUsers as $distantUser) {
            try {
                $buyerImportationObject = $this->mapUserData($distantUser);
                $this->importBuyer($buyerImportationObject);
                $index++;

                // flush and trace stat every 100 users
                if ($index % 100 == 0) {
                    $this->flush();
                    $this->logStatTrace($startProcessDate, $startPacketDate, $index, "import buyer index");
                    $startPacketDate = new Datetime("now");
                }
            } catch (ImportBuyerException $buyerException) {
                $this->logService->error("Problem during import buyer", "IMPORT_BUYER", $buyerException->getUserContext() ?? []);
            }
        }
        // save for objects after last flush (few last object)
        $this->flush();

        // Pass 2 : Record all buyers superiors
        $startProcessDate = $startPacketDate = new DateTime("now");
        $this->logService->info("start hierarchy process", EventNameEnum::COMMAND_IMPORT_BUYER);
        $index2 = 1;
        $distantUsers = $this->distantUserRepository->fetchAll();
        foreach ($distantUsers as $distantUser) {
            try {
                $buyerImportationObject = $this->mapUserData($distantUser);
                $this->importSuperiors($buyerImportationObject);
                $index2++;

                // flush and trace stat every 100 users
                if ($index2 % 100 == 0) {
                    $this->flush();
                    $this->logStatTrace($startProcessDate, $startPacketDate, $index2, "import superiors index");
                    $startPacketDate = new Datetime("now");
                }
            } catch (ImportBuyerException $buyerException) {
                $this->logService->error("Problem during import superiors", "IMPORT_BUYER", $buyerException->getUserContext() ?? []);
            }
        }
        // save for objects after last flush (few last object)
        $this->flush();

        // Disable buyers that are not in ideal anymore
        //$this->buyerService->disableNotActiveBuyers();
        return $index;
    }

    public function importBuyer(BuyerImportationDataObject $buyerImportationDataObject): void
    {
        try {
            // 1 - validate buyer properties
            if (!$this->isValidBuyer($buyerImportationDataObject)) {
                return;
            }

            // 2 - get or create a buyer instance in/from database
            $user = $this->createOrGetBuyerInstance($buyerImportationDataObject);
            if (!$user) {
                return;
            }

            // 3 - set buyer invoice entity
            $invoiceEntity = $this->invoiceEntityService->createInvoiceEntity($buyerImportationDataObject->getInvoiceEntityName(), $buyerImportationDataObject->getEntityCountryCode());
            $user->setInvoiceEntity($invoiceEntity);
            $user->setOrganization($buyerImportationDataObject->getOrganisationId());

            // 4 - set buyer site
            $site = $this->siteService->createSite($buyerImportationDataObject->getSiteName());
            $user->setSite($site);

            // 5 - set user enabler
            $userEnabler = $this->userEnablerService->createEnabler($invoiceEntity, $site, $user->getUserType());

            $this->logService->info(
                "Import buyer : create enabler",
                'CREATE_USER_ENABLER',
                $user->getUsername(), [
                    'ID' => $user->getId(),
                    'SITE_NAME' => $site->getName(),
                    'ENABLER_ID' => $userEnabler->getId(),
                    'USER_ENABLED' => $user->isEnabled(),
                    'ENABLER_ENABLED' => $userEnabler->getEnabled()
                ]
            );

            $this->userEnablerService->attachBuyerToEnabler($user, $userEnabler);


            // 6 - set buyer total address
            $this->createOrGetDefaultBuyerAddressFromTotalAddress(
                $this->createBuyerTotalAddress($buyerImportationDataObject),
                $user
            );

            // 7 - set buyer marketplace
            $marketPlace = $this->marketPlaceService->getMarketPlace($buyerImportationDataObject->getEntityCountryCode());
            if (is_null($marketPlace)) {
                $marketPlace = $this->marketPlaceService->getMarketPlace(MarketPlaceService::DEFAULT_MARKETPLACE);
            }
            $user->setMarketPlace($marketPlace);

            // 7.5 - Ensure EntityManager is open before persisting
            $this->ensureEntityManagerIsOpen();

            // 8 - persist user
            $this->em->persist($user);

        } catch (\Throwable $ex) {
            $this->logImportErrorException($buyerImportationDataObject, $ex, "import buyer error");

            echo "import buyer error: " . $ex->getMessage(). '. IGG: ' . $buyerImportationDataObject->getPersonId() . '. Email: ' . $buyerImportationDataObject->getEmailAddress() . "\n";

            //Reset the default em
            $this->ensureEntityManagerIsOpen();
        }
    }

    public function importSuperiors(BuyerImportationDataObject $buyerImportationDataObject): void
    {
        try {
            /** @var User $buyer */
            $buyer = $this->buyerService->getBuyerFromUsername($buyerImportationDataObject->getPersonID());

            /** @var User $superior */
            $superior = $this->buyerService->getBuyerFromUsername($buyerImportationDataObject->getManagerId());

            $this->logImportSuperiorsInfo($buyerImportationDataObject, "superior import");

            if ($buyer) {
                if (is_null($buyerImportationDataObject->getManagerId()) || strlen(trim($buyerImportationDataObject->getManagerId())) == 0) {
                    $deleted = $this->buyerService->deleteManagerRelation($buyer->getId());
                    if ($deleted) {
                        $this->logService->info(
                            "manager relation deleted",
                            EventNameEnum::COMMAND_IMPORT_BUYER,
                            null, ['buyer_igg' => $buyerImportationDataObject->getPersonId()
                        ]);
                    }
                    return;
                }
                if ($superior) {
                    $this->buyerService->attachBuyerToSuperior($buyer, $superior, false);
                } else {
                    $this->logImportSuperiorsError($buyerImportationDataObject, 'import superior : Unknown superior');
                }
            } else {
                $this->logImportSuperiorsError($buyerImportationDataObject, 'import superior : Unknown User');
            }
        } catch (\Exception $ex) {
            $this->logImportErrorException($buyerImportationDataObject, $ex, "import superior error");

            //Reset the default em
            $this->ensureEntityManagerIsOpen();
        }
    }


    ##################################################
    ###  Utility methods used to log information
    ##################################################
    private function logStatTrace(DateTime $startProcessDate, DateTime $startPacketDate, int $index, string $message)
    {
        $time_interval_packet = $startPacketDate->diff(new Datetime("now"));
        $time_interval_all = $startProcessDate->diff(new Datetime("now"));
        $packet_time_info = $time_interval_packet->format('%i min %s sec');
        $this->logService->info($message, EventNameEnum::COMMAND_IMPORT_BUYER, null, [
            "index" => $index,
            "packet time" => $packet_time_info,
            "all_time" => $time_interval_all->format('%i min %s sec')
        ]);
        echo sprintf("%s - item --> %s in %s\n", $message, $index, $packet_time_info);
    }

    private function logImportErrorException(BuyerImportationDataObject $buyerImportationDataObject, \Throwable $exception, string $message)
    {
        $this->logService->error($message, EventNameEnum::COMMAND_IMPORT_BUYER, null, [
            "user_igg" => $buyerImportationDataObject->getPersonId(),
            "email" => $buyerImportationDataObject->getEmailAddress(),
            "errorMessage" => $exception->getMessage(),
            "trace" => $exception->getTrace()
        ]);
    }

    private function logImportSuperiorsInfo(BuyerImportationDataObject $buyerImportationDataObject, string $message)
    {
        $this->logService->info($message, EventNameEnum::COMMAND_IMPORT_BUYER, null, [
            'buyer_igg' => $buyerImportationDataObject->getPersonId(),
            'superior_igg' => $buyerImportationDataObject->getManagerId()
        ]);
    }

    private function logImportSuperiorsError(BuyerImportationDataObject $buyerImportationDataObject, string $message)
    {
        $this->logService->info($message, EventNameEnum::COMMAND_IMPORT_BUYER, null, [
            'buyer_igg' => $buyerImportationDataObject->getPersonId(),
            'superior_igg' => $buyerImportationDataObject->getManagerId()
        ]);
    }


    /**
     * @throws ImportBuyerException
     */
    private function mapUserData(DistantUser $distantUser): BuyerImportationDataObject
    {
        if ($distantUser->getPreferredLanguage() === null) {
            throw new ImportBuyerException("Preferred language is null", ["IGG" => $distantUser->getIgg(), "email" => $distantUser->getEmail()]);
        }
        if ($distantUser->getOrganization() === null) {
            throw new ImportBuyerException("Organization is null", ["IGG" => $distantUser->getIgg(), "email" => $distantUser->getEmail()]);
        }
        return (new BuyerImportationDataObject())
            ->setPersonID($distantUser->getIgg())
            ->setFirstName($distantUser->getFirstName())
            ->setLastName($distantUser->getLastName())
            ->setPreferredLanguage($distantUser->getPreferredLanguage())
            ->setEmailAddress($distantUser->getEmail() ?? "")
            ->setTelephone($distantUser->getTelephone() ?? "")
            ->setOrganisationId($distantUser->getOrganization())
            ->setManagerId($distantUser->getManagerIgg() ?? "")
            ->setInvoiceEntityName($distantUser->getInvoicingEntityName())
            ->setSiteName($distantUser->getSiteName())
            ->setSiteStreet($distantUser->getSiteStreet())
            ->setSiteStreet2($distantUser->getSiteStreet2())
            ->setSiteZipcode($distantUser->getSiteZipcode())
            ->setSiteLocality($distantUser->getSiteLocality())
            ->setSiteCountry($distantUser->getSiteCountry())
            ->setSiteCountryCode($distantUser->getSiteCountryCode())
            ->setStartDate($this->parseDateIso($distantUser->getStartDate()))
            ->setEndDate($this->parseDateIso($distantUser->getEndDate()))
            ->setCostCenter($distantUser->getCostCenter())
            ->setEntityCountry($distantUser->getInvoicingEntityCountry())
            ->setEntityCountryCode($distantUser->getInvoicingEntityCountryCode());

    }

    #############################################
    ###  Utility methods used to manage buyer
    #############################################
    public function disableSqlLog()
    {
        $connection = $this->em->getConnection();
        $connection->getConfiguration()->setSQLLogger(null);
    }

    private function flush()
    {
        // flush and clear
        $this->em->flush();
        $this->em->clear();

        // call garbage collector to force clearing doctrine objects
        gc_collect_cycles();

        // We ensure the flush didn't close the em.
        $this->ensureEntityManagerIsOpen();
    }

    private function parseDate($dateStr)
    {
        $date = DateTime::createFromFormat('j/m/Y', $dateStr);
        if (!$date) {
            return null;
        }
        return $date;
    }

    private function parseDateIso(?string $dateStr)
    {
        if ($dateStr == null) {
            return new DateTime("+5 years");
        }
        $date = DateTime::createFromFormat('Y-m-d\TH:i:s.uZ', $dateStr);
        if (!$date) {
            return null;
        }
        return $date;
    }

    private function isEmpty(?string $str)
    {
        return (is_null($str) || strlen($str) == 0);
    }

    private function checkMandatory(BuyerImportationDataObject $userObj)
    {

        if ($this->isEmpty($userObj->getEmailAddress()) ||
            $this->isEmpty($userObj->getPersonId()) ||
            $this->isEmpty($userObj->getLastName()) ||
            $this->isEmpty($userObj->getFirstName())) {

            return false;
        }

        return true;

    }

    private function isNewAddress(User $user, BuyerAddress $address)
    {
        if ($user->getDefaultBuyerAddress()) {
            return $user->getDefaultBuyerAddress()->getId() != $address->getId();
        }

        return true;
    }

    private function setCurrentMetaCartShippingAddress(User $user, BuyerAddress $buyerAddress)
    {
        $metaCart = $this->metaCartRepository->findBuyerCurrentMetaCart($user);
        if ($metaCart && ($metaCart->getStatus() == MetaCart::STATUS_CREATED || $metaCart->getStatus() == MetaCart::STATUS_EMPTIED)) {
            $oldShipping = $metaCart->getBuyerShippingAddress();
            if ($oldShipping && $oldShipping->getType() == BuyerAddress::TYPE_TOTAL) {
                $this->logService->info("updating metacart shipping address", EventNameEnum::COMMAND_IMPORT_BUYER, null, ["user_igg" => $user->getUsername()]);

                $metaCart->setBuyerShippingAddress($buyerAddress);
                $this->em->persist($metaCart);
            }
        }

    }

    private function isValidBuyer(BuyerImportationDataObject $buyerImportationDataObject): bool
    {
        $dateNow = new Datetime("now");
        if ($buyerImportationDataObject->getStartDate() > $dateNow) {
            $this->logService->info(
                "User skipped, start date in the future",
                EventNameEnum::COMMAND_IMPORT_BUYER,
                null, ['igg' => $buyerImportationDataObject->getPersonId(),
                'email' => $buyerImportationDataObject->getEmailAddress()]);
            return false;
        }

        $this->logService->info(
            "start user import",
            EventNameEnum::COMMAND_IMPORT_BUYER,
            null, ['igg' => $buyerImportationDataObject->getPersonId(),
            'email' => $buyerImportationDataObject->getEmailAddress()]);

        if (!$this->checkMandatory($buyerImportationDataObject)) {
            $this->logService->error("import buyer error mandatory",
                EventNameEnum::COMMAND_IMPORT_BUYER,
                null,
                ["user_igg" => $buyerImportationDataObject->getPersonId(),
                    "email" => $buyerImportationDataObject->getEmailAddress()]
            );
            return false;
        }

        return true;
    }

    private function getCountryOfdelivery(string $totalCountryCode): Country
    {

        $countries = $this->countryRepository->findAvailableCountryOfDelivery();
        $countryCodes = array_map(function (Country $country) {
            return $country->getIzbergCode();
        }, $countries);


        $countryCodeIzb = $this->countryService->getIzbergCountryCode($totalCountryCode);
        if (!in_array($countryCodeIzb, $countryCodes)) {
            $countryCodeIzb = 'FR';
        }
        // get country code
        return $this->countryService->getCountryByIzbergCode($countryCodeIzb);


    }

    private function createOrGetBuyerInstance(BuyerImportationDataObject $buyerImportationDataObject): ?User
    {
        $country = $this->getCountryOfdelivery($buyerImportationDataObject->getEntityCountryCode());

        return $this->buyerService->createOrUpdateBuyer(
            $buyerImportationDataObject->getEmailAddress(),
            'P@ssw0rd',
            $buyerImportationDataObject->getFirstName(),
            $buyerImportationDataObject->getLastName(),
            $buyerImportationDataObject->getOrganisationId(),
            $buyerImportationDataObject->getCostCenter(),
            $buyerImportationDataObject->getPersonId(),
            $buyerImportationDataObject->getTelephone(),
            (new DateTime('now')) > $buyerImportationDataObject->getEndDate(),
            $country
        );
    }

    private function createBuyerTotalAddress(BuyerImportationDataObject $buyerImportationDataObject): TotalAddress
    {

        $countryCodeIzb = $this->countryService->getIzbergCountryCode($buyerImportationDataObject->getSiteCountryCode());
        // get country code
        $country = $this->countryService->getCountryByIzbergCode($countryCodeIzb);
        // create total address instance
        $totalAddress = new TotalAddress();
        $totalAddress
            ->setName($buyerImportationDataObject->getSiteName())
            ->setAddress($buyerImportationDataObject->getSiteStreet())
            ->setAddress2($buyerImportationDataObject->getSiteStreet2())
            ->setZipCode($buyerImportationDataObject->getSiteZipcode())
            ->setCity($buyerImportationDataObject->getSiteLocality())
            ->setCountry($country);
        // create Total Address if not exists in bdd
        $totalAddress = $this->addressService->createTotalAddressIfNotExists($totalAddress);
        // logs
        $this->logService->info(
            sprintf(
                'TOTAL Address named %s was created if not exists: %s %s, %s %s - %s',
                $totalAddress->getName(),
                $totalAddress->getAddress(),
                $totalAddress->getAddress2(),
                $totalAddress->getZipCode(),
                $totalAddress->getCity(),
                ($totalAddress->getCountry() ? $totalAddress->getCountry()->getCode() : '')
            ),
            self::class . '::importBuyer'
        );
        // return address
        return $totalAddress;
    }

    private function createOrGetDefaultBuyerAddressFromTotalAddress(TotalAddress $totalAddress, User $user)
    {
        $totalAddressModel = $this->addressService->fromTotalAddressEntityToModel($totalAddress);
        $defaultBuyerAddress = $this->addressService->getBuyerAddressAndCreateItIfNotExists($user, BuyerAddress::TYPE_TOTAL, $totalAddressModel, true);
        if ($defaultBuyerAddress && $this->isNewAddress($user, $defaultBuyerAddress)) {
            $user->setDefaultBuyerAddress($defaultBuyerAddress);
            $this->setCurrentMetaCartShippingAddress($user, $defaultBuyerAddress);
        }
    }

    /**
     * Ensures the EntityManager is open before performing operations
     * If closed, recreates it using the same connection and configuration
     */
    private function ensureEntityManagerIsOpen(): void
    {
        if (!$this->em->isOpen()) {
            $this->logService->info("EntityManager is closed, recreating it", EventNameEnum::COMMAND_IMPORT_BUYER);
            $this->em = EntityManager::create($this->em->getConnection(), $this->em->getConfiguration());
        }
    }
}
