@import "../variables";
@import "~breakpoint-sass/stylesheets/breakpoint";

.Header {
    position: fixed;
    left: 0;
    top: 0;
    width: 100%;
    background-color: $WHITE;
    min-height: $NAV_USER_HEIGHT;
    display: block !important;
    justify-content: center;
    align-items: center;
    z-index: 3;

    .dropdown-label {
        font-size: 16px;
        color: $TOTAL_DARK_GREY2;
        padding: 0 10px;
        white-space: nowrap;

        @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
            color: $WHITE;
            font-weight: normal;
            font-size: 14px;
        }
    }

    .include-search-bar {
        display: none;
    }

    .v-line {
        background-color: $TOTAL_RED;
        height: 10px;
        width: 1px;
    }

    .header-link {
        font-weight: bold;
        font-size: 20px;
    }

    .header-container {
        width: 90%;
        display: flex;
        max-width: 1400px;
        height: 70px;
        justify-content: space-between;
        margin: auto;
        align-items: flex-end;
        background-color: $WHITE;

        @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
            height: auto;

            .logo--home-register {
                position: absolute;
                top: 0;
                left: 0;
                margin-left: 0;
                height: auto !important;
            }
        }

        .logo--home {
            display: flex;
            align-items: baseline;

            &-register {
                height: 50px !important;

                img {
                    width: 125%;
                }
            }
        }

        .dropdown-display {
            @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
                margin-top: 0 !important;
            }
        }

        .logo--home {
            text-align: center;

            img {
                width: 200px;
                @include breakpoint($BREAKPOINT_DESKTOP) {
                    margin-top: 20px;
                }
            }
        }

        @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
            width: 100%;
            flex-direction: row-reverse;
        }
    }

    .Header-lang {
        display: none;

        button {
            margin-top: 0;
        }

        ul {
            display: inline;
            height: 35px;
        }

        ul,
        li {
            padding-left: 0;
            list-style-type: none;
        }

        li {
            float: left;
        }

        button {
            color: $PRIMARY_COLOR;

            &:hover {
                color: $APPLI_DARK_YELLOW;
            }
        }

        li.is-active {
            button {
                pointer-events: none;
                font-weight: bold;
            }
        }

        .Header-langSeparator {
            position: relative;

            span {
                position: absolute;
                width: 3px;
                height: 15px;
                background: $PRIMARY_COLOR;
                top: 10px;
                left: -3px;
            }
        }
    }

    .title-home {
        color: $TOTAL_DARK_GREY3;
        font-size: 18px;
        width: 75%;
        text-align: center;
        padding-top: 20px;
        display: flex;
        align-items: center;
        justify-content: center;

        span {
            font-weight: bold;
            padding: 0 4px 0 4px;
        }

        span {
            font-weight: bold;
        }

        @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
            display: none;
        }
    }

    [class^="lang-item-"] {
        width: 45px;
        color: $TOTAL_DARK_PURPLE;
        font-weight: bold;
        font-size: 1rem;
    }

    .lang-item- {
        &fr:before {
            content: "Fr";
        }

        &en:before {
            content: "En";
        }

        &fr:after {
            content: "|";
        }

        &en:after {
            content: "|";
        }
    }

    #cart-1 .hide {
        // Hide cart item bullet when it's empty (desktop and mobile mode together)
        display: none !important;
    }

    .Header-hamburgerIcon {
        button {
            margin-top: 0;
        }

        .Header-lang {
            display: block;
        }
    }

    .Header-loginLink {
        font-size: 15px;
        font-weight: bold;

        a {
            text-transform: uppercase;
            color: $PRIMARY_COLOR;
        }
    }

    button {
        background: transparent;
        border-radius: 0;
        width: 32px;
        height: 32px;
        border: 0;
        padding: 0;

        svg {
        }
    }

    .logo--desktop {
        display: none;
        margin-right: 40px;

        img {
            max-width: 100%;
        }

        &:hover a:after {
            display: none;
        }
    }

    .header-user-link {
        display: inline-flex !important;
    }

    .Header-menu {
        position: fixed;
        overflow: auto;
        top: 0;
        bottom: 0;
        max-width: 600px;
        height: 100%;
        transform: translateX(-110%);
        opacity: 0;
        z-index: 1;

        @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
            transform: none;
            opacity: 1;
            background-color: $WHITE !important;
            box-shadow: none;
            height: 95px;
            max-width: none;

            .Header-menu-row {
                display: flex;
                flex-direction: row;
                justify-content: space-between;

                .Header-menu-col {
                    width: 50%;

                    ul {
                        list-style-type: none;
                    }
                }
            }
        }

        -webkit-box-shadow: 4px 4px 48px -11px rgba(0, 0, 0, 0.53);
        -moz-box-shadow: 4px 4px 48px -11px rgba(0, 0, 0, 0.53);
        box-shadow: 4px 4px 48px -11px rgba(0, 0, 0, 0.53);

        transition: transform 60ms ease-out;

        &.is-active {
            transform: translateX(-10px);
            opacity: 1;
            transition: transform 60ms ease-in, opacity 10ms linear;
        }

        &.js-panel-container {
            text-align: left;
            background-color: transparent;

            &.user-header {
                text-align: right;
                margin-right: 0 !important;

                .v-line {
                    margin-bottom: 2px !important;
                }
            }
        }
    }

    .Header-menu-search {
        display: block;
        transform: translateX(150%);
        right: 0;

        &.is-active {
            transform: translateX(0px);
        }

        @include breakpoint($BREAKPOINT_DESKTOP) {
            display: none;
        }
    }

    .Header-menuItems {
        list-style-type: none;
        padding-left: 0;
        margin: 20px 40px 0 40px;

        .mobile-only {
            margin-top: 0 !important;
            display: flex;
            justify-content: space-between;
        }

        > .Menu-item {
            border-top: 1px solid transparent;
        }

        // add border to items stqrting from the third
        > .Menu-item:nth-child(1n + 4) {
            border-top: 1px solid $APPLI_DARK_YELLOW;
        }

        #id-user {
            display: inline-table;

            .Menu-item {
                width: auto;

                span {
                    padding-left: 5px;
                    color: $TOTAL_DARK_GREY3;
                    font-size: 14px;
                }
            }
        }
        a

        #cart-1 a {
            color: $TOTAL_RED;
        }

        .Menu-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            list-style-type: none;
            font-size: 1em;
            padding: 20px 3px;
            margin-right: 20px;

            @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
                flex-direction: column;
                margin-right: 5px;
                padding: 0;
                align-items: baseline;

                i {
                    float: right;
                    margin-top: -13px;
                }
            }

            a {
                color: $WHITE;
                text-decoration: none;
                font-weight: bold;
            }

            &:not(.has-submenu):hover {
                a {
                    color: $APPLI_GREY11;

                    .Icon {
                        fill: $APPLI_GREY11;
                    }
                }
            }

            .arrow {
                border-color: $TOTAL_GREY !important;

                &.up {
                    margin-top: 7px !important;
                }

                &.bottom {
                    margin-bottom: 7px !important;
                }
            }

            &.menu--signin {
                display: none;
            }

            &.is-active {
                .Submenu {
                    display: block;

                    .Submenu-items {
                        padding-left: 0;
                    }

                    .Menu-item {
                        font-size: 12px;
                        padding: 5px 10px 5px 5px;

                        a {
                            text-transform: none;
                        }
                    }
                }
            }

            &.menu--logout {
                display: block;

                span {
                    display: flex;

                    .Icon {
                        display: inline;
                        width: 24px;
                        height: 24px;
                        fill: $WHITE;
                        margin-right: 10px;
                        transform: translateY(2px);
                    }
                }
            }

            .Submenu .Submenu-items .Menu-item.menu--logout {
                display: none !important;
            }

            &.has-submenu > a:before {
                position: absolute;
                display: block;
                content: "X";
                color: transparent;
                background-image: url(/images/chevron-down.svg);
                background-repeat: no-repeat;
                background-size: contain;
                width: 24px;
                height: 24px;
                left: -35px;
                top: 2px;
                transform: rotate(180deg);
            }

            &.has-submenu {
                &.is-active,
                &:target {
                    > a:before {
                        transform: rotate(0);
                    }
                }
            }

            &.item--spacer {
                flex-grow: 1;
                display: none;
            }
        }
        #news{
            text-decoration: none;
            opacity: 0.7;
        }
    }

    .Menu-item.has-submenu:target {
        .Submenu {
            display: block;
        }
    }

    .Submenu {
        display: none;

        .Submenu-items {
            padding-left: 0;
        }

        .Menu-item {
            font-size: 12px;
            font-weight: normal;
            padding: 5px 0;

            a {
                text-transform: none;
            }

            &.is-active {
                a {
                    color: $APPLI_DARK_YELLOW;
                }
            }
        }
    }

    @include breakpoint($BREAKPOINT_DESKTOP) {
        @include breakpoint(max-width 1110px) {
            .first-level {
                display: none;
            }
        }
        display: flex;

        #hamburger-btn-li {
            margin-left: 0 !important;
            cursor: pointer;
            margin-bottom: 3px !important;
            height: 16px;
            width: 22px;
            display: flex;
            align-items: center;
            position: relative;
            transition: all 0.3s ease-in-out;

            &::before,
            &::after {
                display: block;
                content: "";
                position: absolute;
                right: 0;
                left: 0;
                height: 2px;
                background: $TOTAL_RED;
                transition: all 0.3s ease-in-out;
                border-radius: 2px;
            }

            &::before {
                top: 0;
            }

            &::after {
                bottom: 0;
            }

            span {
                display: block;
                height: 2px;
                background: $TOTAL_RED;
                width: 100%;
                transition: all 0.3s ease-in-out;
                opacity: 1;
                transform: scale(1);
                border-radius: 2px;
            }

            &.open {
                &::before {
                    transform: rotateZ(45deg);
                    top: 7px;
                }

                &::after {
                    transform: rotateZ(-45deg);
                    bottom: 7px;
                }

                span {
                    opacity: 0;
                    transform: scale(0);
                }
            }
        }

        #js-hamburger-button {
            padding: 0 !important;
            height: 32px !important;

            &:hover {
                border: none;
                color: $TOTAL_RED;
            }
        }

        .header-container.connected {
            border-bottom: 1px solid $TOTAL_RED;

            .logo--home {
                @include breakpoint($BREAKPOINT_DESKTOP) {
                    position: absolute;
                    left: calc(50% - 100px) !important;
                    z-index: 2 !important;
                    top: 30px;
                    display: flex;
                    height: 60px;
                    width: 200px;
                    background-color: $WHITE;
                    align-items: center;
                    justify-content: space-around;
                }
            }

            .dropdown.menu-account {
                min-width: 130px;
                margin-left: 0 !important;
            }

            .header-country-select,
            .header-language-select {
                min-width: 120px;
            }

            .Header-hamburgerIcon {
                display: none;
            }
        }

        .logo--desktop {
            display: block;
        }

        &-loginLink,
        &-closeMenu {
            display: none !important;
            pointer-events: none !important;
        }

        .Header-lang {
            margin-left: 40px;
            display: block;
        }

        .Header-menu {
            position: relative;
            overflow: visible;
            transform: translateX(0);
            box-shadow: none;
            opacity: 1;
            margin-right: 20px;
        }

        .Header-menuItems {
            display: inline-flex;
            align-items: center;
            margin: 0;
            height: 100%;

            li {
                display: inline;
            }

            .Item-cart {
                .quantity {
                    display: flex;
                    align-items: center;
                    position: absolute;
                    top: -1.5em;
                    right: 0;
                    min-width: 1.8em;
                    height: 1.8em;
                    padding: 0.2rem;
                    font-size: 0.9em;
                    background-color: $PRIMARY_COLOR;
                    -webkit-box-shadow: 0px 0px 8px $TOTAL_DARK_GREY;
                    -moz-box-shadow: 0px 0px 8px $TOTAL_DARK_GREY;
                    box-shadow: 0px 0px 8px $TOTAL_DARK_GREY;
                    color: $WHITE;
                    border-radius: 1em;

                    p {
                        text-align: center;
                        flex-grow: 1;
                        margin-bottom: 0;
                    }
                }
            }

            > li {
                margin: 0 7px;
            }

            > .Menu-item {
                color: $APPLI_GREY6;
                display: flex;
                align-items: center;

                &:hover {
                    background: transparent !important;

                    > a {
                        color: $APPLI_GREY6 !important;
                        transition: color 100ms linear;
                    }
                }
            }

            .Menu-item {
                position: relative;
                padding: 0;
                font-size: 12px;
                font-weight: normal;
                border-top: none !important;
                margin: 0 5px;

                a {
                    display: flex;
                    color: $TOTAL_GREY;
                    position: initial !important;
                }

                a:before {
                    display: none;
                }

                a:after {
                    display: none;
                }

                &.has-icon a {
                    display: flex;
                    font-weight: 300;
                    align-items: center;

                    span {
                        display: flex;
                    }

                    .arrow {
                        margin-top: 2px;
                    }
                }

                &:hover {
                    background: transparent;
                }

                &:hover,
                &.is-active {
                    a:after {
                        opacity: 1;
                        transition: opacity 80ms linear;
                    }
                }

                &.menu--signin {
                    display: flex; // remove display none from mobile

                    background: $APPLI_DARK_YELLOW;
                    border-radius: 20px;
                    padding: 5px 10px 5px 10px;
                    height: auto;

                    &:hover {
                        > a {
                            color: $APPLI_GREY11 !important;
                        }

                        background: $APPLI_DARK_YELLOW !important; // needed do not remove
                    }

                    a {
                        color: $WHITE !important;

                        &:after {
                            display: none;
                        }
                    }
                }

                &:hover {
                    .Submenu {
                        display: block !important;
                    }
                }

                &.menu--logout {
                    //display: none;
                    opacity: 0;
                    pointer-events: none;
                }

                &.is-active {
                    .Submenu {
                        display: none;
                    }
                }

                .Submenu {
                    .Submenu-items {
                        .Menu-item {
                            padding: 10px 20px;
                        }

                        .Menu-item.menu--logout {
                            display: block !important;
                            opacity: 1 !important;
                            pointer-events: all;

                            .Icon {
                                display: inline;
                                width: 16px;
                                height: 16px;
                                transform: translateY(0);
                                fill: $PRIMARY_COLOR_DARK;
                            }

                            &:hover {
                                .Icon {
                                    fill: $WHITE;
                                }
                            }
                        }
                    }
                }

                &.has-submenu {
                    padding-left: 5px;
                }

                &.item--spacer {
                    display: inline;
                }
            }
        }

        .Submenu {
            position: absolute;
            top: $NAV_USER_HEIGHT;
            background: $WHITE;
            left: 50%;
            transform: translateX(-50%);
            border: 1px solid $APPLI_GREY5;

            -webkit-box-shadow: 5px 5px 5px 0 rgba(0, 0, 0, 0.08);
            -moz-box-shadow: 5px 5px 5px 0 rgba(0, 0, 0, 0.08);
            box-shadow: 5px 5px 5px 0 rgba(0, 0, 0, 0.08);

            ul {
                padding-left: 0;
            }

            .Menu-item {
                text-align: center;
                padding: 10px 20px;
                border-top: 1px solid $APPLI_GREY7;
                cursor: pointer;
                white-space: nowrap;
                font-size: 12px;
                font-weight: normal;

                &:hover {
                    a {
                        color: $WHITE !important;
                        transition: none !important;
                    }

                    background: $PRIMARY_COLOR !important;
                }

                a {
                    text-transform: uppercase;
                    display: block;
                    color: $APPLI_GREY6 !important;
                    transition: none !important;

                    &:after {
                        display: none;
                    }
                }

                span {
                    position: relative;
                }

                .Icon {
                    display: inline;
                    position: absolute;
                    margin: 0 5px;
                    left: -5px;
                    width: 16px;
                    height: 16px;
                    transform: translate(0);
                    fill: $PRIMARY_COLOR;
                }

                &.has-icon {
                    background: $APPLI_GREY10;

                    a {
                        color: $PRIMARY_COLOR !important;
                    }

                    span {
                        padding-left: 20px;
                    }

                    &:hover {
                        .Icon {
                            fill: $WHITE;
                        }

                        a {
                            color: $WHITE !important;
                        }
                    }
                }

                &:first-of-type {
                    border-top: 0;
                }

                &.is-active {
                    background: $APPLI_GREY11;

                    a {
                        color: $WHITE !important;
                    }
                }
            }
        }
    }
    @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
        height: 75px;
        min-height: 0;

        .header-container {
            .logo--home {
                display: flex;
                height: 100%;
                align-items: center;

                img {
                    width: 200px;
                }

                &.mobile-only {
                    display: flex !important;
                }
            }

            &.connected {
                display: flex;
                flex-direction: column;
                justify-content: center;

                .logo--home {
                    @include breakpoint($BREAKPOINT_DESKTOP) {
                        position: absolute;
                        left: 50%;
                        transform: translateX(-50%);
                        margin-left: 0;
                        top: 5px;
                        display: flex;
                    }

                    img {
                        width: 180px;
                        height: 52px;
                    }
                }
            }
        }

        .Header-closeMenu {
            display: block;
            position: relative;
            text-align: right;
            margin: 20px;

            .Icon {
                width: 32px;
                height: 32px;
                fill: $WHITE;
            }

            .Icon-close {
                width: 1rem;
                height: 1rem;
            }

            svg {
                fill: $PRIMARY_COLOR !important;
            }
        }

        .Header-hamburgerIcon {
            width: 100%;
            margin-right: 0;
            display: flex;
            flex-wrap: wrap;
            align-content: stretch;
            border-bottom: 1px solid $TOTAL_GREY;
            @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
                border-bottom: none;
            }
            background-color: $WHITE;
            flex-direction: row-reverse;

            &.supplier-only {
                display: flex !important;
                justify-content: space-between;
                height: 100%;
            }

            .search-icon {
                display: block;
                @include breakpoint($BREAKPOINT_DESKTOP) {
                    min-width: 40px;
                }
                margin-right: 10px;

                .Icon {
                    width: 17px !important;
                    height: 17px !important;
                }
            }

            .Icon-button-text .second-level {
                color: $WHITE !important;
                font-weight: normal !important;
                font-size: 14px;
            }

            .icon-mobile {
                display: flex;
                justify-content: space-between;
                align-items: center;
                width: 100%;

                button:hover {
                    border: none;
                }

                &.header-mobile-hamburger {
                    height: 58px;
                    padding: 0 10px;
                }

                &.header-mobile-search {
                    height: 35px;
                    background-color: $TOTAL_RED;
                    @include breakpoint($BREAKPOINT_DESKTOP) {
                        margin-right: 30px;
                    }

                    ul {
                        margin-bottom: 0;
                        padding: 0;
                        display: flex;
                        justify-content: right;
                    }

                    li {
                        list-style-type: none;

                        &.menu-account {
                            cursor: pointer;
                            display: inline-flex;
                        }
                    }

                    .mobile-selects {
                        .v-line {
                            background-color: $WHITE;
                        }
                    }

                    .Header-menuItems {
                        display: inline-flex !important;
                        align-items: center;
                        text-align: left;
                        margin: 0 11px 0 13px;

                        .header-language-select {
                            margin-left: 15px;
                        }

                        li {
                            display: inline;

                            a {
                                display: inline-flex !important;
                                align-items: center;
                            }
                        }
                    }
                }

                #js-hamburger-button-mobile {
                    margin: 0;
                    padding: 0 !important;

                    &:hover {
                        border: none;
                        color: $TOTAL_RED;
                    }
                }

                #js-close-button {
                    margin: 0;
                    padding: 0 !important;

                    &:hover {
                        border: none;
                        color: $TOTAL_RED;
                    }
                }

                .Icon {
                    stroke: $WHITE;
                    margin: 0;
                }

                .all-carts {
                    display: flex;
                }

                .Item-cart {
                    display: flex;
                    align-items: center;
                    margin-right: 10px;
                    position: relative;
                }
            }
        }

        .Header-hamburgerIcon.supplier-only {
            width: 100%;
            height: 100%;

            .Menu-item {
                display: block;
            }
        }

        .Header-h-line {
            display: none;
        }

        .Header-menu {
            background: none !important;
            width: 100%;
            display: none;

            .Item-cart {
                .quantity {
                    display: flex;
                    align-items: center;
                    position: absolute;
                    left: 5rem;
                    height: 1rem;
                    min-width: 1rem;
                    padding: 0.2rem;
                    font-size: 0.7rem;
                    background-color: $PRIMARY_COLOR;
                    -webkit-box-shadow: 0px 0px 8px $TOTAL_DARK_GREY;
                    -moz-box-shadow: 0px 0px 8px $TOTAL_DARK_GREY;
                    box-shadow: 0px 0px 8px $TOTAL_DARK_GREY;
                    border-radius: 1rem;

                    p {
                        text-align: center;
                        flex-grow: 1;
                        margin-bottom: 0;
                    }
                }
            }

            .Header-menuItems {
                margin: 3rem 0 0 0;
                color: $WHITE;

                .Menu-item {
                    padding: 0 0.3em;

                    a {
                        display: flex;
                        color: $WHITE;
                        font-size: 1.2rem;
                        font-weight: normal;

                        span {
                            display: flex;
                        }

                        .Icon-button-text {
                            display: flex;
                            justify-content: center;
                            padding-left: 1rem;
                            padding-right: 1rem;

                            .second-level {
                                font-size: 0.875rem;
                                font-weight: 600;
                                color: $TOTAL_GREY;
                                letter-spacing: 0.1rem;
                            }
                        }
                    }
                }

                li {
                    width: 100%;
                    padding: 0 3em;

                    &.hamburger-link {
                        margin: 1.2rem 0;

                        .link {
                            color: $TOTAL_DARK_GREY;
                            font-size: 1rem;
                            font-weight: 600;
                            margin-left: 1rem;
                        }
                    }
                }

                .simple-links {
                    margin: 2rem 0;
                }

                .h-line {
                    background-color: $PRIMARY_COLOR;
                    margin: 0 5%;
                    width: 90%;
                }

                .Icon-flag {
                    width: 20px;
                    height: 15px;
                    fill: $PRIMARY_COLOR !important;

                    &.padding {
                        padding: 0.2em;
                    }
                }

                .dropdown {
                    display: block;

                    #Dropdown-Account {
                        width: 95%;
                        text-align: left;

                        a {
                            border-bottom: 1px solid $TOTAL_GREY5;
                        }
                    }

                    .dropdown-display {
                        font-size: 1.2rem;
                        margin-top: 20px;

                        a {
                            color: $WHITE;
                            display: flex;
                            align-items: center;
                        }

                        &:not(.active) {
                            .cross {
                                display: flex;
                                justify-content: center;
                                align-items: center;
                                height: 100%;
                                margin: 8px;

                                &::after {
                                    content: "";
                                    height: 2px;
                                    width: 20px;
                                    background-color: $TOTAL_GREY;
                                }

                                &::before {
                                    content: "";
                                    position: absolute;
                                    height: 20px;
                                    width: 2px;
                                    background-color: $TOTAL_GREY;
                                }
                            }
                        }

                        &.active {
                            .cross {
                                display: flex;
                                justify-content: center;
                                align-items: center;
                                height: 100%;
                                margin: 8px;

                                &::after {
                                    content: "";
                                    height: 2px;
                                    width: 20px;
                                    background-color: $TOTAL_GREY;
                                }
                            }
                        }
                    }

                    .dropdown-content {
                        position: relative;
                        box-shadow: none;
                        padding: 0 40px;
                        min-width: auto;

                        &.show {
                            display: block;
                        }

                        .arrow-up {
                            display: none;
                        }

                        .dropdown-list {
                            background-color: transparent;
                            margin-left: 30px;

                            a {
                                color: $TOTAL_GREY;
                                font-size: 1.2rem;
                            }
                        }
                    }
                }
            }
        }
    }

    @media print {
        display: none;
    }
}

#Dropdown-Account {
    min-width: 230px;
    text-align: left;

    a {
        white-space: nowrap;
        border-top: 1px solid $TOTAL_GREY5;
        padding: 12px 30px 12px 15px;
    }

    .onboarding-step {
        margin-bottom: 0;
        margin-right: 20px;
        width: 15px;
    }
}

.Icon {
    display: inline;
    margin: 0 5px;
}

.Icon-shipping {
    background: url("/images/ico-shipping.svg");
    background-repeat: no-repeat;
    width: 30px;
    height: 20px;
}

.Icon-shipping-white {
    background: url("/images/ico-shipping-white.svg");
    background-repeat: no-repeat;
    width: 30px;
    height: 20px;
}

.Icon-earth {
    background: url("/images/earth.svg");
    background-repeat: no-repeat;
    width: 20px;
    height: 20px;
}

.Icon-white-earth {
    background: url("/images/earth-white.svg");
    background-repeat: no-repeat;
    width: 20px;
    height: 20px;
}

.Icon-flag {
    width: 13px;
    height: 15px;

    @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
        width: 21px;
        height: 15px;
    }
}

.Icon-button-text {
    display: flex;
    flex-direction: column;
    padding-left: 10px;
    padding-right: 10px;

    .first-level {
        font-size: 0.75rem;
        font-weight: 600;
        white-space: nowrap;
        letter-spacing: 1px;
    }

    .second-level {
        font-size: 1rem;
        line-height: 1rem;
        font-weight: 300;
        white-space: nowrap;
        text-transform: none;
        color: $TOTAL_DARK_GREY2;
        max-width: 200px;
        overflow: hidden;
        text-overflow: ellipsis;
    }
}

.Header-shadow {
    z-index: 150;
    position: fixed;
    width: 100%;
    top: 40px;
    height: 10px;

    -webkit-box-shadow: 0 5px 5px 0 rgba(222, 222, 222, 1);
    -moz-box-shadow: 0 5px 5px 0 rgba(222, 222, 222, 1);
    box-shadow: 0 5px 5px 0 rgba(222, 222, 222, 1);

    @include breakpoint($BREAKPOINT_DESKTOP) {
        top: 75px;
    }
}

.Header-h-line {
    z-index: 150;
    position: fixed;
    top: $NAV_USER_HEIGHT;
}
