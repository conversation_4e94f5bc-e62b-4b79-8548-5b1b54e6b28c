<?php

namespace AppBundle\Twig;

use Twig\Extension\AbstractExtension;
use Twig\TwigFilter;

class ChangeSetExtension extends AbstractExtension
{
    public function getName(): string
    {
        return 'json.filter.extension';
    }

    public function getFilters(): array
    {
        return [
            'change_set_to_html' => new TwigFilter('change_set_to_html', 'ChangeSetExtension:changeSetToHtml', ['is_safe' => ['html']]),
        ];
    }


    public function changeSetToHtml($changeSet): string
    {
        $result = "<ul>";
        if ($changeSet) {
            $this->serializeChangeSet($result, $changeSet);
        }
        $result .= "</ul>";
        return $result;
    }

    private function serializeChangeSet(&$result, $changeSet, $prefix = '')
    {
        foreach ($changeSet as $field => $value) {
            $this->appendField($result, $field, $value, $prefix);
        }
    }

    private function appendField(&$result, $field, $value, $prefix = "")
    {
        $result .= '<li> ' . $field . ' ';
        if ($field === 'password') {
            $this->appendValues($result, ['********', '********']);
        } else {
            $this->appendValues($result, $value);
        }
        $result .= '</li>';
    }

    private function appendValues(&$result, $value)
    {
        if (is_array($value) && count($value) > 0) {
            $result .= '[ ancien = ';
            $this->appendSimpleValue($result, $value[0]);
            $result .= '] [ nouveau = ';
            if (count($value) > 1) {
                $this->appendSimpleValue($result, $value[1]);
                $result .= ' ]';
            } else {
                $result .= '??? ]';
            }
        }
    }

    private function appendSimpleValue(&$result, $value)
    {
        if ($value === NULL) {
            $result .= htmlspecialchars('<NULL>');
        } else if ($value instanceof \DateTime) {
            $result .= htmlspecialchars($value->format('d-m-Y H:i:s'));
        } else if (is_bool($value)) {
            if ($value) {
                $result .= 'true';
            } else {
                $result .= 'false';
            }

        } else {
            $result .= htmlspecialchars($value);
        }
    }

}