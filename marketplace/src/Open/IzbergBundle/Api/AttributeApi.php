<?php

namespace Open\IzbergBundle\Api;

use Open\IzbergBundle\Api;
use Open\IzbergBundle\Dto\AttributeDTO;
use Open\LogBundle\Utils\EventNameEnum;

class AttributeApi extends Api
{
    private const ATTRIBUTE_PATH = "product_attribute/?application=";

    public function  getUri(){
        return null;
    }

    public function getItemClass(){
        return null;
    }

    /**
     * @param $locale
     * @return array
     */
    public function getAttributes($locale)
    {
        $headers = [
            Api::HEADER_ACCEPT_LANGUAGE => $locale
        ];

        $attributes = [];
        $requestUrl = self::ATTRIBUTE_PATH . $this->getApplicationId();

        do {
            $response = $this->sendApiRequest(
                self::HTTP_GET_OPERATION,
                $requestUrl,
                null,
                $headers,
                [
                    self::OPTION_IS_OPERATOR => true,
                ]
            );

            $attributeKeyIsValid = function($attributeKey) {
                $validAttributeKey = true;

                foreach([' ', '&'] as $illegalChar) {
                    if(strpos($attributeKey, $illegalChar) !== false) {
                        $this->logService->error(
                            sprintf("izberg attribute %s should not contains '%s' character", $attributeKey, $illegalChar),
                            Api::LOG_IZBERG_API_ERROR
                        );

                        $validAttributeKey = false;
                    }
                }

                return $validAttributeKey;
            };

            // For all addresses in the json serialize to an Address entity and add it to the collection
            foreach ($response->body->objects as $attribute) {

                if (!call_user_func($attributeKeyIsValid, $attribute->key)) {
                    continue;
                }

                $attributeDTO = new AttributeDTO();
                $attributeDTO->setKey($attribute->key);
                $attributeDTO->setLabel($attribute->name);
                $attributeDTO->setType($attribute->value_type);
                $attributes[$attribute->key] = $attributeDTO;
            }

            $requestUrl = $response->body->meta->next;

        }while($requestUrl !== null);

        return $attributes;
    }

    public function getOrderAttributeId($key)
    {
        return $this->getAttributeId('order_attribute', $key);
    }

    public function getMerchantAttributeId($key)
    {
        return $this->getAttributeId('merchant_attribute', $key);
    }

    public function getCustomerAttributeId($key)
    {
        return $this->getAttributeId('customer_attribute', $key);
    }

    private function getAttributeId(string $api, string $key)
    {
        $response = $this->sendApiRequest(
            self::HTTP_GET_OPERATION,
            $api.'/?key='.$key,
            null,
            [],
            [
                self::OPTION_IS_OPERATOR => true,
            ]
        );

        if (!count($response->body->objects)) {
            $this->logService->error(
                sprintf(
                    'Could not find order_attribute identified by key %s',
                    $key
                ),
                EventNameEnum::IZBERG_ATTRIBUTE_NOT_FOUND
            );
            return null;
        }

        return $response->body->objects[0]->id;
    }

    /**
     * update the reconciliation key of a merchant order
     * @param integer $attributeId the identifier of the attribute
     * @param integer $merchantOrderId the identifier of the merchant order
     * @param string $value the value to set for the attribute
     */
    public function updateReconciliationAttribute($attributeId, $merchantOrderId, $value)
    {
        $this->updateMerchantOrderAttribute($attributeId, $merchantOrderId, $value);
    }

    public function updateMerchantOrderAttribute($attributeId, $merchantOrderId, $value)
    {
        $data = [
            'attribute' => '/v1/order_attribute/'.strval($attributeId).'/',
            'application' => '/v1/application/'.$this->getApplicationId().'/',
            'value' => $value,
            'entity' => '/v1/merchant_order/'.strval($merchantOrderId).'/'
        ];


        $this->sendApiRequest(
            self::HTTP_POST_OPERATION,
            'order_attribute_value/',
            $data,
            [],
            [
                self::OPTION_IS_OPERATOR => true,
                self::OPTION_NO_DATA_RETURN => true,
            ]
        );
    }

    /**
     * @param string $attributeKey
     * @param int    $merchantId
     * @param bool   $forceOperator
     *
     * @return mixed|null
     */
    public function getMerchantAttribute(string $attributeKey,int $merchantId, bool $forceOperator=false)
    {
        $data = [
            'key' => $attributeKey,
            'merchant' => $merchantId
        ];

        $response = $this->sendApiRequest(
            self::HTTP_GET_OPERATION,
            'merchant_attribute_value/?'.http_build_query($data),
            [],
            [],
            [
                self::OPTION_IS_OPERATOR => $forceOperator
            ]
        );
        if (!count($response->body->objects)) {
            $this->logService->info(
                sprintf(
                    'Could not find merchant attribute identified by id %s for merchant %s',
                    $attributeKey,
                    $merchantId
                ),
                EventNameEnum::IZBERG_ATTRIBUTE_NOT_FOUND
            );
            return null;
        }


        return $response->body->objects[0]->value;
    }

    public function searchMerchantAttribute($data)
    {
        $data["limit"] = 100;
        $response = $this->sendApiRequest(
            self::HTTP_GET_OPERATION,
            'merchant_attribute_value/?'.http_build_query($data)
        );

        return $response->body->objects;
    }

    public function updateMerchantAttribute($attributeId, $merchantId, $value)
    {
        $data = [
            "application" => "/v1/application/".$this->getApplicationId()."/",
            "attribute" => "/v1/merchant_attribute/".  $attributeId  ."/",
            "merchant" => "/v1/merchant/".$merchantId."/",
            "value" => $value,
        ];

        $this->sendApiRequest(
            self::HTTP_POST_OPERATION,
            'merchant_attribute_value/',
            $data,
            [],
            [
                self::OPTION_IS_OPERATOR => true,
                self::OPTION_NO_DATA_RETURN => true,
            ]
        );
    }

    /**
     * update many merchant order attributes
     * @param $merchantOrderId
     * @param array $attributes (key/value)
     */
    public function updateMerchantOrderAttributes($merchantOrderId, $attributes)
    {
        foreach ($attributes as $attributeId => $value) {
            $this->updateMerchantOrderAttribute($attributeId, $merchantOrderId, $value);
        }
    }

    public function updateCustomerAttribute($attributeId, $userId, $value){
        $data = [
            'attribute' => "/v1/customer_attribute/".strval($attributeId)."/",
            'application' => "/v1/application/".$this->getApplicationId()."/",
            'value' => $value,
            'customer' => '/v1/user/'.strval($userId).'/',
        ];

        $this->sendApiRequest(
            self::HTTP_POST_OPERATION,
            'customer_attribute_value/',
            $data,
            [],
            [
                self::OPTION_NO_DATA_RETURN => true,
            ]
        );
    }
}
