{% trans_default_domain 'AppBundle' %}

{# @var metaCartOrder \AppBundle\Entity\MetaCart #}
{% for metaCartOrder in metaCarts %}
    <a href="{{ path('front.subordinatesOrder.detail', {'orderId': metaCartOrder.id }) }}">
        <section class="order-summary">
            <section>
                <section><span>{{ 'orders.list.block.order_title' | trans }}</span> : {{ metaCartOrder.id }}</section><section>{{ metaCartOrder.orderCreatedAt | localizeddate('short', 'none', locale) }}</section>
            </section>
            <section>
                <section><span>{{ 'orders.list.block.total_title' | trans }}</span> : {{ metaCartOrder.amount | number_format(2, '.') }} {{ metaCartOrder.currency }}</section>
                <img alt="open" src="{{ asset('images/arrow-next.svg') }}">
            </section>
        </section>
    </a>
{% endfor %}
<div class="show-more-container mobile-only" onclick="ShowMoreSubOrders();">
    <span class="show-more-img"><img src="{{ asset('images/plus.svg') }}"></span>
    <span>{{ 'orders.show_more'|trans | upper }}</span>
</div>
