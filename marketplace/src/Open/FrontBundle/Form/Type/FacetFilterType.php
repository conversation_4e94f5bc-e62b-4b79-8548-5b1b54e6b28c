<?php

namespace Open\FrontBundle\Form\Type;

use AppBundle\Model\Facet;
use AppBundle\Model\FacetValue;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\FormType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

class FacetFilterType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options)
    {
        $facets = $options['facets'];

        /** @var Facet $facet */
        foreach ($facets as $facet) {

            if ($facet->getFieldName() === 'product.application_categories') {
                continue;
            }

            $choices = $this->buildFacetChoices($facet);
            if (count($choices)) {
                $builder->add(
                    str_replace('.', ':', $facet->getFieldName()),
                    ChoiceType::class,
                    [
                        'label' => $facet->getLabel(),
                        'multiple' => true,
                        'expanded' => true,
                        'choices' => $choices,
                    ]
                );
            }
        }
    }

    private function buildFacetChoices(Facet $facet): array
    {
        $choices = [];

        /** @var FacetValue $facetValue */
        foreach ($facet->getValues() as $facetValue) {
            $choiceLabel = sprintf('%s (%d)', $facetValue->getLabel(), $facetValue->getHits());
            $choices[$choiceLabel] = $facetValue->getValue();
        }

        return $choices;
    }

    public function configureOptions(OptionsResolver $resolver)
    {
        $resolver->setDefaults([
            'facets' => [],
            'translation_domain' => 'AppBundle',
        ]);
    }

    public function getParent()
    {
        return FormType::class;
    }
}
