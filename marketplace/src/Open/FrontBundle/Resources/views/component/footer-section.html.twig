<li>
    <div class="footer-section">
        <div class="title-container">
            <h4 class="section-title">{{ title }}</h4>
            {% if id is defined %}
                <div id="cross-{{ id }}" onclick="window.UI.Footer.onClickCross('{{ id }}')" class="cross mobile-only">
                    <div class="horizontal"></div>
                    <div class="vertical"></div>
                </div>
            {% endif %}
        </div>
        <div {% if id is defined %} id="section-{{ id }}" class="section-content hide-mobile" {% else %} class="section-content" {% endif %}>
            <div class="section-column">
            {% for link in links %}
                <a class="footer-link" href="{{ link.URL }}">{{ link.name }}</a>
            {% endfor %}
            </div>
        </div>
    </div>
</li>