<?php

namespace Open\IzbergBundle\Model;

use Doctrine\Common\Collections\ArrayCollection;
use <PERSON><PERSON>\Serializer\Annotation as JSM;
use <PERSON><PERSON>\Serializer\Annotation\Type;

/**
 * @JSM\ExclusionPolicy("none")
 */
class FetchCreditNotesResponse
{
    /**
     * @Type("Open\IzbergBundle\Model\Meta")
     * @var Meta
     */
    private $meta;

    /**
     * @Type("ArrayCollection<Open\IzbergBundle\Model\CreditNote>")
     * @var ArrayCollection
     */
    private $objects;

    public function getObjects(): ArrayCollection
    {
        return $this->objects;
    }

    public function setObjects(ArrayCollection $objects)
    {
        $this->objects = $objects;
    }

    public function getMeta(): Meta
    {
        return $this->meta;
    }

    public function setMeta(Meta $meta): void
    {
        $this->meta = $meta;
    }

    public function hasCreditNotes()
    {
        return ($this->objects->count());
    }

    public function first()
    {
        return $this->objects->first();
    }
}
