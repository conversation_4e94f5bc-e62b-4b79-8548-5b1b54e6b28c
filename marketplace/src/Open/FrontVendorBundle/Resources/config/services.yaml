parameters:

services:
    _defaults:
        autowire: true
        autoconfigure: true
        public: false

    Open\FrontVendorBundle\:
        resource: '../../*'
        exclude: '../../{Entity,EventSubscriber,Repository,Tests,Util}'

    Open\FrontVendorBundle\Controller\:
        resource: '../../Controller'
        public: true
        tags: ['controller.service_arguments']

    Open\FrontVendorBundle\Controller\ApiController:
        public: true
        calls:
            - [ setContainer, [ "@service_container" ] ]
