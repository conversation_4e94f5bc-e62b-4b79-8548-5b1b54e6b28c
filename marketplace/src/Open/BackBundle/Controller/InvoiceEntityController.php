<?php

namespace Open\BackBundle\Controller;

use App<PERSON><PERSON><PERSON>\Controller\MkoController;
use AppBundle\Entity\CheckableZipCodeTrait;
use AppBundle\Services\InvoiceEntityService;
use Open\BackBundle\Form\FilterInvoiceEntityType;
use Open\BackBundle\Form\InvoiceEntityThresholdFilter;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Security;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;

class InvoiceEntityController extends MkoController
{
    use CheckableZipCodeTrait;

    /**
     * @Route("/entity-invoice", name="admin.invoice_entity.list")
     * @Security("is_granted('ROLE_ADMIN') or is_granted('ROLE_OPERATOR')")
     * @param Request $request
     *
     * @return mixed
     */
    public function listInvoiceEntity(Request $request, InvoiceEntityService $invoiceEntityService)
    {
        $form = $this->createForm(FilterInvoiceEntityType::class);

        $form->handleRequest($request);
        if ($form->isSubmitted() && $form->isValid()) {
            $filter = $form->getData();
        } else {
            $filter = [];
            $filter['status'] = 'all';
        }

        //render list of users
        return $this->render(
            '@OpenBack/invoice_entity/invoice_entity_list.html.twig',
            [
                'pagination' => $invoiceEntityService
                    ->getCustomFilteredPaginator(25, $request, $filter),
                'form' => $form->createView(),
                'locale' => $request->getLocale(),
            ]
        );
    }

    /**
     * @Route("/invoice-entity-thresholds", name="admin.invoice_entity.thresholds")
     * @Security("is_granted('ROLE_ADMIN') or is_granted('ROLE_OPERATOR')")
     */
    public function invoiceEntityThresholds(Request $request, InvoiceEntityService $invoiceEntityService)
    {
        $thresholdOrder = $request->query->get('order', 'asc');

        $options = [];
        $filter = [];


        $form = $this->createForm(InvoiceEntityThresholdFilter::class, null, $options);
        $form->handleRequest($request);


        // filtering form submitted
        if ($form->isSubmitted() && $form->isValid()) {
            $filter = $form->getData();
        }

        // thresholds order
        $filter['order'] = $thresholdOrder;

        // threshold form submitted
        if ($request->isMethod(Request::METHOD_POST) &&
            $this->isCsrfTokenValid('invoice_entity_threshold_update', (string) $request->request->get('token'))
        ) {
            $invoiceEntityId = intval($request->request->get('invoice_entity'));
            $invoiceEntity = $invoiceEntityService->findInvoiceEntityById($invoiceEntityId);

            if ($invoiceEntity) {
                $invoiceEntityThreshold = intval($request->request->get('invoice_entity_threshold'));
                $invoiceEntityThresholdStatus = (bool)intval($request->request->get('invoice_entity_threshold_status'));

                $invoiceEntity->setValidationThreshold($invoiceEntityThresholdStatus ? $invoiceEntityThreshold : 0);
                $invoiceEntity->setManagerValidation($invoiceEntityThresholdStatus);
                $invoiceEntityService->save($invoiceEntity);

                return $this->redirect($request->headers->get('referer'));
            }
        }

        $paginator = $invoiceEntityService->getCustomFilteredPaginator(200, $request, $filter);
        //render list of users
        return $this->render('@OpenBack/invoice_entity/invoice_entity_thresholds.html.twig',
            array(
                'pagination' => $paginator,
                //'qualifier_val' => $qualifier,
                'form' => $form->createView(),
                'locale' => $request->getLocale(),
                'thresholdOrder' => $thresholdOrder,
            ));
    }

    /**
     * @Route("/entity-invoice/{id}/info", name="admin.invoice_entity.info")
     * @Security("is_granted('ROLE_ADMIN') or is_granted('ROLE_OPERATOR')")
     * @param integer $id
     *
     * @return mixed
     */
    public function getInvoiceEntityInformation(InvoiceEntityService $invoiceEntityService, $id)
    {
        return $this->render('@OpenBack/invoice_entity/invoice_entity_info.html.twig', [
            'invoice_entity' => $invoiceEntityService->findInvoiceEntityById($id),
        ]);
    }

}
