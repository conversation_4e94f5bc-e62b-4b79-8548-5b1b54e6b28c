@import "../variables";
@import "~breakpoint-sass/stylesheets/breakpoint";

body[class*="Page--user_profile"], body[class*="Page--nl_user_profile"] {
    .page-wrap {
        min-height: calc(100vh - 230px);
    }

    .user-profile {
        margin: 40px auto;
        text-align: center;

        .account-header {
            margin: 20px auto 60px auto;
            text-align: center;
        }

        .account-info {
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            max-width: 1000px;
            margin: auto;

            @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
                flex-direction: column;
            }
        }

        .account-card {
            width: 49%;
            border: 1px solid $TOTAL_GREY;
            padding: 25px 0 15px 0;

            > section {
                padding: 15px 30px 0 30px;
            }

            @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
                width: 95%;
                margin: 0 auto 25px auto;
            }

            .Icon {
                width: 25px;
                margin: 0 10px 0 0;
            }

            &-header {
                font-size: 14px;
                color: $TOTAL_DARK_GREY3;
                border-bottom: 1px solid $TOTAL_GREY5;
                text-align: left;
                padding: 0 0 15px 0 !important;
                margin: 0 30px 0 30px;
            }

            &-info {
                padding-top: 20px;
                text-align: left;
                line-height: 3;

                div {
                    display: flex;
                    align-items: center;
                }

                section {
                    display: flex;
                    flex-direction: row;
                    justify-content: space-between;
                }

                #account-identification {
                    @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
                        flex-direction: column;
                    }
                }
            }

            &-note {
                text-align: left;
                color: $TOTAL_GREY6;
                font-size: 15px;
                font-style: italic;
                margin-top: 40px;

                @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
                    padding: 0 10px 0 10px !important;
                }

                span {
                    font-weight: bold;
                }
            }

            #account-delegates {
                line-height: 25px !important;
                margin: 0 10px 10px 0 !important;
            }

            #account-manager, #account-delegates {
                span {
                    margin-right: 10px;
                }

                justify-content: left;
            }

            #account-site {
                .card-label {
                    margin-right: 10px;
                }

                line-height: 1.8;
            }
        }

        .card-label {
            text-transform: uppercase;
            font-size: 14px;
            color: $TOTAL_DARK_GREY2;
            margin-right: 5px;
            white-space: nowrap;
        }

        .account-title {
            background-color: $WHITE;
            display: inline-block;
            position: relative;
            font-size: 22px;
            font-weight: bold;

            &:after {
                content: "";
                position: absolute;
                height: 2px;
                border-top: 2px solid $TOTAL_GREY;
                top: 18px;
                width: 20px;
                right: 100%;
                margin-right: 15px;
            }

            &:before {
                content: "";
                position: absolute;
                height: 2px;
                border-top: 2px solid $TOTAL_GREY;
                top: 18px;
                width: 20px;
                left: 100%;
                margin-left: 15px;
            }
        }
    }
}
