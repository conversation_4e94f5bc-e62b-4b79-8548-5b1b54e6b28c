<?php
/**
 * Created by PhpStorm.
 * User: CMA15450
 * Date: 08/03/2017
 * Time: 13:47
 */

namespace Open\BackBundle\Form;

use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Form\Extension\Core\Type\CollectionType;
use AppBundle\Entity\SettingsCollection;
use Symfony\Component\Form\Extension\Core\Type\SubmitType;


class SettingsForm extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options)
    {

        $builder
            ->add('settings', CollectionType::class, array(
                'entry_type' => SettingForm::class
            ))
            ->add('submit', SubmitType::class, array(
                    'label' => 'system.setting.form.submit'
            ))
        ;
    }

    public function configureOptions(OptionsResolver $resolver)
    {
        $resolver->setDefaults([
            'translation_domain' => 'AppBundle',
            'data_class' => SettingsCollection::class
        ]);
    }
}
