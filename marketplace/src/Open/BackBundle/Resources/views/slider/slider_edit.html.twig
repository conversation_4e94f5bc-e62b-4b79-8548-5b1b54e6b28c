{% extends '@OpenBack/base.html.twig' %}
{% form_theme form 'bootstrap_4_layout.html.twig' %}
{% trans_default_domain "AppBundle" %}
{% if id is null %}
    {% set pageTitle = 'back.slider.commons.addTitle'| trans %}
{% else %}
    {% set pageTitle = 'back.slider.commons.editTitle'| trans({'%id': id, '%type': nodeType}) %}
{% endif %}

{% block title %}
    {{ pageTitle }}
{% endblock %}

{% block body %}

 <div class="container">

    <div style="width:100%; padding: 0 0 20px 5px">
        <a href="{{ path('admin.slider.list') }}" style="display:flex; align-items: center; color: #9600FF">
            <div style="width: 10px; height: 10px; border: solid; border-color: #9600FF; border-width: 0 2px 2px 0;transform: rotate(135deg); margin-right: 5px"></div>
            {{ 'back.back_to_list' | trans }}
        </a>
    </div>

    {{ form_start(form, {'attr': {'id':'js-form-notification-edit'}}) }}

    <div class="card">
        <div class="card-header">
            {{ pageTitle }}
        </div>
        <div class="card-body">
            <div class="form-row">
                <div class="col-md-12">
                    {{ form_row(form.slug) }}
                </div>
            </div>

            <div class="form-row">
                <div class="col-md-12">
                    {{ form_row(form.orderNode) }}
                </div>
            </div>
            {% if sliderItem.backgroundImage and sliderItem.backgroundImage.id is defined %}
             <div class="form-row">
                <div class="col-md-12">
                    {{ 'back.slider.edit.actualImage' | trans }}
                    <img src="{{ path('front.get.image', {'id': sliderItem.backgroundImage.id}) }}" width="100" height="100" border="1 px black"/>
                </div>
                <div class="col-md-12">
                    {{ 'back.slider.edit.imageP' | trans }}
                </div>
             </div>
            {% endif %}

            <div class="form-row js-attachments-container">
                {{ form_label(form.backgroundImage) }}
                <ul class="attachment-files js-attachment-files">
                    <li class="file--warning">{% if sliderItem.backgroundImage %}
                        {{ sliderItem.backgroundImage }}
                        {% else %}
                        {{ 'ticket.common.nofiles'|trans }}
                    {% endif %} 
                    </li>
                </ul>
                {{ form_widget(form.backgroundImage,{'required':sliderItem.backgroundImage ? false :true}) }}
            </div>

            <label class="form-control-label required">{{ 'node.form.content'|trans }}</label>

            <div class="card">
                <div class="card-header">
                    <nav>
                        <ul class="nav nav-tabs card-header-tabs" id="js-content-nav" role="tablist">
                        </ul>
                    </nav>
                </div>
                <div class="card-body">

                    <div class="tab-content" id="js-content-tabs">
                        {% for c in form.content %}
                            <div class="tab-pane fade js-content-tab" role="tabpanel" data-lang="{{ c.vars.value.lang }}" id="tab-{{ c.vars.value.lang }}">{{ form_widget(c) }}</div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>

<br />

    <div class="form-row Buttons">
        <div class="col-md-12">
            <a class="btn btn-danger" href="{{ path('admin.slider.list')}} " onclick="alertModal(event)">{{ 'back.commons.cancel'| trans }}</a>
        {% if id == 0 %}
            {% set button_title = 'back.slider.commons.create' %}
        {% else %}
            {% set button_title = 'back.slider.commons.update' %}
        {% endif %}
            <button type="submit" class="btn btn-primary">{{ button_title| trans }}</button>
        </div>
    </div>

    {{ form_end(form) }}

<br/>
{% endblock %}

{% block javascripts %}
   <script type="text/javascript">
        'use strict';

        var _updateList = function () {
                var $filesDiv = $(this).parents('.js-attachments-container').find('.js-attachment-files');

                if (this.files.length > 0) {
                    $filesDiv.html('');

                    for (var i = 0 ; i < this.files.length ; i++) {
                        var $li = $('<li>' + this.files[i].name  + '</li>');

                        $filesDiv.append($li);
                    }
                } else {
                    $filesDiv.html('<li class="file--warning">{{ 'ticket.common.nofiles'|trans }}</li>');
                }
            };

        function alertModal(e){
                e.preventDefault();
                var href = $(e.currentTarget).attr('href');

                UI.Modal.confirm('', '{{ 'back.slider.modal.cancel_confirm'|trans({}, 'AppBundle') }}', function () {
                    window.UI.Modal.showLoading();
                    window.location.href = href;
                },
                    function () {
                        console.log('Slider object confirmation canceled');
                    });

                return false;
            };

      document.addEventListener('DOMContentLoaded', function() {

          window.UI.SlugType.initWithoutValidation('slider_form_content_0_link');
          window.UI.SlugType.initWithoutValidation('slider_form_content_1_link');
          window.UI.SlugType.initWithoutValidation('slider_form_content_2_link');
          window.UI.SlugType.initWithoutValidation('slider_form_content_3_link');
          window.UI.SlugType.initWithoutValidation('slider_form_content_4_link');


        window.UI.ContentTabs.init(
          '.js-content-tab',
          '#js-content-tabs',
          '#js-content-nav',
          '#js-add-lang',
          [
            {
                value: 'en',
                label: '{{ 'node.form.lang.en'| trans }}'
            },
            {
                value: 'fr',
                label: '{{ 'node.form.lang.fr'| trans }}'
            },
            {
                value: 'es',
                label: '{{ 'node.form.lang.es'| trans }}'
            },
            {
                value: 'de',
                label: '{{ 'node.form.lang.de'| trans }}'
            },
            {
                value: 'it',
                label: '{{ 'node.form.lang.it'| trans }}'
            }
           ]
        );
         // EO Sort country list
         $('input[type=file]').on('change', _updateList);

      });
    </script>

{% endblock %}