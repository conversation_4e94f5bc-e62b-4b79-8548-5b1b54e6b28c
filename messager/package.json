{"name": "@total/messager", "version": "0.1.17", "description": "Stencil Component Starter", "main": "dist/index.js", "module": "dist/index.mjs", "es2015": "dist/esm/index.mjs", "es2017": "dist/esm/index.mjs", "types": "dist/types/index.d.ts", "collection": "dist/collection/collection-manifest.json", "collection:main": "dist/collection/index.js", "unpkg": "dist/messager/messager.js", "files": ["dist/", "loader/"], "scripts": {"build": "stencil build --resolveJsonModule", "start": "stencil build --dev --watch --serve --resolveJsonModule", "test": "stencil test --spec --e2e", "test.watch": "stencil test --spec --e2e --watchAll", "generate": "stencil generate"}, "devDependencies": {"@stencil/core": "^1.8.11", "@stencil/react-output-target": "0.0.9", "@stencil/sass": "^1.1.1"}, "repository": {"type": "git", "url": "http://localhost"}, "dependencies": {"axios": "^0.19.2", "mime": "^2.4.4", "moment": "^2.24.0"}, "author": "", "license": "ISC"}