<?php
/**
 * Created by PhpStorm.
 * User: LRO16285
 * Date: 29/03/2018
 * Time: 15:14
 */

namespace AppBundle\Form\Type;

use AppBundle\Entity\Address;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Validator\Constraints\Length;


class AddressType extends AbstractType
{
  const LABEL = "label";
  const REQUIRED = "required";

  public function buildForm(FormBuilderInterface $builder, array $options)
  {
    $builder
      ->add(
        'address',
        TextType::class,
        array(
          self::LABEL => 'address.form.address',
          self::REQUIRED => true,
            'constraints' => array(
                new Length(null,1, 35)
            )
        ))
      ->add(
        'address2',
        TextType::class,
        array(
          self::LABEL => 'address.form.address2',
          self::REQUIRED => true,
            'constraints' => array(
                new Length(null,0, 35)
            )
        ))
      ->add(
        'zipCode',
        TextType::class,
        array(
          self::LABEL => 'address.form.zipcode',
          self::REQUIRED => true,
        ))
      ->add(
        'city',
        TextType::class,
        array(
          self::LABEL => 'address.form.city',
          self::REQUIRED => true,
        ))
      ->add(
        'regionText',
        TextType::class,
        array(
          self::LABEL => 'address.form.region',
          self::REQUIRED => false,
        ))
      ->add(
        'country',
        TextType::class,
        array(
          self::LABEL => 'address.form.country',
          self::REQUIRED => false,
        ))
    ;

  }

  public function configureOptions(OptionsResolver $resolver)
  {
    $resolver->setDefaults(array(
      'data_class' => Address::class,
      'translation_domain' => 'AppBundle',
      'validation_groups' => 'Default',
    ));
  }

}