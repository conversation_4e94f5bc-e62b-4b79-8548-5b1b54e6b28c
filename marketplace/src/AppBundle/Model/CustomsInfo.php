<?php
/**
 * Created by PhpStorm.
 * User: LRO16285
 * Date: 25/06/2018
 * Time: 09:16
 */

namespace AppBundle\Model;


class CustomsInfo
{
  private $is_domestic;
  private $is_export_EU;
  private $is_export_non_EU;
  private $free_tax;

  /**
   * CustomsInfo constructor.
   *
   * @param $is_domestic
   * @param $is_export_EU
   * @param $is_export_non_EU
   * @param $free_tax
   */
  public function __construct(
    $is_domestic,
    $is_export_EU,
    $is_export_non_EU,
    $free_tax
  ) {
    $this->is_domestic = $is_domestic;
    $this->is_export_EU = $is_export_EU;
    $this->is_export_non_EU = $is_export_non_EU;
    $this->free_tax = $free_tax;
  }

  public function __toString()
  {
    if ($this->is_domestic){
      return 'customs.info.domestic';
    }

    if ($this->is_export_EU){
      return 'customs.info.export_EU';
    }

    if ($this->is_export_non_EU){
      return 'customs.info.export_non_EU';
    }

    return '';
  }

  /**
   * @return mixed
   */
  public function getisDomestic()
  {
    return $this->is_domestic;
  }

  /**
   * @param mixed $is_domestic
   */
  public function setIsDomestic($is_domestic): void
  {
    $this->is_domestic = $is_domestic;
  }

  /**
   * @return mixed
   */
  public function getisExportEU()
  {
    return $this->is_export_EU;
  }

  /**
   * @param mixed $is_export_EU
   */
  public function setIsExportEU($is_export_EU): void
  {
    $this->is_export_EU = $is_export_EU;
  }

  /**
   * @return mixed
   */
  public function getisExportNonEU()
  {
    return $this->is_export_non_EU;
  }

  /**
   * @param mixed $is_export_non_EU
   */
  public function setIsExportNonEU($is_export_non_EU): void
  {
    $this->is_export_non_EU = $is_export_non_EU;
  }

  /**
   * @return mixed
   */
  public function getFreeTax()
  {
    return $this->free_tax;
  }

  /**
   * @param mixed $free_tax
   */
  public function setFreeTax($free_tax): void
  {
    $this->free_tax = $free_tax;
  }

}