<?php

namespace Open\IzbergBundle\Api;

use Open\IzbergBundle\Api;
use Open\IzbergBundle\Model\Product;
use Open\IzbergBundle\Model\ProductOffer;

final class ProductApi extends Api
{
    private const PRODUCT_PATH = 'product';

    public function getUri()
    {
        return 'product';
    }

    public function getItemClass()
    {
        return Product::class;
    }


    public function createProduct(int $merchantId, Product $product, $keywords, $categories): Product
    {

        $brand = $product->getBrand();
        $brandUri = null;
        if (!is_null($brand)) {
            $brandUri = $brand->getResourceUri();
        }
        $data = [
            'application' => '/v1/application/' . $this->getApplicationId() . '/',
            'merchant' => '/v1/merchant/' . $merchantId . '/',
            'name' => $product->getName(),
            "product_type" => "physical",
            'description' => $product->getName(),
            'brand' => $brandUri,
            'keywords' => $keywords,
            'application_categories' => $categories
        ];

        $response = $this->sendApiRequest(
            self::HTTP_POST_OPERATION,
            self::PRODUCT_PATH . '/',
            $data,
            [
                Api::HEADER_ACCEPT_LANGUAGE => 'fr',
            ],
            [
                Api::OPTION_IS_OPERATOR => true,
            ]
        );

        return $this->serializer->deserialize($response->raw_body, Product::class, 'json');
    }

    /**
     * Get a product offer by its Id
     * @param int $id
     * @param string $language (without it custom attribute might be null)
     * @return mixed
     */
    public function getProduct(int $id, string $language)
    {
        $response = $this->sendApiRequest(
            self::HTTP_GET_OPERATION,
            self::PRODUCT_PATH . '/' . intval($id) . '/',
            [],
            [self::HEADER_ACCEPT_LANGUAGE => $language],
            [
                Api::OPTION_IS_OPERATOR => true,
            ]
        );

        return json_decode($response->raw_body, true);

    }

    /**
     * @param $id
     * @param $data
     */
    public function patchProduct($id, string $language, $data)
    {

        $this->sendApiRequest(
            self::HTTP_PATCH_OPERATION,
            self::PRODUCT_PATH . '/' . intval($id) . '/',
            $data,
            ["Accept-Language" => $language],
            [
                self::OPTION_NO_DATA_RETURN => true,
                Api::OPTION_IS_OPERATOR => true
            ],
        );

    }

    public function updateProductAddCategory(int $productId, int $categoryId, string $language)
    {
        $product = $this->getProduct($productId, $language);
        $alreadyThere = array_filter($product["application_categories"],
            function (array $cat) use ($categoryId) {
                return $cat['id'] == $categoryId;
            }
        );

        if(count($alreadyThere)==0) {
            $product["application_categories"][] = ["resource_uri" => $this->getApiUrl() . '/' . self::PRODUCT_PATH . '/' . $categoryId . '/'];
            $this->patchProduct($productId, $language, $product);
        }

    }
}
