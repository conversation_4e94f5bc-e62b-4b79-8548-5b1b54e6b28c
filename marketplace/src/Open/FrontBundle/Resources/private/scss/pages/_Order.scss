@import "../variables";
@import "~breakpoint-sass/stylesheets/breakpoint";

.account-header {
    margin: 0px 0 30px;
    padding: 0 32px;
    text-align: center;

    @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
        margin-bottom: 0;
        font-weight: bold;
        font-size: 1rem;
    }
}

.previous-page-img {
    text-align: left;
    padding: 15px 20px 15px 20px;

    a {
        font-weight: bold;
        color: $TOTAL_RED;

        &:hover {
            text-decoration: none;
            color: $TOTAL_RED;
        }
    }
}

.orders-list-empty {
    display: flex;
    justify-content: left;
    padding: 15px 0 0 5px;
}

.account-title {
    background-color: $WHITE;
    display: inline-block;
    position: relative;
    font-size: 22px;
    font-weight: bold;

    &:after {
        content: "";
        position: absolute;
        height: 2px;
        border-top: 2px solid $TOTAL_GREY;
        top: 18px;
        width: 20px;
        right: 0;
    }

    &:before {
        content: "";
        position: absolute;
        height: 2px;
        border-top: 2px solid $TOTAL_GREY;
        top: 18px;
        width: 20px;
        left: 0;
    }
}

.Page--orders {
    main.container {
        max-width: 1400px;
        background-color: $WHITE;
        width: 90%;

        @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
            width: 100%;
        }

        .order-links {
            display: flex;
            justify-content: flex-end;

            span, a {
                font-weight: 600;
            }
        }
    }
}

.table {
    width: 100%;
    border-bottom: 1px solid #d7d7d7;

    thead {
        tr {
            border: 1px solid #d7d7d7;

            th {
                font-size: 0.8125rem;
                font-weight: bold;
                color: $TOTAL_DARK_GREY2;
            }
        }
    }

    td, th {
        padding: 10px 24px;
        border-right: 1px solid #d7d7d7
    }

    td:last-child,
    th:last-child {
        border: 1px solid #ffffff;
        background-color: #fff;
    }

    .bordered {
        border: 1px solid #d7d7d7 !important;
    }

    tbody {

        tr {
            cursor: pointer;

            &:nth-child(even) {
                background-color: rgba(239, 239, 239, 0.5);
            }
        }

        td {
            font-size: 14px;
            color: $TOTAL_DARK_GREY3;
        }
    }
}

.tooltip-total {
    max-width: 100%;

    .tooltiptext {
        width: 100%;
        left: 27%;
        top: 40px;
    }
}

.order-resume-block {

    .detail-order-header-back {
        width: 100%;
        border-bottom: 2px solid $TOTAL_GREY5;
        padding: 10px 0 15px 0;
        margin-bottom: 35px;

        @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
            border-bottom: none;
            text-align: center;
            margin-bottom: 0;
            padding: 0 0 15px 0;
        }

        &-link {
            font-size: 0.8125rem;
            @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
                font-weight: bold;
                text-align: left;
                padding: 15px 20px;
                font-size: 1rem;
            }

            a {
                color: $TOTAL_DARK_GREY3;
                @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
                    color: $TOTAL_RED;
                }
            }

            a:hover {
                text-decoration: none !important;
            }
        }

        .account-title::after {
            top: 17px;
        }

        .account-title::before {
            top: 17px;
        }
    }

    .detail-order-info {

        font-size: 0.875rem;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        height: max-content;

        @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
            flex-direction: column-reverse;
            height: auto;
            margin-top: 15px;
        }

        &-col {
            display: flex;
            flex-direction: column;
            justify-content: flex-start;
            flex: 1;

            @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
                padding: 15px;

                &:first-of-type {
                    background-color: #efefef;
                }
            }

            span {
                font-weight: bold;
                padding-right: 10px;
                white-space: nowrap;
            }
        }

        &-row {
            display: flex;
            padding-bottom: 5px;

            @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
                justify-content: left !important;

                &.adress-row {
                    flex-direction: column;
                }

                &.f-dc-m {
                    flex-direction: column;
                }
            }
        }

        &-btn {

            @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
                margin-top: 20px;
            }

            a:hover {
                color: $TOTAL_DARK_GREY3;
                text-decoration: none !important;
            }

        }

    }
}

.order-detail-block {
    margin-top: 30px;
    @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
        margin-top: 0;
    }

    .supplier-resume {

        .resume-table.subtotal {
            justify-content: space-between;
            width: 15.625rem;
        }

    }

    .product-link {
        margin-left: 20px;
        line-height: 1.8;

        @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
            margin: 15px 0 0 0;
        }
    }

}

.order-summary {
    width: 100%;
    padding: 10px 30px 20px 15px;
    color: $TOTAL_DARK_GREY2;

    @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
        background-color: #f8f8f8;
        border-top: 1px solid $TOTAL_GREY6;

        &-supplier-date {
            flex-direction: column;

            section {
                justify-content: flex-start !important;
            }
        }
    }

    section {
        display: flex;
        justify-content: space-between;
        padding-top: 5px;

        span {
            font-weight: bold;
            margin-right: 5px;
        }
    }
}

.feedback-content {
    width: 90%;
    max-width: 1400px;
    margin: auto !important;
    display: flex;
    justify-content: space-between;

    &-text {
        font-size: 16px;
        font-weight: normal;
    }

    p {
        font-weight: bold;
        font-size: 12px;
    }

    .col-5 {
        padding: 30px 20px 0 20px;

        @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
            max-width: 100%;
        }

    }

    .feedback-btn {
        width: 100%;
        padding: 30px 0 40px 0;

        .btn {
            margin: 0 10px;
        }
    }

    .starrr {
        font-size: 38px;
        color: $TOTAL_YELLOW;
    }

    .feedback-comments {
        p {
            margin-bottom: 0;
        }

        form {
            padding-top: 5px;
        }

        textarea {
            border-radius: 5px;
            border: 1px solid $TOTAL_GREY4;
            color: $TOTAL_GREY6;
            height: 5rem;
            width: 100%;
        }
    }

    @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
        flex-direction: column;
    }
}


body[class*="Page--order"] {
    .w-72 {
        width: 72px !important;
    }

    .orders-table thead tr th:nth-child(8n), .orders-table thead tr th:nth-child(9n), .orders-table thead tr th:nth-child(10n) {
        border-right: 1px solid #dee2e6 !important;
    }

    main.container {
        max-width: 1400px;
        background-color: $WHITE;
        width: 90%;

        @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
            width: 100%;
        }
    }

    .orders-types-list {
        margin-top: 40px;

        &:last-child {
            border-bottom: 1px solid $TOTAL_GREY6;
            @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
                border-bottom: none;
            }
        }

        a:hover {
            text-decoration: none;
        }

    }

    .orders-type {
        width: 100%;
        border-top: 1px solid $TOTAL_GREY6;
        padding: 20px 30px 20px 15px;
        color: $TOTAL_DARK_GREY2;
        font-weight: bold;
        display: flex;
        justify-content: space-between;
    }

}

.orders-table .sorting_desc {
    &::after, &::before {
        display: none !important;
    }
}

body[class*="Page--order_"] {
    @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
        main.container {
            width: 90%;
        }
    }

    textarea#comment:disabled {
        border: none !important;
        background: transparent;
        padding: 0;
        border-radius: 0 !important;
        resize: none;
    }

    .heart-icon {
        z-index: 2;
        margin: 15px;
    }
}
