@import "../variables";
@import "~breakpoint-sass/stylesheets/breakpoint";

.Messages {
    list-style-type: none;
    padding-left: 0;
    max-width: 90%;
    margin: 20px auto 0 auto;
    position: relative;
    z-index: 10;


    @include breakpoint($BREAKPOINT_DESKTOP) {
        max-width: 80%;
    }

    .Message {
        &-item {
            padding: 35px 30px;
            position: relative;
            text-align: left;
            border: 1px solid $TOTAL_LIGHT_GREY;
            margin-top: 10px;
            -webkit-box-shadow: 0px 0px 4px rgba($TOTAL_GREY, 0.2);
            -moz-box-shadow: 0px 0px 4px rgba($TOTAL_GREY, 0.2);
            box-shadow: 0px 0px 4px rgba($TOTAL_GREY, 0.2);

            font-size: 1rem;
            font-weight: 300;
            background-color: $WHITE;
            color: $TOTAL_DARK_GREY;

            &:first-of-type {
                margin-top: 20px;
            }

            @include breakpoint($BREAKPOINT_DESKTOP) {
                max-width: 80%;
                margin: 10px auto;
            }
        }

        &-closeButton {
            position: absolute;
            right: 20px;
            top: 16px;
            width: 25px;
            height: 25px;
        }
    }

    &:empty {
        //display: none;
    }

    .Message--error {
        color: $BLACK;
        margin-top: 2px;
    }

    .Message--info,
    .Message--notice {
        color: #31708f;
        background-color: #d9edf7;
    }

    .Message--warning {
        color: #8a6d3b;
        background-color: #fcf8e3;
        border-color: #faebcc;
    }

    .Message--progress {
        color: $WHITE;
        background-color: $PRIMARY_COLOR;
        border-color: $PRIMARY_COLOR;
        margin-bottom: 20px;
        display: none;
        text-align: left;
        white-space: nowrap;
        padding: 3px;
        border-radius: 3px;
    }
}
