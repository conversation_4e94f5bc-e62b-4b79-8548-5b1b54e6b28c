<?php

namespace AppBundle\Model;

class MessageData
{
    private const STATUS_UNREAD = 'unread';

    private $id;
    private $creationDate;
    private $lastThread;
    private $supplierName;
    private $subject;
    private $threadsCount;
    /**
     * @var bool
     */
    private $unread;

    /**
     * @return mixed
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * @param mixed $id
     * @return self
     */
    public function setId($id): self
    {
        $this->id = $id;
        return $this;
    }

    /**
     * @return mixed
     */
    public function getCreationDate()
    {
        return $this->creationDate;
    }

    /**
     * @param mixed $creationDate
     * @return self
     */
    public function setCreationDate($creationDate): self
    {
        $this->creationDate = $creationDate;
        return $this;
    }

    /**
     * @return mixed
     */
    public function getLastThread()
    {
        return $this->lastThread;
    }

    /**
     * @param mixed $lastThread
     * @return self
     */
    public function setLastThread($lastThread): self
    {
        $this->lastThread = $lastThread;
        return $this;
    }

    /**
     * @return mixed
     */
    public function getSupplierName()
    {
        return $this->supplierName;
    }

    /**
     * @param mixed $supplierName
     * @return self
     */
    public function setSupplierName($supplierName): self
    {
        $this->supplierName = $supplierName;
        return $this;
    }

    /**
     * @return mixed
     */
    public function getSubject()
    {
        return $this->subject;
    }

    /**
     * @param mixed $subject
     * @return self
     */
    public function setSubject($subject): self
    {
        $this->subject = $subject;
        return $this;
    }

    /**
     * @return mixed
     */
    public function getThreadsCount()
    {
        return $this->threadsCount;
    }

    /**
     * @param mixed $threadsCount
     * @return self
     */
    public function setThreadsCount($threadsCount): self
    {
        $this->threadsCount = $threadsCount;
        return $this;
    }

    /**
     * @return bool
     */
    public function isUnread(): bool
    {
        return $this->unread;
    }

    /**
     * @param bool $unread
     * @return self
     */
    public function setUnread(bool $unread): self
    {
        $this->unread = $unread;
        return $this;
    }

}
