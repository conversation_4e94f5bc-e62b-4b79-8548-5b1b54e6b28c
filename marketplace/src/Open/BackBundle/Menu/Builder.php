<?php

namespace Open\BackBundle\Menu;

use Knp\Menu\FactoryInterface;
use Symfony\Component\DependencyInjection\ContainerAwareInterface;
use Symfony\Component\DependencyInjection\ContainerAwareTrait;
use Symfony\Contracts\Translation\TranslatorInterface;

class Builder implements ContainerAwareInterface
{
    use ContainerAwareTrait;

    private ?TranslatorInterface $translator = null;

    /***
     * Juste pour alléger la lecture du code mainMenu
     * N'apporte rien d'autre
     * @param $msg
     *
     * @return string
     */
    private function trans($msg, $parameters = [])
    {
        // On force la locale à 'en'
        if (is_null($this->translator)) {
            $this->translator = $this->container->get('translator');
        }
        return $this->translator->trans($msg, $parameters, 'AppBundle', 'en');
    }

    /***
     * construction du menu principal de l'admin
     *
     * @param \Knp\Menu\FactoryInterface $factory
     * @param array $options
     *
     * @return \Knp\Menu\ItemInterface
     */
    public function mainMenu(FactoryInterface $factory, array $options)
    {
        $menu = $factory->createItem('root');

        $user = $menu->addChild($this->trans('admin_menu.admin_users.label'));

        $user->setAttribute('dropdown', true);

        $user->addChild($this->trans('admin_menu.admin_users.user_list'), array('route' => 'admin.user.list', 'routeParameters' => array("qualifier" => "all")));

        $user->addChild($this->trans('admin_menu.admin_users.user_enabler_list'), array('route' => 'admin.user_enabler.list', 'routeParameters' => array("qualifier" => "all")));

        $user->addChild($this->trans('admin_menu.admin_users.invoice_entity_thresholds'), array('route' => 'admin.invoice_entity.thresholds', 'routeParameters' => array("qualifier" => "all")));

        if ($options['isAdmin']) {
            $user->addChild($this->trans('admin_menu.admin_users.admin_list'), array('route' => 'admin.admin.list', 'routeParameters' => array("qualifier" => "all")));
        }

        if ($options['isAdmin']) {
            $user->addChild($this->trans('admin_menu.admin_users.add_user'), array('route' => 'admin.user.add'));
        }

        //technical menu item to match all other user routes
        $user->addChild('', array('extras' => array('routes' => array(array('pattern' => '/^admin\.user.*/')))))->setDisplay(false);


        $menu->addChild($this->trans('admin_menu.merchant_menu.label',["%mkp%"=>"FRA"]), array('route' => 'admin.merchant.list', 'routeParameters' => array("mkp_discriminator"=>"FRA")));
        $menu->addChild($this->trans('admin_menu.merchant_menu.label',["%mkp%"=>"DEU"]), array('route' => 'admin.merchant.list', 'routeParameters' => array("mkp_discriminator"=>"DEU")));
        $menu->addChild($this->trans('admin_menu.merchant_menu.label',["%mkp%"=>"BEL"]), array('route' => 'admin.merchant.list', 'routeParameters' => array("mkp_discriminator"=>"BEL")));

        $pages = $menu->addChild($this->trans('admin_menu.web_content.label'));
        $pages->addChild($this->trans('admin_menu.web_content.list'), array('route' => 'admin.page.list', 'routeParameters' => array()));
        $pages->addChild($this->trans('admin_menu.web_content.add'), array('route' => 'admin.page.add', 'routeParameters' => array()));
        $pages->setAttribute('dropdown', true);

        $pages->addChild('', array('extras' => array('routes' => array(array('pattern' => '/^admin\.page.*/')))))->setDisplay(false);
        $pages->addChild($this->trans('admin_menu.other_menu.slider'), array('route' => 'admin.slider.list', 'routeParameters' => array()));

// TOTALMP 478: this feature must be hidden at this time (unused)
// TODO: Re-activate message menu feature or delete this commented lines
//    $tickets = $menu->addChild($this->trans('admin_menu.messages_menu.label'));
//    $tickets->setAttribute('dropdown', true);
//    $tickets->addChild($this->trans('admin_menu.messages_menu.list'), array('route' => 'admin.ticket.list'));
//    $tickets->addChild($this->trans('admin_menu.messages_menu.resolved'), array('route' => 'admin.ticket.list', 'routeParameters' => array('status' => 'closed')));
//    $tickets->addChild($this->trans('admin_menu.messages_menu.add'), array('route' => 'admin.ticket.create'));
// technical menu item to match all other user routes
//    $tickets->addChild('', array('extras' => array('routes' => array( array('pattern' => '/^admin\.ticket.*/')))))->setDisplay(false);

        $others = $menu->addChild($this->trans('admin_menu.other_menu.label'));
        $others->setAttribute('dropdown', true);
        $others->addChild($this->trans('admin_menu.other_menu.notifications'), array('route' => 'admin.notification.list', 'routeParameters' => array()));
        $others->addChild($this->trans('admin_menu.redirects.label'), array('route' => 'admin.redirect.list', 'routeParameters' => array("qualifier" => "all")));

        return $menu;
    }
}
