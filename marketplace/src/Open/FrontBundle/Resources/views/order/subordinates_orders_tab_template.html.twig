{% trans_default_domain 'AppBundle' %}
 {% if metaCarts|length > 0 %}
<table class="table desktop-only orders-table">
    <thead>
    <tr>
        <th>{{ 'orders.list.block.cart_number' | trans }}</th>
        <th>{{ 'orders.list.block.date_title' | trans }}</th>
        <th>{{ 'orders.list.block.address_title' | trans }}</th>
        <th>{{ 'orders.list.block.recipient' | trans }}</th>
        <th class="talign-r min-w-90">{{ 'orders.list.block.total_title' | trans }}</th>
        <th class="no-bottom-bordered"></th>
    </tr>
    </thead>
    <tbody>

    {# @var metaCartOrder \AppBundle\Entity\MetaCart #}
    {% for metaCartOrder in metaCarts %}
        <tr onclick="event.stopPropagation(); window.location ='{{ path('front.subordinatesOrder.detail', {'orderId': metaCartOrder.id }) }}';">
            <td>{{ metaCartOrder.id }} {% if metaCartOrder.gift %}
                    <img class="pl-3" src="{{ asset('images/gift.svg') }}" alt="" height="16">
                {% endif %} {% if metaCartOrder.risk %}
                    <img class="pl-3" src="{{ asset('images/ico-chimie3.svg') }}" title="{{ 'cart.risk_info' | trans }}" alt="" height="16">
                {% endif %}
            </td>
            <td>{{ metaCartOrder.orderCreatedAt | localizeddate('short', 'none', locale) }}</td>
            <td>
                {% set shippingAddress = metaCartOrder.buyerShippingAddress %}

                {% if shippingAddress is not null %}
                    {{ shippingAddress.address }}
                    {% if shippingAddress.address2 is not null %}{{ shippingAddress.address2 }}{% endif %}
                    ,
                    {{ shippingAddress.zipcode }}
                    {{ shippingAddress.city }}
                    {% set country = 'country.'~shippingAddress.country.code %}
                    {{ country | trans }}
                {% endif %}
            </td>
            <td>
                {% if shippingAddress is not null %}
                    {% if shippingAddress.contact is not null %}{{ shippingAddress.contact }}{% endif %}
                {% endif %}
            </td>
            <td class="talign-r min-w-90">{{ metaCartOrder.amount | number_format(2, '.')}} {{ metaCartOrder.currency }}</td>
            <td class="action-col" style="white-space: nowrap">
                {% if metaCartOrder.status == 'ORDERED' or metaCartOrder.status == 'REJECTED' %}
                    <div class="tooltip-total tooltip-request-validation">
                        <img src="{{ asset('images/ico-info.svg') }}" width="14" height="14">
                        <div class="tooltiptext">
                            <p>{{ (metaCartOrder.status == 'ORDERED') ? 'orders.list.validated_by' | trans : 'orders.list.rejected_by' | trans }}</p>
                            <p>{{ metaCartOrder.validatedBy.firstname }} {{ metaCartOrder.validatedBy.lastname }}</p>
                            <p>{{ (locale == 'fr') ? 'le ' : '' }}{{ metaCartOrder.validatedAt | localizeddate('short', 'none', locale) }} {{ 'generic.at' | trans }} {{ metaCartOrder.validatedAt | date('H:i') }}</p>
                        </div>
                    </div>
                 {% else %}
                     <div class="tooltip-total tooltip-request-validation" style="margin-right: 0">
                        <button class="btn btn-sm btn-success" type="button" onclick="event.stopPropagation(); window.UI.Modal.onValidate('{{ path('cart.details.accept') }}', {{ metaCartOrder.id }}, '{{ 'orders.modal.confirm_validation'|trans({}, 'AppBundle')|raw }}', '{{ 'orders.modal.confirm_error'|trans({}, 'AppBundle')|raw }}', '{{ path('front.ordersToValidate.list') }}', '{{ csrf_token('cart_accept') }}')" style="
                            height: auto !important;
                            padding: 0px 6px !important;
                            font-size: 12px;
                            letter-spacing: initial;
                        ">{{ 'btn.accept' | trans }}</button>
                         <div class="tooltiptext">
                             {{ 'orders.list.link.validate' | trans }}
                         </div>
                     </div>
                     <div class="tooltip-total tooltip-request-validation" style="margin-right: 0">
                         <button class="btn btn-sm btn-danger" type="button"  onclick="event.stopPropagation(); window.UI.Modal.onReject('{{ path('cart.details.reject') }}', {{ metaCartOrder.id }}, '{{ 'orders.modal.confirm_reject'|trans({}, 'AppBundle')|raw }}', '{{ 'wishlist.item.delete.error'|trans({}, 'AppBundle')|raw }}', '{{ path('front.ordersToValidate.list') }}', '{{ csrf_token('cart_reject') }}')" style="
                            height: auto !important;
                            padding: 0px 6px !important;
                            font-size: 12px;
                            letter-spacing: initial;
                        ">{{ 'btn.reject' | trans }}</button>
                         <div class="tooltiptext">
                             {{ 'orders.list.link.reject' | trans }}
                         </div>
                     </div>
                {% endif %}
                <a class="btn btn-default btn-sm" href="{{ path('front.subordinatesOrder.detail', {'orderId': metaCartOrder.id }) }}" style="
                            height: auto !important;
                            padding: 0px 6px !important;
                            font-size: 12px;
                            letter-spacing: initial;
                            border-color: #343639;
                            color: #343639;
                        ">{{ 'btn.detail' | trans }}</a>
            </td>
        </tr>
    {% endfor%}
    </tbody>
</table>
 {% else %}
     <section class="order-summary">
         {{ 'orders.list.tab.empty' | trans  }}
     </section>
 {% endif %}
