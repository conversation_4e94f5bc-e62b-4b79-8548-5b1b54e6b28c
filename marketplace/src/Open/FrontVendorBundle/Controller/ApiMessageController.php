<?php

namespace Open\FrontVendorBundle\Controller;

use A<PERSON><PERSON><PERSON>le\Controller\MkoController;
use AppB<PERSON>le\Entity\Merchant;
use AppBundle\Entity\User;
use AppBundle\Model\Tax\Taxes;
use AppBundle\Services\MailService;
use AppBundle\Services\MessageQuoteService;
use AppBundle\Services\QuoteService;
use Exception;
use FOS\RestBundle\Controller\Annotations as Rest;
use FOS\RestBundle\Controller\Annotations\Get;
use FOS\RestBundle\Request\ParamFetcher;
use Open\FrontVendorBundle\Exception\ApiException;
use Symfony\Component\HttpFoundation\Request;


class ApiMessageController extends MkoController
{

    /**
     * read
     * @Get( path = "/api/message/{id}",  name = "app_message_show" )
     * @Get( path = "/api/buyer/message/{id}", name = "app_buyer_message_show"  )
     *
     * @Rest\View(StatusCode = 200)
     * @throws ApiException
     */

    public function getAction(Request $request, $id, MessageQuoteService $messageQuoteService)
    {
        // TODO: check security
        $user = $this->getUser();

        try {
            $quotes = $messageQuoteService->fetchUserThread($id, $user);

        } catch (Exception $ex) {
            throw new ApiException($ex->getMessage());
        }

        if ($quotes) {
            return $quotes;
        } else {
            throw new ApiException("error.not_found", 404);
        }
    }

    /**
     * read
     * @Get( path = "/api/message/refresh/{id}",  name = "app_message_refresh" )
     * @Get( path = "/api/buyer/message/refresh/{id}",  name = "app_buyer_message_refresh" )
     *
     * @Rest\View(StatusCode = 200)
     * @throws ApiException
     */

    public function refreshMessageView($id, MessageQuoteService $messageQuoteService)
    {
        // TODO: check security
        $user = $this->getUser();
        try {
            $quotes = $messageQuoteService->fetchUserThreadRefresh($id, $user);

        } catch (Exception $ex) {
            throw new ApiException($ex->getMessage());
        }

        if (!is_null($quotes)) {
            return $quotes;
        } else {
            throw new ApiException("error.not_found", 404);
        }
    }

    /**
     *
     * @Rest\Post("/api/message/file/{threadId}",  name = "app_message_send")
     * @Rest\Post("/api/buyer/message/file/{threadId}",  name = "app_buyer_message_send")
     * @Rest\FileParam(name="files",map=true, requirements={"mimeTypes"={"application/zip","application/x-zip-compressed","application/vnd.openxmlformats-officedocument.wordprocessingml.document", "application/msword", "application/vnd.ms-excel", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "application/pdf","image/jpeg", "image/gif", "image/png"}, "maxSize"="5m"}, nullable=true)
     * @Rest\RequestParam(name="message", description="Body Message.")
     * @Rest\View(StatusCode = 201)
     * @throws ApiException
     */
    public function createAction(Request $request, ParamFetcher $paramFetcher, $threadId, QuoteService $quoteService, MessageQuoteService $messageQuoteService)
    {
        $files = ($paramFetcher->get('files'));

        $message = ($paramFetcher->get('message'));

        if (is_null($files) || count($files) == 0) {
            $files = array();
        }
        $quote = $quoteService->findQuoteByThreadId($threadId);
        $user = $this->getUser();
        $quoteMessage = $messageQuoteService->instantMessage($quote, $user, $message, ...$files);
        if ($user instanceof Merchant) {
            $quoteService->sendMail($quote, MailService::QUOTE_NEW_MESSAGE_TO_BUYER, null, $message);
        } else if ($user instanceof User) {
            $quoteService->sendMail($quote, null, MailService::QUOTE_NEW_MESSAGE_TO_VENDOR, $message);
        }
        return $quoteMessage;


    }
}
