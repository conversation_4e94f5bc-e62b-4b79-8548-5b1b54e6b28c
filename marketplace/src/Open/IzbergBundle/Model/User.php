<?php

namespace Open\IzbergBundle\Model;

use JMS\Serializer\Annotation as JSM;

class User
{
    /**
     * @JSM\Type("integer")
     */
    private $id = 0;

    /**
     * @return mixed
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * @param mixed $id
     */
    public function setId($id): self
    {
        $this->id = $id;

        return $this;
    }
}
