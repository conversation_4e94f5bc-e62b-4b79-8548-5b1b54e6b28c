<?php

namespace Open\IdealBundle\Model;

class WorkingLocation
{
    private ?string $Id = null;
    private ?string $Name = null;
    private ?string $Name_fr = null;
    private ?string $Street = null;
    private ?string $Street2 = null;
    private ?string $Street3 = null;
    private ?string $Cedex = null;
    private ?string $PostalCode = null;
    private ?string $Locality = null;
    private ?ParentLocation $ParentLocation = null;

    /**
     * @return string|null
     */
    public function getId(): ?string
    {
        return $this->Id;
    }

    /**
     * @param string|null $Id
     * @return WorkingLocation
     */
    public function setId(?string $Id): WorkingLocation
    {
        $this->Id = $Id;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getName(): ?string
    {
        return $this->Name;
    }

    /**
     * @param string|null $Name
     * @return WorkingLocation
     */
    public function setName(?string $Name): WorkingLocation
    {
        $this->Name = $Name;
        return $this;
    }

    /**
     * @return ParentLocation|null
     */
    public function getParentLocation(): ?ParentLocation
    {
        return $this->ParentLocation;
    }

    /**
     * @param ParentLocation|null $ParentLocation
     * @return WorkingLocation
     */
    public function setParentLocation(?ParentLocation $ParentLocation): WorkingLocation
    {
        $this->ParentLocation = $ParentLocation;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getStreet(): ?string
    {
        return $this->Street;
    }

    /**
     * @param string|null $Street
     * @return WorkingLocation
     */
    public function setStreet(?string $Street): WorkingLocation
    {
        $this->Street = $Street;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getPostalCode(): ?string
    {
        return $this->PostalCode;
    }

    /**
     * @param string|null $PostalCode
     * @return WorkingLocation
     */
    public function setPostalCode(?string $PostalCode): WorkingLocation
    {
        $this->PostalCode = $PostalCode;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getNameFr(): ?string
    {
        return $this->Name_fr;
    }

    /**
     * @param string|null $Name_fr
     * @return WorkingLocation
     */
    public function setNameFr(?string $Name_fr): WorkingLocation
    {
        $this->Name_fr = $Name_fr;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getStreet2(): ?string
    {
        return $this->Street2;
    }

    /**
     * @param string|null $Street2
     * @return WorkingLocation
     */
    public function setStreet2(?string $Street2): WorkingLocation
    {
        $this->Street2 = $Street2;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getStreet3(): ?string
    {
        return $this->Street3;
    }

    /**
     * @param string|null $Street3
     * @return WorkingLocation
     */
    public function setStreet3(?string $Street3): WorkingLocation
    {
        $this->Street3 = $Street3;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getCedex(): ?string
    {
        return $this->Cedex;
    }

    /**
     * @param string|null $Cedex
     * @return WorkingLocation
     */
    public function setCedex(?string $Cedex): WorkingLocation
    {
        $this->Cedex = $Cedex;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getLocality(): ?string
    {
        return $this->Locality;
    }

    /**
     * @param string|null $Locality
     * @return WorkingLocation
     */
    public function setLocality(?string $Locality): WorkingLocation
    {
        $this->Locality = $Locality;
        return $this;
    }



}