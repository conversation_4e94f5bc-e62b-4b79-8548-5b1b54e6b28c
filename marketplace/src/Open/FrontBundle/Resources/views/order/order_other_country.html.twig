{% extends '@OpenFront/menu/buyer_menu.html.twig' %}
{% trans_default_domain 'AppBundle' %}
{# @var order \AppBundle\Model\Cart\Cart #}
{# @var merchantOrder \AppBundle\Model\\AppBundle\Model\Order\MerchantOrder#}

{% block title %}{{ 'orders.detail.order_detail' | trans }} {{ order_id }}{% endblock %}

{% block body %}

    {% block searchbar %}
        {% if formSearchBar is defined %}
            {% include('@OpenFront/component/search-bar.html.twig') %}
        {% else %}
            {{ render(controller("Open\\FrontBundle\\Controller\\SearchBarController::formAction")) }}
        {% endif %}
    {% endblock %}

    <div class="Page-cart-inner Page-order-detail w-90 max-1400">
        <div class="order-resume-block">
            <div class="detail-order-header-back">
                <section class="detail-order-header-back-link desktop-only">
                    <a class="previous-page" href="{{ back_link }}"><i class="arrow left"></i> {{ 'orders.detail.go_back'|trans }}</a>
                </section>
                <section class="detail-order-header-back-link mobile-only">
                    <a class="previous-page" href="{{ back_link }}">
                        <img alt="go back" src="{{ asset('images/red-arrow.svg') }}">
                        <img alt="go back" src="{{ asset('images/red-arrow.svg') }}">
                        {{ 'orders.list.back'|trans }}
                    </a>
                </section>
                <div style="text-align:center" class="mobile-only">
                    <section class="account-header account-title">{{ 'orders.detail.mobile_title' | trans }}</section>
                </div>
            </div>
            <div class="order-detail-block">
                <div class="text-center">
                    <div class="pb-2">
                        <svg class="icon-warning mr-1"></svg>
                        {{ 'error.notfound.merchant_order_not_found' | trans }}
                    </div>
                </div>
            </div>
        </div>
    </div>
{% endblock %}

