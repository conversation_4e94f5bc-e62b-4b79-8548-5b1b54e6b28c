{% extends '@OpenBack/base.html.twig' %}
{% trans_default_domain "AppBundle" %}
{% block body %}

    <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.7.1/css/bootstrap-datepicker.css" rel="stylesheet" type="text/css" />
    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.7.1/js/bootstrap-datepicker.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.7.1/locales/bootstrap-datepicker.fr.min.js"></script>


    <div>
        <h4>{{ 'back.company.filter_title' | trans }}</h4>
    </div>
    <div class="form-container company-filter">
        {{ form_start(form, {'class': 'myclass', 'attr': {'class': 'form-inline'}}) }}
        <div class="input-group col-md-5">
            {{ form_label(form.datemin) }}
            {{ form_errors(form.datemin) }}
            {{ form_widget(form.datemin, {'attr' : {'class' : 'resetable_input form-control col-md-3 datepicker'}}) }}
            {{ form_label(form.datemax) }}
            {{ form_errors(form.datemax) }}
            {{ form_widget(form.datemax, {'attr' : {'class' : 'resetable_input form-control col-md-3 datepicker'}}) }}
        </div>
        <div class="input-group col-md-3">
            {{ form_widget(form.save, {'attr' : {'class' : 'btn-info'}}) }}
            <button id="clearForm" class="btn btn-outline-dark" type="button">{{ 'back.company.filter_clear' | trans }}</button>
        </div>
    </div>

    <div class="d-flex export-btn">
        <a href="#" id="exportBtn" class="btn btn-sm btn-primary btn-dark" role="button" aria-pressed="true">{{ 'back.commons.export_csv'|trans }}</a>
    </div>

    <div>
        <table class="table table-hover">
            <thead>
            <tr>
                {# sorting of properties based on query components #}
                <th>{{ knp_pagination_sortable(pagination, 'back.search_historization.id'|trans, 'e.id') }}</th>
                <th>{{ knp_pagination_sortable(pagination, 'back.search_historization.date'|trans, 'e.createdAt') }}</th>
                <th>{{ knp_pagination_sortable(pagination, 'back.search_historization.is_anonymous'|trans, 'e.is_anonymous') }}</th>
                <th>{{ knp_pagination_sortable(pagination, 'back.search_historization.searched_term'|trans, 'e.search_term') }}</th>
                <th>{{ knp_pagination_sortable(pagination, 'back.search_historization.filter_label'|trans, 'e.search_term') }}</th>
                <th>{{ knp_pagination_sortable(pagination, 'back.search_historization.nb_hits'|trans, 'e.nb_hits') }}</th>
            </tr>
            <tr>
                <th>{{ form_widget(form.id, {'attr': {'class': ' input_reset form-control','style': 'width:50px'}}) }}</th>
                <th></th>
                <th>{{ form_widget(form.isAnonymous, {'attr' : {'class' : 'input_reset form-control filter_select'}})}}</th>
                <th>{{ form_widget(form.searchedTerm, {'attr' : {'class' : 'input_reset form-control col-md-6'}}) }}</th>
                <th>{{ form_widget(form.filter, {'attr' : {'class' : 'input_reset form-control col-md-6'}}) }}</th>
                <th>{{ form_widget(form.nbHits, {'attr' : {'class' : 'input_reset form-control col-md-2'}}) }}</th>
            </tr>
            </thead>
            {% for search in pagination %}
                <tr {% if loop.index is odd %}class="color"{% endif %}>
                    <td>{{ search.id }}</td>
                    <td>{{ search.createdAt| localizeddate('short', 'none', locale) }}</td>
                    {% if search.isAnonymous == 1 %}
                        <td>Yes</td>
                    {% else %}
                        <td>No</td>
                    {% endif %}
                    <td>{{ search.searchTerm }}</td>
                    <td>{{ search.filter }}</td>
                    <td>{{ search.nbHits }}</td>
                </tr>
            {% endfor %}
        </table>
    </div>
    <div class="d-flex justify-content-left">
        <div class="navigation col-md-10">
            {{ knp_pagination_render(pagination, null, query) }}
        </div>
        <div class="col-md-2">Total: {{ pagination.getTotalItemCount }}</div>
    </div>
    <script type="application/javascript">
        document.addEventListener('DOMContentLoaded', function() {

            $("#exportBtn").click(function() {
                document.filter_search_historization.action= '{{ path('search_historization.export') }}';
                $('form[name="filter_search_historization"]').submit();
                document.filter_search_historization.action= '';
            });

            $("#clearForm").click(function() {
                $(".input_reset").val("");
                $(".resetable_input").val("");
                $('form[name="filter_search_historization"]').submit();
            });

            $(".filter_select").change(function() {
                $("form").submit();
            });
        })
    </script>
{% endblock %}