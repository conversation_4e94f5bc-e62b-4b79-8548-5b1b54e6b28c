@import "../variables";

body[class*="Page Page--quote"], body[class*="Page Page--nl_quote"] {
    .d-flex {
        display: flex;
    }

    .justify-content-between {
        justify-content: space-between;
    }

    .m-0 {
        margin: 0 !important;
    }

    .mt-20 {
        margin-top: 20px;
    }

    .mt-40 {
        margin-top: 40px;
    }

    .mb-20 {
        margin-bottom: 20px !important;
    }

    .mb-30 {
        margin-bottom: 30px !important;
    }

    table {
        border: none !important;
        border-bottom: 1px solid #d7d7d7 !important;
    }

    table th:first-child {
        border-left: 1px solid #d7d7d7 !important;
    }

    table th:last-child {
        border: none !important;
        display: none !important;
    }

    .quote-table {
        width: 100% !important;
    }

    .quote-table th {
        border: 1px solid #d7d7d7 !important;
        border-left: none !important;
        padding: 0 10px !important;
        height: 35px;
        line-height: 35px;
        font-size: 13px;
        font-weight: bold;
        color: #343639;
    }

    .quote-table tr:nth-child(2n) {
        background-color: rgba(239, 239, 239, 0.5);
    }

    .quote-table td {
        border-right: 1px solid #d7d7d7 !important;
    }

    .quote-table td:nth-child(5n) {
        border-right: none !important;
    }

    .quote-table td:nth-child(6n) {
        border: none !important;
        display: none !important;
    }

    .previous-page {
        width: 150px;
        height: 15px;
        object-fit: contain;
        font-size: 14px;
        font-weight: normal;
        font-stretch: normal;
        font-style: normal;
        line-height: normal;
        letter-spacing: normal;
        color: #343639;
        text-decoration: none;
    }

    .divider {
        width: 100%;
        border-bottom: solid 1px #acb0b2;
        margin: 10px 0;
    }

    .detail-quote-info-row .label, .detail-quote-info-row .value {
        width: 364px;
        height: 27px;
        object-fit: contain;
        font-size: 14px;
        font-weight: normal;
        font-stretch: normal;
        font-style: normal;
        line-height: 1.93;
        letter-spacing: normal;
        color: #343639;
    }

    .detail-quote-info-row .label {
        font-weight: bold;
    }

    .detail-info-container {
        object-fit: contain;
        border: solid 1px #d7d7d7;
        background-color: #ffffff;
        padding: 20px 30px;
    }

    .border-bottom {
        border-bottom: solid 1px #d7d7d7 !important;
    }

    .open-tab-icon {
        width: 20px;
        height: 20px;
        cursor: pointer;
    }

    .open-tab-icon.collapsed {
        transform: rotate(180deg);
    }

    .title {
        object-fit: contain;
        font-size: 15px;
        font-weight: bold;
        font-stretch: normal;
        font-style: normal;
        line-height: normal;
        letter-spacing: normal;
        color: #343639;
    }

    .dotted-divider {
        width: 100%;
        margin-bottom: 10px;
        border-bottom: 1px dashed #acb0b2;
    }

    .offer-container {
        display: flex;
    }

    .offer-picture {
        width: 140px;
        height: 140px;
        object-fit: contain;
        margin-right: 20px;
    }

    .offer-name {
        margin-bottom: 0;
        padding-bottom: 0;
        object-fit: contain;
        font-size: 17px;
        font-weight: bold;
        font-stretch: normal;
        font-style: normal;
        line-height: normal;
        letter-spacing: normal;
        color: #353535;
    }

    .offer-ref {
        margin: 5px 0 0 10px;
        padding-top: 0;
        object-fit: contain;
        font-size: 13px;
        font-weight: bold;
        font-stretch: normal;
        font-style: normal;
        line-height: normal;
        color: #b1b1b1;
    }

    .offer-description {
        margin-top: 15px;
        object-fit: contain;
        font-size: 16px;
        font-weight: 300;
        font-stretch: normal;
        font-style: normal;
        line-height: 1.5;
        letter-spacing: normal;
        color: #353535;
    }

    .detail-quote-info {
        margin-bottom: 30px;
    }

    .mr-10 {
        margin-right: 10px;
    }

    .label {
        text-transform: none !important;
    }

    .quote-pdf-container {
        position: relative;
        right: 20px;
        width: 420px;
    }

    .quote-pdf-label {
        position: relative;
        width: 220px;
        object-fit: contain;
        font-size: 14px;
        font-weight: normal;
        font-stretch: normal;
        font-style: normal;
        line-height: 1.93;
        letter-spacing: normal;
        color: #343639;
    }

    .download-pdf-btn {
        position: relative;
        top: -7px;
        object-fit: contain;
        font-size: 13px;
        font-weight: bold;
        font-stretch: normal;
        font-style: normal;
        line-height: 36px;
        letter-spacing: normal;
        color: #343639;
        width: 189px;
        height: 35px;
        border-radius: 5px;
        border: solid 1px #343639;
        cursor: pointer;
    }

    .download-pdf-btn-icon {
        margin: 10px 10px 0 10px;
        display: inline-block;
        width: 16px;
        height: 16px;
        background-size: 100% 100% !important;
    }

    .download-pdf-btn-label {
        position: absolute;
        top: 0;
    }

    .table-resume-divider {
        width: 270px;
        border-top: 3px solid $TOTAL_RED;
        padding-top: 20px;
        object-fit: contain;
        float: right;
        display: flex;
        justify-content: space-between;
    }

    .table-resume-label, .table-resume-value {
        clear: both;
        object-fit: contain;
        font-size: 16px;
        font-weight: bold;
        font-stretch: normal;
        font-style: normal;
        line-height: 1.5;
        letter-spacing: normal;
        text-align: right;
        color: #353535;
    }

    .quote-comment-content {
        clear: both;
        margin-top: 30px;
        font-size: 16px;
        font-weight: 300;
        line-height: 1.5;
        color: #353535;
    }

    .quote-comment-content section {
        margin-bottom: 30px;
    }

    .btn-container {
        display: flex;
        justify-content: center;
    }

    .add-to-cart-btn, .discuss-quote-btn, .cancel-order-btn {
        width: 235px;
        height: 45px;
        margin: 10px;
        border-radius: 5px;
        font-size: 14px;
        font-weight: bold;
        font-stretch: normal;
        font-style: normal;
        line-height: normal;
        text-align: center;
        background-color: #ffffff;
    }

    .add-to-cart-btn {
        color: #ffffff;
        background-color: $TOTAL_RED;
    }

    .discuss-quote-btn {
        color: $TOTAL_RED;
        border: solid 1px $TOTAL_RED;
    }

    .cancel-order-btn {
        color: #353535;
        border: solid 1px #353535;
    }

    .quote-version-list-header {
        height: 34px;
        object-fit: contain;
        border: solid 1px #d7d7d7;
        background-color: #ffffff;
    }

    .quote-version-list-header p {
        margin: 0;
        padding: 6px 20px;
        font-size: 13px;
        font-weight: bold;
        color: #343639;
    }

    .quote-version-list-row {
        height: 34px;
        display: flex;
        justify-content: space-between;
    }

    .quote-version-list-row p {
        margin: 0;
        padding: 6px 20px;
        font-size: 15px;
        color: #343639;
    }

    .quote-download-link {
        margin: 0;
        font-size: 15px;
        font-style: italic;
        color: #134391;
    }

    .text-center {
        text-align: center;
    }

    #ongoing {
        color: $TOTAL_RED
    }

    .price-max-error {
        color: $TOTAL_RED;
        padding: 0 20px;
        width: 80%;
        margin: auto;
        text-align: center;
    }

    #toggleMessage {
        display: inline-block;
        color: #fff;
        background: #134391;
        height: 57px;
        width: 57px;
        text-align: center;
        padding: 13px;
        border-radius: 50%;
        margin-top: 15px;

        &:hover {
            cursor: pointer;
        }
    }
}
