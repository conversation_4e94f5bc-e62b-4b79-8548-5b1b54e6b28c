<?php

namespace Open\IzbergBundle\Model;

use <PERSON><PERSON>\Serializer\Annotation as JSM;
use <PERSON><PERSON>\Serializer\Annotation\Type;

/**
 * @JSM\ExclusionPolicy("none")
 */
class Meta
{
    /**
     * @Type("int")
     * @var int
     */
    private $limit;

    /**
     * @Type("string")
     * @var ?string
     */
    private ?string $next;

    /**
     * @Type("int")
     * @var int
     */
    private $offset;

    /**
     * @Type("string")
     * @var ?string
     */
    private $previous;

    /**
     * @Type("int")
     * @var int
     */
    private $total_count;

    public function getLimit(): ?int
    {
        return $this->limit;
    }

    public function setLimit(int $limit): void
    {
        $this->limit = $limit;
    }

    /**
     * @return string|null
     */
    public function getNext(): ?string
    {
        return $this->next;
    }

    /**
     * @param string|null $next
     */
    public function setNext(?string $next): void
    {
        $this->next = $next;
    }



    public function getOffset(): ?int
    {
        return $this->offset;
    }

    public function setOffset(int $offset): void
    {
        $this->offset = $offset;
    }

    public function getPrevious(): ?string
    {
        return $this->previous;
    }

    public function setPrevious(string $previous): void
    {
        $this->previous = $previous;
    }

    public function getTotalCount(): int
    {
        return $this->total_count;
    }

    public function setTotalCount(int $total_count): void
    {
        $this->total_count = $total_count;
    }
}