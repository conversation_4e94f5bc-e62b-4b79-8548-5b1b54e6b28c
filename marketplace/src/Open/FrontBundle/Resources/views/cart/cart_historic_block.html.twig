{% import '@OpenFront/shared/utils.html.twig' as utils %}

{% trans_default_domain 'AppBundle' %}

{% set locale = app.request.locale %}

<div class="historic-block">
    <div class="row">
        <div class="comment-id">{{ id }}</div>
        <div class="date">
            <span class="title">{{ 'cart.historic.date_assign'|trans }}&nbsp;: </span>
            <span class="value">{{ element.date|localizeddate("short", "none", locale) }}</span>
        </div>
    </div>

    <div class="row user-assigned">
        <span class="title">{{ 'cart.historic.user_who'|trans }}&nbsp;: </span>
        <span class="value">{{ element.user.firstname|upper }} {{ element.user.lastname|upper }} [{{ utils.displayRoleName(element.user.roles) }}]</span>
    </div>

    <div class="row user-who">
        <span class="title">{{ 'cart.historic.user_assigned'|trans }}&nbsp;: </span>
        <span class="value">{{ element.assignedUser.firstname|upper }} {{ element.assignedUser.lastname|upper }}</span>
    </div>

    <div class="row comment">
        <span class="title">{{ 'cart.historic.comment'|trans }}&nbsp;: <span class="value">{{ element.comment }}</span></span>
    </div>
</div>