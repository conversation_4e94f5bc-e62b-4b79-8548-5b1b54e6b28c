FROM php:8.0-fpm

# php extension installed with mlocati/docker-php-extension-installer https://github.com/mlocati/docker-php-extension-installer
ADD https://github.com/mlocati/docker-php-extension-installer/releases/latest/download/install-php-extensions /usr/local/bin/

RUN set -x &&\
    chmod +x /usr/local/bin/install-php-extensions && sync && \
    install-php-extensions redis &&\
    install-php-extensions yaml &&\
    install-php-extensions gd &&\
    install-php-extensions pdo_mysql &&\
    install-php-extensions intl &&\
    install-php-extensions mysqli &&\
    install-php-extensions zip &&\
    install-php-extensions soap &&\
    install-php-extensions bcmath &&\
    install-php-extensions sockets &&\
    install-php-extensions @composer &&\
    install-php-extensions apcu &&\
    install-php-extensions opcache &&\
    install-php-extensions xdebug &&\
    install-php-extensions amqp

# install other php dependencies and symfony client
RUN set -x &&\
  apt-get -y update &&\
  apt-get -y install gnupg zip gzip libzip-dev wget &&\
  wget https://get.symfony.com/cli/installer -O - | bash && \
  mv /root/.symfony/bin/symfony /usr/local/bin/symfony

# install blackfire
RUN set -x &&\
#    wget https://packages.blackfire.io/binaries/blackfire-agent/1.44.1/blackfire-agent-linux_amd64 &&\
#    mv blackfire-agent-linux_amd64 blackfire-agent &&\
#    chmod +x blackfire-agent &&\
#    mv blackfire-agent /usr/local/bin/ &&\
    wget https://packages.blackfire.io/binaries/blackfire-php/1.49.0/blackfire-php-linux_amd64-php-80.so &&\
    mv blackfire-php-linux_amd64-php-80.so blackfire.so &&\
    chmod +x blackfire.so &&\
    mv blackfire.so /usr/local/lib/php/extensions/no-debug-non-zts-20200930/ &&\
    echo [blackfire] >> /usr/local/etc/php/conf.d/docker-php-ext-blackfire.ini &&\
    echo extension=blackfire.so >> /usr/local/etc/php/conf.d/docker-php-ext-blackfire.ini &&\
    echo blackfire.agent_socket=tcp://blackfire:8707 >> /usr/local/etc/php/conf.d/docker-php-ext-blackfire.ini

# copy and run bash scripts
COPY setup.sh /usr/local/bin/
RUN chmod +x /usr/local/bin/setup.sh

COPY blackfire-entrypoint.sh /usr/local/bin/
RUN chmod +x /usr/local/bin/blackfire-entrypoint.sh

# php config
RUN cp /usr/local/etc/php/php.ini-development /usr/local/etc/php/php.ini \
    && sed -i 's/memory_limit = 128M/memory_limit = 256M/' /usr/local/etc/php/php.ini

# add user
RUN usermod -u 1000 www-data

# create symfony cache folders
RUN mkdir -p /var/cache/symfony && chown -R www-data:www-data /var/cache/symfony && chown -R www-data:www-data /var/www

# composer config
RUN mkdir -p /composer
ENV COMPOSER_HOME="/composer"
ENV PATH=$PATH:$COMPOSER_HOME/vendor/bin/

# install composer global dependecies
RUN composer global require 'squizlabs/php_codesniffer ~3.6'
RUN composer global require 'vimeo/psalm ^4.5'
RUN composer global require 'psalm/plugin-symfony ^2.1'
RUN composer global require 'roave/psalm-html-output ^1.0'

# change composer home owner
RUN chown -R  www-data:www-data $COMPOSER_HOME

# expose port 9000 and start php-fpm server
EXPOSE 9000
CMD ["php-fpm"]
