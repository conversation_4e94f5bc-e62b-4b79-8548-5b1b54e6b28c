<?php

namespace Open\LogBundle\EventSubscriber;

use Open\LogBundle\Service\LogService;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\Security\Core\AuthenticationEvents;
use Symfony\Component\Security\Core\Event\AuthenticationFailureEvent;
use Symfony\Component\Security\Http\Event\LoginSuccessEvent;

class LogAccountEventSubscriber implements EventSubscriberInterface
{
    const ON_FILTER_USER_RESPONSE_EVENT = 'onFilterUserResponseEvent';

    private LogService $logger;

    public function __construct(LogService $logger)
    {
        $this->logger = $logger;
    }

    public static function getSubscribedEvents(): array
    {
        // Catch login actions to trigger logs actions
        return array(
            'SECURITY_IMPLICIT_LOGIN' => 'onUserEvent',
            LoginSuccessEvent::class => 'onInteractiveLogin',
            AuthenticationEvents::AUTHENTICATION_FAILURE => 'onAuthenticationFailure',
            'REGISTRATION_CONFIRMED' => self::ON_FILTER_USER_RESPONSE_EVENT,
            'REGISTRATION_SUCCESS' => 'onUserFormEvent',
            'RESETTING_RESET_COMPLETED' => self::ON_FILTER_USER_RESPONSE_EVENT,
            'PROFILE_EDIT_COMPLETED' => self::ON_FILTER_USER_RESPONSE_EVENT
        );
    }


    public function onInteractiveLogin(LoginSuccessEvent $event, string $eventName)
    {
        // Retrieve current user
        $user_name = $event->getUser()->getUserIdentifier();

        $this->logger->info("InteractiveLogin : ", $eventName, $user_name, []);

    }

    public function onUserEvent($event, $eventName)
    {
        $this->logger->info("UserEvent : ", $eventName, $event->getUser()->getUsername(), []);
    }


    public function onUserFormEvent($event, $eventName)
    {
        $this->logger->info("UserFormEvent : ", $eventName, $event->getForm()->getData()->getUsername(), []);
    }

    public function onAuthenticationFailure(AuthenticationFailureEvent $event, string $eventName)
    {
        $this->logger->info("AuthenticationFailure : ", $eventName, $event->getAuthenticationToken()->getUsername(), []);
    }

    public function onFilterUserResponseEvent($event, $eventName)
    {
        $this->logger->info("FilterUserResponseEvent : ", $eventName, $event->getUser()->getUsername(), []);
    }
}
