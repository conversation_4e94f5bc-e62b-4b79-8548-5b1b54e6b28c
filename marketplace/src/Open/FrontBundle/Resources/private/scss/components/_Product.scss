@import "../variables";
@import "~breakpoint-sass/stylesheets/breakpoint";

.Product {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    width: 25%;
    min-height: 300px;
    background-color: $WHITE;
    padding: 20px 30px;
    margin: 0;
    color: $TOTAL_GREY;
    border: 1px solid $TOTAL_GREY3;

    @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
        width: 100%;
        margin-bottom: 10px;
        text-align: center;
    }

    &-compare a {
        display: flex;
        align-items: center;
        font-weight: 300;
        color: $TOTAL_GREY;

        .Icon {
            fill: $TOTAL_MIDDLE_BLUE;
            margin: 0 10px 0 0;
            width: 17px;
            height: 17px;
        }
    }

    &-body {
        text-align: left;
    }

    &-reference, &-incoterm {
        text-transform: uppercase;
        letter-spacing: 1.5px;
        font-size: 13px;
        font-weight: 700;
        color: $TOTAL_GREY8;
    }

    &-image {
        width: 100%;
        height: 160px;
        margin: 15px 0;
        background-repeat: no-repeat;
        background-size: contain;
        background-position: center;
    }

    .tooltip-product-card-location {
        max-width: 100%;

        .tooltiptext {
            width: 100%;
            left: 27%;
            top: 40px;
        }
    }

    &-name {
        margin: 5px 0;
        word-wrap: break-word;
        text-overflow: ellipsis;
        font-size: 17px;

        a {
            color: $TOTAL_DARK_GREY3;
            width: 200px;
            font-size: 1.0625rem;
            font-weight: 600;

            &:hover {
                color: $TOTAL_BLUE;
                text-decoration: none;
            }
        }
    }

    .h-line {
        width: 45px;
        height: 3px;
        background-color: $TOTAL_GREY7;
        margin: 5px 0;
    }

    &-info {
        color: $TOTAL_DARK_GREY3;
        font-weight: normal;
        font-size: 16px;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        line-height: 2;
    }

    &-price {
        font-size: 16px;
        font-weight: 500;
        line-height: 32px;
        display: flex;
        flex-direction: column;
        white-space: nowrap;
        padding-left: 10px;

        .sku-info {
            color: $TOTAL_DARK_GREY;
            font-size: 0.75rem;
            line-height: 0.75rem;
            margin-top: 5px;
            text-transform: uppercase;
        }

    }

    &-merchant-rating {

    }

    @include breakpoint($BREAKPOINT_MOBILE_MAX) {
        margin-bottom: 0;
        padding: 15px 20px;

        .display-mobile {
            display: flex;
            align-items: center;
            justify-content: space-between;

            .Product-price {
                margin-bottom: 0;
            }
        }
    }
}