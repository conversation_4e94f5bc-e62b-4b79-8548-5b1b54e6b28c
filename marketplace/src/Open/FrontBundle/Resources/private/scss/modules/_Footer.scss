@import "../variables";
@import "~breakpoint-sass/stylesheets/breakpoint";

.Footer {
    background: $WHITE;
    display: flex;
    flex-direction: row;
    -ms-flex-align: center;
    width: 100%;
    justify-content: center;
    bottom: 0;
    color: $TOTAL_DARK_GREY3;

    &.connected {
        flex-direction: column !important;
        min-height: 210px;

        .footer-logo--home img {
            width: 200px;
        }

        section {
            border: none !important;
        }

        .additional-content {
            width: 100%;
            margin: auto;
            background-color: $TOTAL_LIGHT_GREY;
            font-size: 14px;
            height: 60px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;

            @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
                flex-direction: column;
            }
        }
    }

    .footer-border {
        height: 20px;
        background-color: $TOTAL_LIGHT_GREY;
        width: 100%;
        max-width: 100%;

        @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
            display: none;
        }
    }

    section {
        padding-top: 10px;
        border-top: 2px solid rgba(77, 79, 82, 0.31);
        display: flex;
        justify-content: space-between;
        flex-direction: row;
        width: 90%;
        max-width: 1400px;

        @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
            flex-direction: column;
        }
    }

    .v-line {
        width: 1px !important;
        height: 15px;
        margin: auto 0 auto 0;
    }

    .h-line {
        width: 100%;
        height: 1px;
        background-color: $TOTAL_DARKER_GREY !important;
    }

    .footer-connected {
        padding: 40px 0 40px 0;
        border-top: 2px solid rgba(77, 79, 82, 0.31);
        display: flex;
        justify-content: space-between;
        flex-direction: column;
        width: 90%;
        margin: auto;
        max-width: 1400px;

        @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
            width: 100%;
        }

        .yammer-account {
            background-image: url('/images/yammer.png');
            background-position: 50%;
            width: 35px;
            height: 35px;
            border-radius: 5px;

            @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
                margin-top: 20px;
            }
        }
    }

    .footer-links {
        width: 400px;
        display: flex;
        justify-content: space-between;
        text-transform: uppercase;
        font-size: 13px !important;

        div {
            padding: 10px 0 10px 0;
        }

        a {
            color: $TOTAL_DARKEST;
            font-weight: 500 !important;
        }

        a:hover {
            text-decoration: none;
        }

        @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
            width: 100%;
            flex-direction: column;
            align-items: center;
        }

        &-connected {
            display: flex;
            text-transform: uppercase;
            flex-direction: row;
            justify-content: space-between;
            font-size: 12px;
            align-items: center;

            @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
                width: 100% !important;
                padding: 10px 0 10px 0;
                flex-direction: column !important;
                align-items: center !important;

                div {
                    padding: 10px;
                }
            }

            div {
                display: flex;
                margin: 0;
                text-align: center;
            }

            a {
                color: $TOTAL_DARKEST;
                font-weight: 500 !important;
            }

            a:hover {
                text-decoration: none;
            }

        }

    }

    .container-logo {
        padding: 15px 0 0 45px;

        .footer-logo {
            max-width: 170px;
        }
    }

    .footer-pair-section {
        display: flex;
    }


    .additional-content {
        font-size: 13px;
        color: $TOTAL_DARKEST;

        p {
            font-weight: 300;
        }

        .company {
            color: $WHITE;
            font-weight: 400;
        }
    }


    @include breakpoint($BREAKPOINT_DESKTOP) {

        .footer-logo {
            margin-left: 50px;
        }

        .additional-content {
            width: 125px;
        }

        .content {
            max-width: 1200px;
            margin: 80px auto 50px;
            z-index: 10;

            .h-line {
                margin: 15px 0 20px;
                background-color: $WHITE;
                opacity: 0.1;
            }
        }
    }

    @include breakpoint($BREAKPOINT_NOT_DESKTOP) {

        .v-line {
            margin: auto 10px auto 11px;
            padding: 0 !important;
        }

        .input-email {
            display: flex;
            align-items: center;
            height: 48px !important;
        }

        .hide-mobile {
            display: none !important;
        }

        .additional-content {
            display: flex;
            flex-direction: column;
            align-items: center;
            margin: 20px;

            .company {
                color: $TOTAL_GREY;
                font-weight: 300;
            }
        }
    }

    @media print {
        background-color: $TOTAL_DARK_PURPLE;
        -webkit-print-color-adjust: exact;
    }

}

@include breakpoint($BREAKPOINT_NOT_DESKTOP) {
    .inverse-mobile {
        display: flex;
        flex-direction: column-reverse !important;
        margin-bottom: 0;
    }
}
