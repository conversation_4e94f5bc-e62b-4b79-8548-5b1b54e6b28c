<?php

namespace Open\IzbergBundle\Model;

use Countable;

abstract class IzbergResponse implements Countable
{
    /**
     * @var Meta
     */
    protected Meta $meta;

    private array $objects = [];

    public function getMeta(): Meta
    {
        return $this->meta;
    }

    public function setMeta(Meta $meta): self
    {
        $this->meta = $meta;
        return $this;
    }

    public function getObjects(): array
    {
        return $this->objects;
    }

    public function setObjects(array $objects): self
    {
        $this->objects = $objects;
        return $this;
    }

    public function count(): int
    {
        return count($this->objects);
    }
}
