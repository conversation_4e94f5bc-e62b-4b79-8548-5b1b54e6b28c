<?php
/**
 * Created by PhpStorm.
 * User: CMA15450
 * Date: 18/01/2018
 * Time: 13:38
 */

namespace Open\BackBundle\Form;

use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\IntegerType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;


class FilterPageType extends AbstractType
{

	public function buildForm(FormBuilderInterface $builder, array $options)
	{
		$builder
			->add(
				'id',
                TextType::class
			)

		;


	}

	/**
	 * @return string
	 */
	public function getName()
	{
		return 'page_filter';
	}

	/**
	 * Configures the options for this type.
	 *
	 * @param OptionsResolver $resolver The resolver for the options.
	 */
	public function configureOptions(OptionsResolver $resolver)
	{
		$resolver->setDefaults(
			[
				'csrf_protection' => false
			]
		);
	}
}