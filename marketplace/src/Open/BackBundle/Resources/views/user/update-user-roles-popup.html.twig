{% trans_default_domain "AppBundle" %}
<!-- Modal -->
<div class="modal fade" id="update-user-roles-modal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLongTitle" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            {{ form_start(form) }}
            <div class="modal-header">
                <h5 class="modal-title" id="exampleModalLongTitle">{{ 'user.roles.popup.title' | trans }}</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
            {{ form_row(form.userRoles) }}
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">{{ 'user.roles.popup.cancel' | trans }}</button>
                <button type="submit" class="btn btn-primary">{{ 'user.roles.popup.save' | trans }}</button>
            </div>
        </div>
        {{ form_end(form) }}
    </div>
</div>
