/* eslint-disable */
/* tslint:disable */
/**
 * This is an autogenerated file created by the Stencil compiler.
 * It contains typing information for all components that exist in this project.
 */


import { HTMLStencilElement, JSXBase } from '@stencil/core/internal';


export namespace Components {
  interface TMessager {
    'file_url': string;
    'get_messages_url': string;
    'headerbackground': string;
    'headerbordercolor': string;
    'ismobile': boolean;
    'language': string;
    'logo': string;
    'post_message_url': string;
    'refresh_message_url': string;
    'refresh_time': number;
    'user_id': number;
  }
}

declare global {


  interface HTMLTMessagerElement extends Components.TMessager, HTMLStencilElement {}
  var HTMLTMessagerElement: {
    prototype: HTMLTMessagerElement;
    new (): HTMLTMessagerElement;
  };
  interface HTMLElementTagNameMap {
    't-messager': HTMLTMessagerElement;
  }
}

declare namespace LocalJSX {
  interface TMessager {
    'file_url'?: string;
    'get_messages_url'?: string;
    'headerbackground'?: string;
    'headerbordercolor'?: string;
    'ismobile'?: boolean;
    'language'?: string;
    'logo'?: string;
    'post_message_url'?: string;
    'refresh_message_url'?: string;
    'refresh_time'?: number;
    'user_id'?: number;
  }

  interface IntrinsicElements {
    't-messager': TMessager;
  }
}

export { LocalJSX as JSX };


declare module "@stencil/core" {
  export namespace JSX {
    interface IntrinsicElements {
      't-messager': LocalJSX.TMessager & JSXBase.HTMLAttributes<HTMLTMessagerElement>;
    }
  }
}


