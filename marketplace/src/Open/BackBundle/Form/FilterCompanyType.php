<?php

namespace Open\BackBundle\Form;

use Doctrine\ORM\EntityRepository;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\CheckboxType;
use Symfony\Component\Form\Extension\Core\Type\DateType;
use Symfony\Component\Form\Extension\Core\Type\SubmitType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Contracts\Translation\TranslatorInterface;


class FilterCompanyType extends AbstractType
{
    const TRANSLATION_DOMAIN = 'AppBundle';
    const CLAZZ = 'class';
    const LABEL = 'label';

    private TranslatorInterface $translator;

    public function __construct(TranslatorInterface $translator)
    {
        $this->translator = $translator;
    }

  public function buildForm(FormBuilderInterface $builder, array $options)
  {
    $builder
      ->add(
        'id',
        TextType::class
      )
      ->add(
        'name',
        TextType::class
      )
      ->add(
        'country',
        EntityType::class,

        array(
          self::CLAZZ => 'AppBundle:Country',
          'placeholder' => 'address.form.all_country_placeholder',
          'query_builder' => function (EntityRepository $er) {
            return $er->createQueryBuilder('c')
              ->orderBy('c.code', 'ASC');
          },
          'translation_domain' => self::TRANSLATION_DOMAIN,
          'choice_translation_domain' => self::TRANSLATION_DOMAIN,
        )
      )->add(
        'identification',
        TextType::class
      )->add(
        'city',
        TextType::class
      )->add(
        'termpayment_moneytransfert_enabled',
        CheckboxType::class,
        array('label' => false,
        )
      )->add(
        'termpayment_moneytransfert_pending',
        CheckboxType::class,
        array('label' => false,
        )
        )->add(
        'status',
        ChoiceType::class,
        array(
          'choices' => array(
            'back.company.list.status_company.all' => 'all',
            'back.company.list.status_company.acceptable' => 'acceptable',
            'back.company.list.status_company.valid' => 'valid',
            'back.company.list.status_company.disabled' => 'disabled',
            'back.company.list.status_company.rejected' => 'rejected',
              'back.company.list.status_company.pending' => 'pending',

          ),
          'choice_translation_domain' => self::TRANSLATION_DOMAIN,
        )
      )->add(
        'termpaymentMoneyTransfertRequestDate',
        DateType::class

      )->add(
        'termpaymentMoneyTransfertAcceptDate',
        DateType::class

      )->add(
        'creationMin',
        DateType::class,
        array(
          'label' => 'back.company.list.filter.creationMin',
          'widget' => 'single_text',
          'format' => 'dd-MM-yyyy',
          'attr' => [
            self::CLAZZ => 'form-control input-inline datepicker',
            'data-provide' => 'datepicker',
            'data-date-format' => 'dd-mm-yyyy',
          ],
          'translation_domain' => self::TRANSLATION_DOMAIN,
        )
      )->add(
        'creationMax',
        DateType::class,
        array(
          'label' => 'back.company.list.filter.creationMax',
          'widget' => 'single_text',
          'format' => 'dd-MM-yyyy',
          'attr' => [
            self::CLAZZ => 'form-control input-inline datepicker',
            'data-provide' => 'datepicker',
            'data-date-format' => 'dd-mm-yyyy',
          ],
          'translation_domain' => self::TRANSLATION_DOMAIN,
        )
      )->add(
        'term_payment_date_min',
        DateType::class,
        array(
          'label' => 'back.company.list.filter.term_payment_date_min',
          'widget' => 'single_text',
          'format' => 'dd-MM-yyyy',
          'attr' => [
            self::CLAZZ => 'form-control input-inline datepicker',
            'data-provide' => 'datepicker',
            'data-date-format' => 'dd-mm-yyyy',
          ],
          'translation_domain' => self::TRANSLATION_DOMAIN,
        )
      )->add(
        'term_payment_date_max',
        DateType::class,
        array(
          'label' => 'back.company.list.filter.term_payment_date_max',
          'widget' => 'single_text',
          'format' => 'dd-MM-yyyy',
          'attr' => [
            self::CLAZZ => 'form-control input-inline datepicker',
            'data-provide' => 'datepicker',
            'data-date-format' => 'dd-mm-yyyy',
          ],
          'translation_domain' => self::TRANSLATION_DOMAIN,
        )
      )->add(
        'save',
        SubmitType::class,
        array(
          'label' => 'back.company.list.filter.submit',
          'attr' => array(self::CLAZZ => 'save'),
          'translation_domain' => self::TRANSLATION_DOMAIN,
        )
      )->setMethod('GET');
  }

    /**
     * @return string
     */
    public function getName()
    {
        return 'company_filter';
    }

    /**
     * Configures the options for this type.
     *
     * @param OptionsResolver $resolver The resolver for the options.
     */
    public function configureOptions(OptionsResolver $resolver)
    {
        $resolver->setDefaults(
            [
                'csrf_protection' => false
            ]
        );
    }

}