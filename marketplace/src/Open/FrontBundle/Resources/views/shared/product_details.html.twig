{% extends '@OpenFront/base.html.twig' %}
{% trans_default_domain 'AppBundle' %}

{% block header_js %}
    <script src="{{ asset('js/jquery.sliderPro.min.js') }}"></script>
{% endblock %}

{% block body %}

    {% block searchbar %}
        {% if formSearchBar is defined %}
            {% include('@OpenFront/component/search-bar.html.twig') %}
        {% else %}
            {{ render(controller("Open\\FrontBundle\\Controller\\SearchBarController::formAction")) }}
        {% endif %}
    {% endblock %}

    <div class="offer-details-container">
        <div class="offer-product-details">
            <div class="offer-picture desktop-only">
                {% if offer.associatedPictures is not empty %}
                    {% for picture in offer.associatedPictures %}
                        {% if(loop.first) %}
                            <div class="offer-picture-img" style="background-image:url('{{ picture }}');"></div>
                        {% endif %}
                    {% endfor %}
                    <div class="offer-associated-pictures">
                        {% for picture in offer.associatedPictures|slice(0, 3) %}
                            <div class="offer-picture-img-sm" style="background-image:url('{{ picture }}');"></div>
                        {% endfor %}
                    </div>
                {% else %}
                    {% if offer.picture is not empty %}
                        <div class="offer-picture-img" style="background-image:url('{{ offer.picture }}');"></div>
                    {% else %}
                    <div class="offer-picture-img" style="background-image:url(' {{ asset ( '/images/noVisual.png' ) }} ');"></div>
                    {% endif %}
                {% endif %}
            </div>
            <div class="offer-picture-mobile mobile-only">
                {% if offer.associatedPictures is not empty %}
                    <div class="offer-detail-slider slider-pro" id="js-offer-slider-mobile">
                        <div class="sp-slides">
                            {% for picture in offer.associatedPictures|slice(0, 3) %}
                                <div class="sp-slide">
                                    <img class="sp-image" src="{{ asset ( picture ) }}"/>
                                </div>
                            {% endfor %}
                        </div>
                    </div>
                {% else %}
                    <div class="offer-detail-slider" style="background-image:url(' {{ asset ( '/images/noVisual.png' ) }} ');"></div>
                {% endif %}
            </div>
            <div class="offer-product-info">
                <div class="sep">
                    {% if showScore %}
                        <p class="offer-title">{{'Score : ' ~ offer.score ~ ' --> ' ~ (offer.score/10)|round(0, 'floor')}}</p>
                    {% endif %}
                    <span class="offer-title"> {{ offer.name }}</span>
                    <p class="offer-reference">{{ offer.id }}</p>
                    {% if  offer.sku is not empty %}
                    <p class="sku-reference">{{ 'offer_result_details.sku_label'|trans }}{{ offer.sku }}</p>
                    {% endif %}
                    <!-- supplier -->
                    <div class="supplier-details">
                        <div class="mr-1">{{ 'offer_result_details.supplier'|trans ~ ' : ' ~ offer.merchant.name }}</div>
                        <div class='stars' data-rating="{{ merchant.rating | default(0) }}"></div>
                    </div>
                    <p class="offer-description">{{ offer.description | truncate(500) | nl2br }}</p>
                    <a class="product-link" href="#more">{{ 'offer_result_details.show_more'|trans }}</a>
                    <!-- Delivery details -->
                    <div class="delivery-restriction-container">
                        {% if offer.deliveryRestricted %}
                            <div class="product-card-location" style="background-image:url('{{ asset('images/location-icon.svg') }}');"></div>
                            <p class="delivery-restriction">{{ 'offer_result_details.delivery_restriction'|trans }} : <span>{{ offer.deliveryRestrictionDescription }} </span></p>
                    {% endif %}
                    </div>
                    <div class="adapted-company-container">
                        {% if offer.merchant.adaptedCompany %}
                        <div class="tooltip-total">
                            <div class="product-details-adapted-cie">
                            </div>
                            <div class="tooltiptext">
                                {{ 'merchant.adapted_company' | trans }}
                            </div>
                        </div>
                        {% endif %}

                    </div>
                    {% if offer.risk == true %}
                    <div class="adapted-company-container">
                        <div class="tooltip-total risk">
                            <img  src="{{ asset('images/ico-chimie3.svg') }}" alt="" height="35">
                            <div class="tooltiptext">
                                {{ 'cart.risk_info' | trans }}
                            </div>
                        </div>
                    </div>
                    {% endif %}
                    <div class="contact-div">
                        <svg class="contact-ico"></svg>
                        <a class="product-link contact-text" href="#" onclick="
                                window.UI.Modal.supplierQuestion(
                                '{{ offer.id}}',
                                '{{ path('message.offer.send') }}',
                                '{{ 'message.modal.empty'|trans({}, 'AppBundle')|escape('js') }}',
                                '{{ 'message.add.success'|trans({}, 'AppBundle')|escape('js') }}',
                                '{{ 'message.add.error'|trans({}, 'AppBundle')|escape('js') }}'
                            )"
                    >{{ 'offer_result_details.ask_question'|trans }}</a>
                    </div>
                    <div class="data_sheets">

                        {% if offer.dataSheetUrls[0] is not null %}
                            <div class="mb-2">
                                <svg class="file_download"></svg><a class="datasheet-link" href="{{ offer.dataSheetUrls[0] }}" target="_blank">{{ 'offer_result_details.data_sheet'|trans| upper }}</a>
                            </div>
                        {% endif %}
                        {% if offer.dataSheetUrls[1] is not null %}
                            <div class="mb-2">
                                <svg class="file_download"></svg><a class="datasheet-link" href="{{ offer.dataSheetUrls[1] }}" target="_blank">{{ 'offer_result_details.attached_sheet'|trans| upper }}</a>
                            </div>
                        {% endif %}

                        <div class="mb-2 d-flex">
                            {% include('@OpenFront/shared/favourite-icon.html.twig') with {'offerId': offer.id, 'isFavourite': offer.isFavorit} %}
                            <a id="addOfferToFavourite" class="datasheet-link ml-2" href="#">{{ 'favourite.offer_detail.add_to_favourite'|trans|upper }}</a>
                        </div>
                    </div>
                </div>
            </div>
            <div class="offer-product-price">
                {% if not offer.priceGivenOnQuotation %}
                    <div class="offer-product-price-header">
                        <p class="price">{{ ((offer.price)|number_format(2)) ~ ' ' ~ offer.currency }}</p>
                    </div>

                    <div class="offer-product-price-content">
                        {% if offer.reductions|length %}
                            <p class="offer-product-price-label">{{ 'offer_result_details.discount_quantity'|trans ~ ' :' }}</p>
                            {% for reduction in offer.reductions %}
                                <p> ≥
                                    {{
                                        reduction.quantity ~ ' : ' ~
                                    ((reduction.price)|number_format(2)) ~ ' ' ~ offer.currency ~
                                        ' (' ~ reduction.formattedPercentage ~ ')'
                                    }}
                                </p>
                            {% endfor %}
                        {% endif %}
                    </div>
                {% endif %}

                <div class="offer-product-price-form">
                    {% if not offer.priceGivenOnQuotation %}
                        <form class="cart-form" method="post" name="addOfferToCart">
                            <input type="hidden" name="form[offerId]" value="{{ offer.id }}" />
                            <input type="hidden" name="form[_token]" value="{{ csrf_token('form') }}"/>
                            <div class="dis-row">
                                <div class="qtyComponent">
                                    <span class="offer-qty-label">{{ 'offer_result_details.quantity'|trans }}</span>
                                    <div class="qtyComponentBtn">
                                        <button type="button" class="qtyModif min" disabled></button>
                                        <div class="tooltip-total">
                                            <input name="form[quantity]" type="number" data-moq="{{ offer.moq }}" data-batchsize="{{ offer.batchSize }}" class="qtyInput" value="{{ offer.moq }}" min="{{ offer.moq }}" max="{{ offer.stock }}">
                                        </div>
                                        <button type="button" class="qtyModif plus"></button>
                                    </div>

                                </div>

                            </div>
                            <div id="stock-label" class="tooltip-total">
                                {% if offer.status != 'active'  %}
                                    {% set stockStatus = 'inactive' %}
                                    <p class="product-stock" id="product-{{ stockStatus }}">{{ 'offer_result_details.inactive'|trans }}</p>
                                {%  else %}
                                    {% if offer.stockAvailable %}
                                        {% set stockStatus = 'in-stock' %}
                                        <p class="product-stock" id="product-{{ stockStatus }}">{{ 'offer_result_details.in_stock'|trans }}</p>
                                    {% endif %}

                                    {% if offer.limitedStock %}
                                        {% set stockStatus = 'limited-stock' %}
                                        <p class="product-stock" id="product-{{ stockStatus }}">{{ 'offer_result_details.limited_stock'|trans }}</p>
                                        <div id="stock-tool-text" class="tooltiptext">
                                                            <span class="info">
                                                                {{ 'offer_result_details.info.limited_stock' | trans({'%stock%':offer.stock}) }}
                                                            </span>
                                        </div>
                                    {% endif %}

                                    {% if offer.outOfStock %}
                                        {% set stockStatus = 'out-stock' %}
                                        <p class="product-stock" id="product-{{ stockStatus }}">{{ 'offer_result_details.out_of_stock'|trans }}</p>
                                        <div class="tooltiptext">
                                                            <span class="info">
                                                                {{ 'offer_result_details.info.out_of_stock' | trans }}
                                                            </span>
                                        </div>
                                    {% endif %}
                                    {% if offer.stockOnCommand %}
                                        {% set stockStatus = 'command-stock' %}
                                        <p class="product-stock" id="product-{{ stockStatus }}">{{ 'offer_result_details.stock_on_command'|trans }}</p>
                                    {% endif %}
                                {% endif %}
                            </div>
                            <div class="offer-validate-btn">
                                <button class="cart-btn" type="submit" name="form[addOfferToCart]" data-offer-reference="{{ offer.id }}">
                                    <svg class="cart-icon"
                                            xmlns="http://www.w3.org/2000/svg"
                                            viewBox="0 0 18 13.622">
                                        <path
                                                d="m 18,3.618 -2.55,6.598 H 5.854 L 2.494,1.209 H 0.437 L 0,0 h 3.305 l 3.36,9.007 h 7.966 l 2.012,-5.39 H 18 Z m -3.162,10.004 a 1.2165,1.2165 0 1 1 0,-2.433 1.2165,1.2165 0 0 1 0,2.433 z m -7.784,0 a 1.2165,1.2165 0 1 1 0,-2.433 1.2165,1.2165 0 0 1 0,2.433 z M 15.818,3.818 15.366,4.909 H 9.818 l 0.43,-1.09 h 5.57 z M 14.728,6 14.286,7.09 H 11.455 L 11.874,6 h 2.853 z"
                                                style="fill-rule:evenodd" />
                                    </svg>
                                    <p>{{ 'offer_result_details.add_to_cart'|trans }}</p>
                                </button>
                            </div>
                        </form>
                    {% else %}
                        <div class="offer-validate-btn">
                            <span class="product-link" onclick="
                                    window.UI.Modal.askForQuotation(
                                    '{{ offer.id}}',
                                    '{{ path('message.ask.quotation') }}',
                                    '{{ 'message.modal.empty'|trans({}, 'AppBundle')|escape('js') }}',
                                    '{{ 'quote.add.success'|trans({}, 'AppBundle')|escape('js') }}',
                                    '{{ 'message.add.error'|trans({}, 'AppBundle')|escape('js') }}'
                                    )"
                            >
                                <button id="ask-for-quotation-btn" class="cart-btn" type="submit" name="askOfferQuotation" data-offer-reference="{{ offer.id }}">
                                    {{ 'offer_result_details.ask_quotation'|trans }}
                                </button>
                            </span>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <ul class="tabs__items desktop-only" id="more">
            <li class="tab__item">
                <a class="active" href="#tab1">{{ 'offer_result_details.description'|trans }}</a>
            </li>
            <li class="tab__item">
                <a href="#tab2">{{ 'offer_result_details.technical_details'|trans }}</a>
            </li>
            <li class="tab__item">
                <a href="#tab3">{{ 'offer_result_details.about_supplier'|trans |upper }}</a>
            </li>
        </ul>

        <div class="tabs__content">
            <div class="tab__item">
                <a class="active" href="#tab1">{{ 'offer_result_details.description'|trans }}</a>
            </div>
            <div class="tab__content active" id="tab1">
                <section>
                    {% if offer.description is not null and offer.description is not empty %}
                        {{ offer.description | nl2br }}
                    {% else %}
                        {{ 'offer_result_details.no_description'|trans }}
                    {% endif %}
                </section>
            </div>
            <div class="tab__item">
                <a href="#tab2">{{ 'offer_result_details.technical_details'|trans }}</a>
            </div>
            <div class="tab__content" id="tab2">
                <section>
                    {% if offer.technicalDetails is not null %}
                        {% if  offer.technicalDetails.technicalProperties|length %}
                            {% set nbLinesInCols = (offer.technicalDetails.technicalProperties|length // 2) + 1%}
                            {% set nbKeys = offer.technicalDetails.technicalProperties|length %}
                            <section class="technical-description-items">
                            <table class="table">
                                <tbody>
                                    {% for technicalProperty in offer.technicalDetails.technicalProperties|slice(0, nbLinesInCols) %}
                                        <tr>
                                            <td><span class="technical-description-items-label">{{ technicalProperty.label }}</span><span class="technical-description-items-value">{{ technicalProperty.value }}</span></td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                            <table class="table">
                                <tbody>
                                    {% for technicalProperty in offer.technicalDetails.technicalProperties|slice(nbLinesInCols, nbKeys) %}
                                        <tr>
                                            <td><span class="technical-description-items-label">{{ technicalProperty.label }}</span><span class="technical-description-items-value">{{ technicalProperty.value }}</span></td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                            </section>
                        {% endif %}
                        {% if offer.technicalDetails.description is not null and offer.technicalDetails.description is not empty %}
                            <section class="technical-description-text"><span>{{ 'product.technical_characteristics'|trans }}</span> : {{ offer.technicalDetails.description | nl2br }}</section>
                        {% endif %}
                    {% endif %}
                </section>
            </div>
            <div class="tab__item">
                <a href="#tab3">{{ 'offer_result_details.about_supplier'|trans |upper }}</a>
            </div>
            <div class="tab__content" id="tab3">

                <section class="merchant-description">
                    {% if merchant is not null and merchant is not empty%}
                        {% if merchant.logo is not null and merchant.logo is not empty %}
                            <section class="merchant-description-logo">
                                <img src="{{ merchant.logo }}" />
                            </section>
                        {% endif %}
                        <section class="merchant-description-text">
                            <section class="merchant-description-name desktop-only flex">
                                {{ merchant.name }}
                                {% if merchant is defined and merchant is not empty and reviewsCount is defined and reviewsCount is not null %}
                                <div class='stars' id="rate-merchant-tab" data-rating="{{ merchant.rating | default(0) }}"></div>
                                    <div class="tooltip-total">
                                        <span class="ml-10 show-reviews pointer">{{ reviewsCount }} {{ 'review.view' | trans }}</span>
                                        <div class="tooltiptext">
                                            {{ 'orders.review.show_reviews' | trans }}
                                        </div>
                                    </div>
                                {% endif %}
                            </section>
                            {% if merchant.shortDescription is not null and merchant.shortDescription is not empty %}
                                <p class="merchant-description-title desktop-only">{{ 'offer_result_details.supplier_presentation'|trans }}</p>
                                {{ merchant.shortDescription | nl2br }}
                            {% else %}
                                <p class="mt-10">{{ 'offer_result_details.no_supplier_presentation'|trans }}</p>
                            {% endif %}
                        </section>
                    {% endif %}
                </section>

                <div class="middle-red-line"></div>
                {% if merchant is defined and merchant is not empty and reviews is defined and reviews is not null %}
                    <section class="merchant-reviews container-fluid-total" style="background-color: #F8F8F8;flex-direction: column;">
                        <section>
                            <p class="font-weight-bold">{{ 'review.title'|trans|upper }}</p>
                            <div class="review-list">
                                {% include '@OpenFront/shared/merchant_review_row.html.twig' with { reviews : reviews } %}
                            </div>
                        </section>

                        <p class="next-reviews-container next-reviews-btn"><a class="next-reviews-link desktop-only" href="#" onclick="moreReviews();">{{ 'review.show_more'|trans }}</a></p>
                        <div class="show-more-container mobile-only next-reviews-btn" onclick="moreReviews();">
                            <span class="show-more-img"><img src="{{ asset('images/plus.svg') }}"></span>
                            <span>{{ 'review.show_more'|trans | upper }}</span>
                        </div>
                    </section>
                {% endif %}
            </div>
        </div>
    </div>

    <script type="text/template" id="js-supplier-question-modal-tpl">
        <div class="Modal-header">
            <button type="button" class="close js-cancel-button" data-dismiss="modal" aria-label="Close">
                <span >×</span>
            </button>
            <h5 class="Modal-title">{{ 'product.modal.question_title'|trans([], 'AppBundle') }}</h5>
        </div>

        <form id="questionForm" method="post" name="askSupplierQuestion">
            <div class="Modal-body">
                <p><span>{{ 'modal.report_content.label_product'|trans([], 'AppBundle') }}</span> : {{ "{{ref }}" }}</p>
                 {% if offer.sku %}
                     <p><span>{{ 'modal.internal_reference'|trans([], 'AppBundle') }}</span> : {{ offer.sku }}</p>
                 {% endif %}
                <input type="hidden" name="offer" value="{{ offer.id }}"/>
                <input type="hidden" name="merchant" value="{{ merchant.id }}"/>
                <input type="hidden" name="_token" value="{{ csrf_token('message_offer_send') }}"/>
                <textarea rows="2" cols="5" id="supplier-question-subject" name="message">{{ 'modal.placeholder_product_identifiant'  |trans([], 'AppBundle') ~ offer.id }}{% if offer.sku%}{{ 'modal.placeholder_sku_interne' |trans([], 'AppBundle') ~ offer.sku}} {% endif%}</textarea>
            </div>
            <div class="Modal-footer">
                <button id="js-send-button" type="button" class="btn btn-primary buttonModal" name="form[askSupplierQuestion]">{{ 'modal.send'|trans({}, 'AppBundle')|upper }}</button>
            </div>
        </form>
    </script>

    <script type="text/template" id="js-ask-for-quotation-modal-tpl">
        <div class="Modal-header">
            <button type="button" class="close js-cancel-button" data-dismiss="modal" aria-label="Close">
                <span >×</span>
            </button>
            <h5 class="Modal-title">{{ 'product.modal.askforquotation_title'|trans([], 'AppBundle') }}</h5>
        </div>
        <form id="askForQuotationForm" method="post" name="askForQuotation">
            <div class="Modal-body">
                <input type="hidden" name="offer" value="{{ offer.id }}"/>
                <input type="hidden" name="merchant" value="{{ merchant.id }}"/>
                <input type="hidden" name="_token" value="{{ csrf_token('message_offer_send') }}"/>
                <textarea placeholder="{{ 'product.modal.askforquotation_message'|trans([], 'AppBundle') }}" rows="2" cols="5" id="quotation-question" name="message"></textarea>

                <section id="chosen-files-message" class="italic">
                    {{ 'message.attachment_limit'|trans({'%limit%' : 9}) }}
                    <div>{{ 'message.authorized_types'|trans }}</div>
                    <div>{{ 'message.tips'|trans }}</div>
                </section>
                <section id="file-size-validation-message" style="color: #ED0000; visibility: hidden">{{ 'message.file_too_big'|trans({'%size-limit%' : '5 MB'}) }}</section>
                <section id="file-type-validation-message" style="color: #ED0000; visibility: hidden">{{ 'message.file_type_incorrect'|trans }}</section>
                <div id="add-file-min">
                    <section id="chosen-files">
                        <div id="add-file-btn-min" class="pointer tooltip-total">
                            +
                            <div class="tooltiptext">
                                {{ 'message.labels.add_file' | trans | upper }}
                            </div>
                        </div>
                    </section>
                </div>
                <input id='file-input-0'
                       type='file'
                       hidden="hidden"
                       name="attachments[]"
                       accept="image/png, image/jpeg, application/pdf, image/gif, image/png, image/tiff, application/zip, application/msword, application/vnd.openxmlformats-officedocument.wordprocessingml.document, application/vnd.ms-excel, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                />
                <button id="add-file-btn" type="button" class="btn-white" >{{ 'message.labels.add_file' | trans | upper }} <span>+</span></button>
            </div>
            <div class="Modal-footer">
                <button id="js-ask-quotation-send-button" type="button" class="btn btn-primary buttonModal" name="form[askForQuotation]">{{ 'modal.send'|trans({}, 'AppBundle')|upper }}</button>
            </div>
        </form>
    </script>

    {% block illicitContentForm %}
        <script type="text/template" id="js-report-content-modal-tpl">
            <div class="Modal-header">
                <button type="button" class="close js-cancel-button" data-dismiss="modal" aria-label="Close">
                    <span >×</span>
                </button>
                <h5 class="Modal-title">{{ 'modal.report_content.title'|trans([], 'AppBundle') }}</h5>
            </div>
            <form id="reportContentForm" method="post" name="reportContentForm">
                <div class="Modal-body">
                    <p><span>{{ 'modal.report_content.label_product'|trans({}, 'AppBundle')|raw }}</span> : {{ offer.id }}</p>
                    <input type="hidden" name="offer" value="{{ offer.id }}"/>
                    <input type="hidden" name="merchant" value="{{ offer.merchant.id }}"/>
                    <input type="hidden" name="_token" value="{{ csrf_token('report_content_send') }}"/>
                    <section class="select-type">
                        <select class="report-type" name="report-type">
                            <option>{{ 'modal.report_content.option1'|trans([], 'AppBundle') }}</option>
                            <option>{{ 'modal.report_content.option2'|trans([], 'AppBundle') }}</option>
                            <option>{{ 'modal.report_content.option3'|trans([], 'AppBundle') }}</option>
                            <option>{{ 'modal.report_content.option4'|trans([], 'AppBundle') }}</option>
                            <option>{{ 'modal.report_content.option5'|trans([], 'AppBundle') }}</option>
                        </select>
                    </section>
                    <textarea placeholder="{{ 'modal.report_content.placeholder'|trans([], 'AppBundle') }}" rows="2" cols="5" id="report-subject" name="message"></textarea>
                </div>
                <div class="Modal-footer">
                    <button id="js-report-button" type="button" class="btn btn-primary buttonModal" name="form[reportContentForm]">{{ 'modal.send'|trans({}, 'AppBundle')|upper }}</button>
                </div>
            </form>
        </script>
    {% endblock %}
{% endblock %}

{% block javascripts %}
    <script src="{{ asset('js/rater.min.js') }}"></script>
    <script type="text/javascript">
        document.addEventListener('DOMContentLoaded', function () {
            var $w = $(window);
            var screenWidth = $('body').width();
            var screenHeight = $w.height();
            var navHeight = 0;
            var $slider;
            var orientation = window.UI.Utils.getScreenOrientation();

            var sliderOptions = {
                width : '100%',
                height : '275px',
                slideDistance : 0,
                fadeArrows : true,
                buttons: true,
                autoplay: false
            };

            $slider = initSlider(screenWidth, screenHeight, navHeight, orientation, sliderOptions);

            // Listen for resize changes
            $w.on("resize", function() {
                screenWidth = $('body').width();
                screenHeight = $w.height();
                navHeight = $('.Header-hamburgerIcon').height();

                var newOrientation = window.UI.Utils.getScreenOrientation();

                // If orientation changed then reinit the slider
                if (newOrientation !== orientation) {
                    orientation = newOrientation;
                    $slider = initSlider(screenWidth, screenHeight, navHeight, orientation, sliderOptions);
                }
            });
        });

        /**
         * Initialize homepage slider with the correct ratio
         * @param screenWidth
         * @param screenHeight
         * @param navHeight
         * @param orientation
         * @param sliderOptions
         */
        var initSlider = function(screenWidth, screenHeight, navHeight, orientation, sliderOptions) {
            if($('#js-offer-slider-mobile').find('.sp-slide').length <= 1) {
                sliderOptions.touchSwipe = false;
            }
            return $('#js-offer-slider-mobile').sliderPro(
                jQuery.extend({}, sliderOptions, {})
            ).data( 'sliderPro' );
        };

    </script>

    <script type="text/javascript">

        var $offset = 0;
        var nbReviews = {{ reviews | length }};

        var moreReviews = function() {
            loadingModal = window.UI.Modal.showLoading();
            reviewsCount = {{ reviewsCount }};
            $offset = $offset + 5;
            $.ajax({
                type: 'POST',
                url: '{{ path('front.offer.merchant.reviews') }}',
                data: {
                    merchantId: {{merchant.id}},
                    offset: $offset,
                    limit: 5
                },
                success: function (htmlResult) {
                    var nbResult = $('.merchant-review', '<div>' + htmlResult + '</div>').length;
                    if(nbResult > 0) {
                        // Incrémentation du nbre de reviews affichées
                        nbReviews = nbReviews + nbResult;
                        // Si on atteind le nbre max de rviews, le lien "Show-more" est caché
                        if(nbReviews == reviewsCount) {
                            $('.next-reviews-btn').hide();
                        }
                        $('.merchant-reviews').find('.review-list').append(htmlResult);
                    } else {
                        $('.next-reviews-btn').hide();
                        closeLoading();
                    }
                },
                complete: function () {
                    closeLoading();
                    genRating();
                }
            });
        };

        function genRating() {
            $('.stars').each(function() {
                var merchantRating = $(this).data('rating');
                $(this).rate({
                    rating: merchantRating,
                    max_value: 5,
                    step_size: 0.5,
                    readonly: true,
                    initial_value: merchantRating,
                });
            });
        }
        genRating();

        var batchSize = parseInt($('.qtyInput').data('batchsize'));
        var moq = parseInt($('.qtyInput').data('moq'));

        var inputQuantity = $('input[name="form[quantity]"]');
        var plusButton = $('.qtyModif.plus');
        var addToCartButton = $('button[name="form[addOfferToCart]"]');
        var stockToolText = $('#stock-tool-text');
        var productStockStatus = $('.product-stock');
        var stockStatus = productStockStatus.html();
        var stockStatusId = productStockStatus.attr('id');

        var stockManagement = function() {
            var offerStock = parseInt(inputQuantity.attr('max'));
            var quantity = parseInt(inputQuantity.val());

            // disable plus quantity button and add to cart button when stock = 0
            if(offerStock === 0 || $('#product-inactive').length) {
                inputQuantity.val(0);
                inputQuantity.attr('disabled', true);
                inputQuantity.css('border-bottom', '1px solid #A4A7B3')
                plusButton.attr('disabled', true);
                addToCartButton.attr('disabled', true);
                addToCartButton.css('background-color', '#E2E4ED');
                addToCartButton.css('border-color', '#E2E4ED');
                addToCartButton.css('cursor', 'not-allowed');
            }

            if($('#product-inactive').length){
                plusButton.attr('disabled', true);
            } else {
                // disable plus quantity button when quantity = offerStock
                if (offerStock === quantity) {
                    plusButton.attr('disabled', true);
                }

                // enable plus quantity button when quantity < offerStock
                if (offerStock > quantity) {
                    plusButton.attr('disabled', false);
                }
            }

            inputQuantity.on('change', function (e) {
                quantity = parseInt(inputQuantity.val());

                if (quantity > offerStock) {
                    plusButton.attr('disabled', false);
                    stockToolText.css('display', 'block');
                    productStockStatus.html('{{ 'offer_result_details.low_stock' | trans }}');
                    productStockStatus.attr('id', 'product-low-stock');
                } else {

                    stockToolText.css('display', 'none');
                    productStockStatus.html(stockStatus);
                    productStockStatus.attr('id', stockStatusId);
                }

            });
        };

        stockManagement();

        // todo review this code with stock management
        $('.qtyModif.plus').click(function() {
            var currentQty = parseInt($('.qtyInput').val());
            var diff = currentQty % batchSize;
            var newQty = currentQty + (batchSize - diff);
            $('.qtyInput').val(newQty).change();
            stockManagement();
        });

        $('.qtyModif.min').click(function() {
            var currentQty = parseInt($('.qtyInput').val());
            var diff = currentQty % batchSize;
            var newQty = currentQty - batchSize;
            if (diff) {
                newQty = currentQty - (batchSize - (batchSize - diff));
            }
            $('.qtyInput').val(newQty).change();
            stockManagement();
        });

        /** Saisie libre : on arrondit à la valeur du dessus si non dans le batchsize **/
        $('.qtyInput').on('change', function() {
            if (parseInt($(this).val()) <= moq) {
                $(this).val(moq);
                $('.qtyModif.min').attr('disabled', true);
            } else {
                var currentQty = parseInt($('.qtyInput').val());
                var diff = currentQty % batchSize;
                var newQty = currentQty;
                if(diff) {
                    newQty = currentQty + (batchSize - (batchSize - diff));
                }
                $(this).val(newQty);
                $('.qtyModif.min').attr('disabled', false);
            }
            stockManagement();
        });

        var closeLoading = function() {
            if(loadingModal !== null) {
                loadingModal.close();
            }
        };

        $('.cart-btn[name="form[addOfferToCart]"]').on('click', function (){
            var $inputQuantity = $('input[name="form[quantity]"]');

            var quantity = $inputQuantity.val();

            var offerStock = parseInt($inputQuantity.attr('max'));

            if(offerStock <= 0) {
                messageModal = window.UI.Modal.alert("{{ 'offer_detail.any_stock'|trans({}, 'AppBundle')|raw }}");
            } else if(quantity === "" || quantity  === 0 || quantity < moq || quantity % batchSize){
                messageModal = window.UI.Modal.alert("{{ 'offer_detail.wrong_quantity'|trans({}, 'AppBundle')|raw }}");
            } else {
                loadingModal = window.UI.Modal.showLoading();
                $.ajax({
                    type: 'POST',
                    url: '{{ path('front.cart.offer.add') }}',
                    data: $('.cart-form').serialize(),
                    success: function (response) {
                        closeLoading();

                        if(response.success === true) {
                            $('input[name="form[quantity]"]').val(moq);
                            $(document).trigger({
                                type: "UpdateItemInCart",
                                itemInCart: response.itemInCart
                            });

                            const successMessage =  "{{ 'cart.add.success'|trans({}, 'AppBundle')|raw }}";
                            let merchantMinOrderMessage = '';

                            if (response.amountIsNotEnough) {
                                merchantMinOrderMessage =  "{{ 'cart.add.merchant_minimum_order'|trans({'%price%': merchant.minimumOrderAmount}, 'AppBundle') ~ ' ' ~ merchant.country.currency }}";
                            }

                            messageModal = window.UI.Modal.addToCartAlert(successMessage, merchantMinOrderMessage);
                        } else {
                            messageModal = window.UI.Modal.alert(response.error);
                        }
                    },
                    error: function (response) {
                        closeLoading();
                        const message = response.responseJSON.error ? response.responseJSON.error : "{{ 'cart.add.error'|trans({}, 'AppBundle')|raw }}";
                        messageModal = window.UI.Modal.alert(message);
                    }
                });
            }
            return false;
        });

        $('.show-reviews').on('click', function () {
            reviewsCount = {{ reviewsCount }};
            nbReviews ={{ reviews | length }};
            var reviews = $('.merchant-reviews');
            if (reviews[0].style.display === "flex") {
                reviews[0].style.display = "none";
            } else {
                reviews[0].style.display = "flex";
            }
            // Si le nbre de reviews affichées = nbre total de reviews, on n'affiche pas le "Show More"
            if (nbReviews == reviewsCount) {
                $('.next-reviews-btn').hide();
            }
        })

        const addFavouriteButton = document.querySelector('#addOfferToFavourite');
        addFavouriteButton.addEventListener("click", function (event) {
            event.preventDefault();
            window.UI.Favourite.addOfferToFavorite(
                "{{ offer.id }}",
                "{{ path('favori.offer.toggle') }}",
                "{{ asset('images/ico-wishlist.svg') }}",
                "{{ asset('images/ico-wishlist-red-plein.svg') }}"
            );
        });
    </script>
{% endblock %}

