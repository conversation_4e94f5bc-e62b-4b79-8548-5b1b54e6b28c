{% trans_default_domain 'AppBundle' %}
{% set countries = countries %}
{% set menu = menuItems %}
{% set user = user %}
{% set hamburger = hamburger %}
{% set categoriesLength = menu|length %}

<header
	class="Header" id="js-header">

	<!-- Header goes here -->
	<section class="header-container connected">
		<div class="Header-menu js-panel-container desktop-only">
			{% include '@OpenFront/menu/header_connected_desktop.html.twig' with { user : user, countries : countries } %}
		</div>

		<div class="logo--home desktop-only">
			<a href="{{ path('homepage') }}">
				<img src="{{ asset('images/logo-mp.svg') }}" alt="logo" style="width: 180px;"/>
			</a>
		</div>

		<div id="Mobile-account-menu" class="overlay">
			{% include '@OpenFront/menu/account_mobile_menu.html.twig' %}
		</div>

		<div id="myNav" class="overlay">
			{% include '@OpenFront/menu/menu.html.twig' with { user : user, menu : menu } %}
		</div>

		<div id="mobile-nav" class="overlay">
			{% include '@OpenFront/menu/menu_mobile.html.twig' with { user : user, menu : menu } %}
		</div>

		<div class="Header-menu js-panel-container user-header desktop-only" style="position: relative">
			{% include '@OpenFront/menu/header_user_desktop.html.twig' with { user : user, cart: cart } %}
		</div>
		<div class="Header-hamburgerIcon">
			<div class="icon-mobile header-mobile-hamburger">
				<button type="button" id="js-hamburger-button" onclick="window.UI.Menu.openNav()" class="desktop-only">
					<img src="{{ asset('/images/group-copy.svg') }}">
				</button>
				<button type="button" id="js-hamburger-button-mobile" onclick="window.UI.Menu.openMobileNav()" class="mobile-only">
					<img src="{{ asset('/images/group-copy.svg') }}">
				</button>
				<button type="button" id="js-close-button" onclick="window.UI.Menu.closeMobileNav()" class="mobile-only" style="display: none;">
					<img src="{{ asset('/images/close.svg') }}">
				</button>
				<div class="logo--home mobile-only">
					<a href="{{ path('homepage') }}">
						<img src="{{ asset('images/logo-mp.svg') }}" alt="logo" style="width: 180px;"/>
					</a>
				</div>
				<div id="cart-1" class="Menu-item has-icon Item-cart mobile-only" style="position: relative">
					<div class="Menu-item has-icon">
						<a href="{{ path('cart.details', {'isMobile': true}) }}">
							<span>
								<img src="{{ asset('images/icone-panier.svg') }}">
							</span>
						</a>
					</div>
					{% if cart and cart.itemsCount > 0 %}
						<div class="counter-bubble-container">
							<div class="counter-bubble">
								{{ cart.itemsCount }}
							</div>
						</div>
					{% else %}
						<div class="hide counter-bubble-container">
							<div class="counter-bubble"></div>
						</div>
					{% endif %}
				</div>
			</div>

			<div class="icon-mobile header-mobile-search">
				<div class="mobile-selects">
					{% include '@OpenFront/menu/header_connected_mobile.html.twig' with { user : user, countries : countries } %}
				</div>

				<ul>
					<li>
						<a href="/{{app.request.locale ~'/supplier/'~ user.getMarketPlace.getTotalDiscriminator() ~ '/register'}}" target="_blank" class="dropdown-label" id="news-icon"> <svg class="icon-warning mr-1" ></svg></a>
						<a style="text-decoration: none" href="/{{app.request.locale ~'/supplier/'~ user.getMarketPlace.getTotalDiscriminator() ~ '/register'}}" target="_blank" class="dropdown-label" id="news">News</a>
					</li>
					<li class="dropdown menu-account">
						<a class="header-user-link mobile-menu-account-link" onclick="window.UI.Menu.openMobileMenuAccount()">
							<div style="position: relative">
								{% if app.session.get('unreadMessage', 0) > 0 or countOrdersToValidate > 0 or countQuotesToRead>0 %}
									<div class="counter-bubble-container">
										<div class="counter-bubble">
											{{ app.session.get('unreadMessage', 0) + countOrdersToValidate + countQuotesToRead }}
										</div>
									</div>
								{% endif %}
								<svg class="Icon Icon-flag">
									<use xlink:href="#icon-user"></use>
								</svg>
							</div>

							<div class="Icon-button-text ">
								<div class="second-level">
									{{ user.username | truncate(8) }}
								</div>
							</div>
						</a>
						<div class="search-icon mobile-only">
							{% set params =  app.request.query.all() | merge({'page':0, 'adv_mobile': true})%}
							<a onclick="window.UI.Menu.openSearch()">
								<svg class="Icon">
									<use xlink:href="#icon-search"></use>
								</svg>
							</a>
						</div>
					</li>
				</ul>
			</div>
		</div>
	</section>
</header>
