<?php
/**
 * Created by PhpStorm.
 * User: PPH04712
 * Date: 16/11/2018
 * Time: 15:47
 */

namespace Open\IzbergBundle\Model;

use <PERSON><PERSON>\Serializer\Annotation as JSM;

class Permission
{

    /**
     * @JSM\Type("integer")
     */
    private $id = 0;

    /**
     * @JSM\Type("string")
     */

    private $merchant;

    /**
     * @JSM\Type("Open\IzbergBundle\Model\User")
     */
    private $user;

    /**
     * @return mixed
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * @param mixed $id
     */
    public function setId($id): void
    {
        $this->id = $id;
    }

    /**
     * @return mixed
     */
    public function getMerchant()
    {
        return $this->merchant;
    }

    /**
     * @param mixed $merchant
     */
    public function setMerchant($merchant): void
    {
        $this->merchant = $merchant;
    }

    /**
     * @return mixed
     */
    public function getUser()
    {
        return $this->user;
    }

    /**
     * @param mixed $user
     */
    public function setUser($user): void
    {
        $this->user = $user;
    }





}