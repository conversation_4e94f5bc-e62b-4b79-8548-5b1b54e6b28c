<?php

namespace Open\IdealBundle\Model;

class CostCenter
{
    private ?string $Id = null;
    private ?string $Name = null;

    /**
     * @return string|null
     */
    public function getId(): ?string
    {
        return $this->Id;
    }

    /**
     * @param string|null $Id
     * @return CostCenter
     */
    public function setId(?string $Id): CostCenter
    {
        $this->Id = $Id;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getName(): ?string
    {
        return $this->Name;
    }

    /**
     * @param string|null $Name
     * @return CostCenter
     */
    public function setName(?string $Name): CostCenter
    {
        $this->Name = $Name;
        return $this;
    }


}