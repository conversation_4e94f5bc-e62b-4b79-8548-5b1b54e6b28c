<?php


namespace AppBundle\Entity;

use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Validator\Constraints as Assert;


/**
 * @ORM\Table(name="buyer_address")
 * @ORM\Entity(repositoryClass="AppBundle\Repository\BuyerAddressRepository")
 * @ORM\HasLifecycleCallbacks()
 */
class BuyerAddress
{
    public const TYPE_TOTAL = 'total';
    public const TYPE_PERSONAL = 'personal';
    public const TYPE_BILLING = 'billing';

    use TimestampedTrait;
    use TechnicalIdentifierTrait;

    /**
     * @var int
     *
     * @ORM\Column(name="id", type="integer")
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="AUTO")
     */
    private $id;

    /**
     * @var User
     *
     * @ORM\ManyToOne(targetEntity="AppBundle\Entity\User")
     * @ORM\JoinColumn(name="user_id", referencedColumnName="id")
     */
    private $user;

    /**
     * @var bool
     *
     * @ORM\Column(name="is_active", type="boolean", options={"default": false})
     */
    private $is_active;

    /**
     * @var string
     *
     * @ORM\Column(name="type", type="string", length=10, nullable=false)
     * @Assert\NotBlank()
     * @Assert\Length(max=10)
     */
    private $type;

    /**
     * @var int
     *
     * @ORM\Column(name="izberg_address_id", type="integer", nullable=true)
     */
    private $izbergAddressId;

    /**
     * @var string
     *
     * @ORM\Column(name="name", type="string", length=50, nullable=true)
     * @Assert\Length(max=50)
     */
    private $name;

    /**
     * @var string
     *
     * @ORM\Column(name="address", type="string", length=100, nullable=false)
     * @Assert\Length(max=100)
     * @Assert\NotBlank(groups={"contact"})
     */
    private $address;

    /**
     * @var string
     *
     * @ORM\Column(name="address2", type="string", length=100, nullable=true)
     * @Assert\Length(max=100)
     */
    private $address2;

    /**
     * @var string
     *
     * @ORM\Column(name="zip_code", type="string", length=15, nullable=false)
     * @Assert\Length(max=15)
     * @Assert\NotBlank(groups={"contact"})
     */
    private $zipCode;

    /**
     * @var string
     *
     * @ORM\Column(name="city", type="string", length=50, nullable=false)
     * @Assert\Length(max=50)
     * @Assert\NotBlank(groups={"contact"})
     */
    private $city;

    /**
     * @ORM\ManyToOne(targetEntity="AppBundle\Entity\Country")
     */
    private $country;

    /**
     * @var string
     *
     * @ORM\Column(name="contact", type="string", length=255, nullable=true)
     * @Assert\Length(max=255)
     */
    private $contact;

    /**
     * @var string
     *
     * @ORM\Column(name="phone", type="string", length=50, nullable=true)
     * @Assert\Length(max=50)
     */
    private $phone;

    /**
     * @var string
     *
     * @ORM\Column(name="comment", type="string", length=255, nullable=true)
     * @Assert\Length(max=255)
     */
    private $comment;

    /**
     * @var MetaCart
     * @ORM\OneToMany(targetEntity="AppBundle\Entity\MetaCart", mappedBy="buyerShippingAddress")
     *
     */
    private $metaCart;

    public function __construct()
    {
        $this->type = self::TYPE_BILLING;
        $this->is_active = true;
        $this->name = '';
    }


    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(?int $id): self
    {
        $this->id = $id;
        return $this;
    }

    public function getUser(): User
    {
        return $this->user;
    }

    public function setUser($user): self
    {
        $this->user = $user;
        return $this;
    }

    public function isIsActive(): bool
    {
        return $this->is_active;
    }

    public function setIsActive(bool $is_active): self
    {
        $this->is_active = $is_active;
        return $this;
    }



    public function getType(): ?string
    {
        return $this->type;
    }

    public function setType(string $type): self
    {
        if(!in_array($type, [ self::TYPE_TOTAL, self::TYPE_PERSONAL, self::TYPE_BILLING, ], true)) {
            throw new \InvalidArgumentException('invalid type');
        }

        $this->type = $type;
        return $this;
    }

    public function getIzbergAddressId(): ?int
    {
        return $this->izbergAddressId;
    }

    public function setIzbergAddressId(int $izbergAddressId): self
    {
        $this->izbergAddressId = $izbergAddressId;
        return $this;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(?string $name): self
    {
        $this->name = $name;
        return $this;
    }

    public function getAddress(): ?string
    {
        return $this->address;
    }
    public function setAddress(?string $address): self
    {
        $this->address = $address;
        return $this;
    }

    public function getAddress2(): ?string
    {
        return $this->address2;
    }

    public function setAddress2(?string $address2): self
    {
        $this->address2 = $address2;
        return $this;
    }

    public function getZipCode(): ?string
    {
        return $this->zipCode;
    }

    public function setZipCode(?string $zipCode): self
    {
        $this->zipCode = $zipCode;
        return $this;
    }

    public function getCity(): ?string
    {
        return $this->city;
    }

    public function setCity(?string $city): self
    {
        $this->city = $city;
        return $this;
    }

    public function getCountry(): ?Country
    {
        return $this->country;
    }

    public function setCountry(?Country $country): self
    {
        $this->country = $country;
        return $this;
    }

    public function getContact(): ?string
    {
        return $this->contact;
    }

    public function setContact(?string $contact): self
    {
        $this->contact = $contact;
        return $this;
    }

    public function getPhone(): ?string
    {
        return $this->phone;
    }

    public function setPhone(?string $phone): self
    {
        $this->phone = $phone;
        return $this;
    }

    public function getComment(): ?string
    {
        return $this->comment;
    }

    public function setComment(?string $comment): self
    {
        $this->comment = $comment;
        return $this;
    }
}
