import React from "react";
import {Trans} from "react-i18next";
import ViewListIcon from "@material-ui/icons/ViewList";
import SearchIcon from "@material-ui/icons/Search";
import AddCircleIcon from "@material-ui/icons/AddCircle";
import MailIcon from "@material-ui/icons/Mail";
import PersonIcon from "@material-ui/icons/Person";
import ExitToAppIcon from "@material-ui/icons/ExitToApp";
import {menuUri} from "./services";
import FA from "react-fontawesome";

const menu = {
    home: [
        {
            label: <Trans>home</Trans>,
            icon: <FA name="tachometer"/>,
            href: menuUri("/"),
            target: "_self",
        },
        {
            label: <Trans>Reviews</Trans>,
            icon: <FA name="star"/>,
            href: menuUri("/reviews/"),
            target: "_self",
        },
        {
            label: <Trans>Guide Fournisseur</Trans>,
            icon: <FA name="link"/>,
            href:
                "https://www.clickandbuy.total/docs-supplier/click_and_buy_guide_fournisseurs.pdf",
            target: "_blank",
        },
    ],
    catalog: [
        {
            label: <Trans>offers</Trans>,
            icon: <SearchIcon/>,
            href: menuUri("/offers/"),
            target: "_self",
        },
        {
            label: <Trans>Créer une offre</Trans>,
            icon: <AddCircleIcon/>,
            href: menuUri("/offers/product_create/"),
            target: "_self",
        },
        {
            label: <Trans>gallery</Trans>,
            icon: <FA name="image"/>,
            href: menuUri("/offers/gallery/"),
            target: "_self",
        },
        {
            label: <Trans>import</Trans>,
            icon: <FA name="th-list"/>,
            href: menuUri("/imports/"),
            target: "_self",
        },
        {
            label: <Trans>Gabarits d'import</Trans>,
            icon: <FA name="link"/>,
            href:
                "http://www.clickandbuy.total/docs-supplier/click_and_buy_gabarits_import_par_categorie.zip",
        },
    ],
    orders: [
        {
            label: <Trans>orders</Trans>,
            icon: <ViewListIcon/>,
            href: menuUri("/orders/manage/"),
            target: "_self",
        },
    ],
    Devis: [
        {
            label: <Trans>Devis</Trans>,
            href: menuUri("/front/supplier/"),
            target: "_self",
        },
    ],
    messages: [
        {
            label: <Trans>inbox</Trans>,
            icon: <MailIcon/>,
            href: menuUri("/messages/inbox"),
            target: "_self",
        },
    ],
    settings: [
        {
            label: <Trans>Profil</Trans>,
            icon: <FA name="edit"/>,
            href: menuUri("/settings/"),
            target: "_self",
        },
        {
            label: <Trans>Informations de l'entreprise</Trans>,
            icon: <FA name="info-circle"/>,
            href: menuUri("/settings/company/"),
            target: "_self",
        },
        {
            label: <Trans>Documents</Trans>,
            icon: <FA name="archive"/>,
            href: menuUri("/settings/documents/"),
            target: "_self",
        },
        {
            label: <Trans>Livraison</Trans>,
            icon: <FA name="send"/>,
            href: menuUri("/services/shipping/"),
            target: "_self",
        },
        {
            label: <Trans>Utilisateurs</Trans>,
            icon: <PersonIcon/>,
            href: menuUri("/settings/permissions/"),
            target: "_self",
        },
    ],
    user: [
        {
            label: <Trans>account</Trans>,
            icon: <PersonIcon/>,
            href: menuUri("/"),
            target: "_self",
        },
        {
            label: <Trans>logout</Trans>,
            icon: <ExitToAppIcon/>,
            href: "/front/supplier/logout",
            target: "_self",
        },
    ],
};

export default menu;
