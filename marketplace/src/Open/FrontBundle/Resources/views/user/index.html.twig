{% extends 'OpenFrontBundle::menu/buyer_menu.html.twig' %}
{% form_theme form ':Form:appli_layout.html.twig' %}
{% trans_default_domain 'AppBundle' %}

{%  set title = 'form.user.title_new' %}
{% if user_id != 0 %}
{% set title = 'form.user.title_edit' %}
{% endif %}

{% block title %}{{ title|trans }} {{ 'form.user.title_common'|trans }}{% endblock %}

{% block body %}
<div class="Page-inner">

    <div class="Form UserForm">

        {{ form_start(form, {'attr':{'id':'js-user-form', 'novalidate':'true'}}) }}

            <div class="form-title"><h1>{{ title|trans }} {{ 'form.user.title_common'|trans }}</h1></div>

            <div class="flex-container">
                {{ form_row(form.lastname) }}
                {{ form_row(form.firstname) }}
                {{ form_row(form.email) }}
                {{ form_row(form.function) }}

                <div class="form-row">
                    <div class="js-select-wrapper select-wrapper has-text">
                        {{ form_widget(form.role, {'attr':{'id':'front_user_form_role'}}) }}
                        {{ form_label(form.role) }}
                    </div>
                    <small class="error">{{ form_errors(form.role) }}</small>
                </div>

                <div class="form-row">
                    <div class="user-definition">
                        <div>
                            <span class="role">{{ 'form.user.definition.requestor'|trans }} : </span>
                            {{ 'form.user.definition.requestor_definition'|trans }}
                        </div>
                        <div>
                            <span class="role">{{ 'form.user.definition.buyer'|trans }} : </span>
                            {{ 'form.user.definition.buyer_definition'|trans }}
                        </div>
                        <div>
                            <span class="role">{{ 'form.user.definition.account_manager'|trans }} : </span>
                            {{ 'form.user.definition.account_manager_definition'|trans }}
                        </div>
                    </div>
                </div>
            </div>

            {% set cbsStyles = '' %}
            {% if form.role.vars.value == admin_role %}
                {% set cbsStyles = 'display:none' %}
            {% endif %}

            <div id="js-sites-cbs" class="js-checkbox-container form--cb UserForm-sites" style="{{ cbsStyles }}">
                {{ form_label(form.sites) }}
                <div class="UserForm-sitesError js-cb-errors">
                    <div id="js-checkboxes" class="UserForm-checkboxes">
                        {% for child in form.sites.children %}
                            {{  form_row(child) }}
                        {% endfor %}
                    </div>
                    <small id="front_user_form[sites][]-error" class="error checkbox-errors" for="front_user_form[sites][]">
                        {{ form_errors(form.sites) }}
                    </small>
                </div>
            </div>

            {% if not isDisabled %}
                <div class="Form-group Buttons-group user-actions">
                    {{ form_row(form.save) }}
                    <a class="cancel-edit" href="{{ path('front.company.users') }}">{{ 'site.form.cancel'|trans }}</a>
                </div>
            {% endif %}

        {{ form_end(form) }}

        <script>
            'use strict';

            document.addEventListener('DOMContentLoaded', function () {

                var $checkboxesContainer = $("#js-checkboxes");
                var $cbsSection = $('#js-sites-cbs');
                var $rolesSelect = $('#front_user_form_role');
                var sites_backup = [];
                var last_role = '{{ form.role.vars.value }}';

                window.UI.Select.init();

                // Tweak validation of the sites checkbox (1 site checked required unless role is admin)
                $('input[name="front_user_form[sites][]"]').rules('add', {
                    required : {
                        depends : function() {
                            return ($('#front_user_form_role').val() !== '{{ admin_role }}');
                        }
                    },
                    messages: {
                        required : '{{ 'form.user.sites.mandatory'|trans({}, 'AppBundle')  }}'
                    }
                });


                // Hide/Show checkboxes when selecting a role
                $rolesSelect.on('change', function () {

                    // backup states then check all inputs and hide the section
                    if ($rolesSelect.val() === '{{ admin_role }}') {

                        sites_backup = [];

                        $checkboxesContainer.find(":checkbox").each(function(idx, el){
                            if (el.checked) {
                                sites_backup.push(el.value);
                            }

                            el.checked = true;
                        });

                        $cbsSection.hide();

                        // Restore the checked inputs and show the section
                    } else if (last_role === '{{ admin_role }}') {

                        var c = 0;

                        $checkboxesContainer.find(":checkbox").each(function (idx, el) {
                            c++;
                            el.checked = (sites_backup.indexOf(el.value) !== -1);
                        });

                        $cbsSection.show();
                    }

                    last_role = $rolesSelect.val();
                });

                // backup checked sites
                $checkboxesContainer.find(":checkbox").each(function(idx, el){
                    if (el.checked) {
                        sites_backup.push(el.value);
                    }
                });


            });
        </script>
    </div>
</div>
{% endblock %}