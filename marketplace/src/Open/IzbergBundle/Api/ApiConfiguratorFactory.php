<?php

namespace Open\IzbergBundle\Api;

class ApiConfiguratorFactory
{
    public static function createApiConfigurator(array $config): ApiConfigurator
    {

        $defaultConfiguration = self::buildConfiguration('default', $config['default_connection']);
        $apiConfigurator = new ApiConfigurator($defaultConfiguration);

        foreach($config['other_connections'] as $configurationName => $settings) {
            $apiConfigurator->addConfiguration(self::buildOtherConfiguration($defaultConfiguration, $configurationName, $settings));
        }

        return $apiConfigurator;
    }

    private static function buildConfiguration(string $configName, $config): ApiConfiguration
    {
        return (new ApiConfiguration())
            ->setDomain($config['domain'])
            ->setVersion($config['version'])
            ->setProtocol($config['protocol'])
            ->setAccessToken($config['access_token'])
            ->setApplicationId($config['application_id'])
            ->setApplicationNamespace($config['application_namespace'])
            ->setSecretKey($config['secret_key'])
            ->setEmail($config['email'])
            ->setUsername($config['username'])
            ->setFirstName($config['first_name'])
            ->setLastName($config['last_name'])
            ->setSellerEmailDomain($config['seller_email_domain'])
            ->setConfigurationName($configName)
            ->setAudience($config['audience'])
            ->setIdentityUrl($config['identity_url'])
            ->setClientId($config['client_id'])
            ->setClientSecret($config['client_secret'])
            ->setDomainId($config['domain_id']);
    }

    private static function buildOtherConfiguration(ApiConfiguration $defaultConfiguration, string $configurationName, array $settings): ApiConfiguration
    {
        return self::buildConfiguration($configurationName, $settings + $defaultConfiguration->toConfigArray());
    }
}
