{% extends '@OpenBack/base.html.twig' %}
{% trans_default_domain 'AppBundle' %}
{% block body %}


<div class="card-columns">
{% for domain,groups in domains %}
    {% set box_label = 'system.settings.' ~ domain ~ '.title' %}
        <div class="card">
            <h3 class="card-header">{{ box_label|trans }}</h3>
            <div class="card-body">
                <p class="card-text">
                    <ul>
                    {% for group,settings in groups %}
                        {% set group_title = 'system.settings.' ~ domain ~ '.' ~ group ~ '.title' %}
                        {% set group_link = 'system.settings.group' ~ domain ~ '.' ~ group ~ '.title' %}
                        <li>
                            <a href="{{ path('system.settings.group', {domain : domain, group : group }) }}">{{ group_title|trans }}</a>
                        </li>
                    {% endfor %}
                    </ul>
                </p>
            </div>
        </div>
{% endfor %}
</div>
{% endblock %}