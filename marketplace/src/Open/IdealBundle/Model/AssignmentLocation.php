<?php

namespace Open\IdealBundle\Model;

class AssignmentLocation
{
    private ?string $Id = null;
    private ?string $Uid = null;
    private ?string $Name = null;
    private ?string $Name_fr = null;
    private ?string $Street = null;
    private ?string $PostalCode = null;

    /**
     * @return string|null
     */
    public function getId(): ?string
    {
        return $this->Id;
    }

    /**
     * @param string|null $Id
     * @return AssignmentLocation
     */
    public function setId(?string $Id): AssignmentLocation
    {
        $this->Id = $Id;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getName(): ?string
    {
        return $this->Name;
    }

    /**
     * @param string|null $Name
     * @return AssignmentLocation
     */
    public function setName(?string $Name): AssignmentLocation
    {
        $this->Name = $Name;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getStreet(): ?string
    {
        return $this->Street;
    }

    /**
     * @param string|null $Street
     * @return AssignmentLocation
     */
    public function setStreet(?string $Street): AssignmentLocation
    {
        $this->Street = $Street;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getPostalCode(): ?string
    {
        return $this->PostalCode;
    }

    /**
     * @param string|null $PostalCode
     * @return AssignmentLocation
     */
    public function setPostalCode(?string $PostalCode): AssignmentLocation
    {
        $this->PostalCode = $PostalCode;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getNameFr(): ?string
    {
        return $this->Name_fr;
    }

    /**
     * @param string|null $Name_fr
     * @return AssignmentLocation
     */
    public function setNameFr(?string $Name_fr): AssignmentLocation
    {
        $this->Name_fr = $Name_fr;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getUid(): ?string
    {
        return $this->Uid;
    }

    /**
     * @param string|null $Uid
     * @return AssignmentLocation
     */
    public function setUid(?string $Uid): AssignmentLocation
    {
        $this->Uid = $Uid;
        return $this;
    }


}