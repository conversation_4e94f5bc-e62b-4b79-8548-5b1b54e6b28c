{% trans_default_domain 'AppBundle' %}

<div id="Advanced-Search-mobile">
    <h4>{{ 'search.advanced_search.title'| trans }}</h4>

    {{ form_start(form, {'attr': {'id': 'form_adv_search'}}) }}

    <div class="Sidebar-Criteria">
        <div class="sidebar-block">
            <div class="sidebar-block-title" onclick="toggleVisibleMob('department-list')">
                <h2>{{ 'search.sidebar.departments'|trans }}</h2>
                <i class="department-list-arrow arrow down"></i>
            </div>

            <div class="dropdown-mobile department-list">
                <div class="h-line"></div>
                {% if retour_dept is defined and retour_dept is not null %}
                    <div class="department-link-bold">
                        <a href="{{ path('front.search',  {'category':  retour_dept, 'text': ''}) }}"><i>{{ 'search.sidebar.up' | trans }}</i></a>
                    </div>
                {% endif %}
                {% for dep_infos in departments %}
                    <div class="department-link">
                        <a href="{{ path('front.search',  {'category':  dep_infos.id, 'text': ''}) }}">{{ dep_infos.label }} <i>({{ dep_infos.quantity }})</i></a>
                    </div>
                {% endfor %}
            </div>
        </div>
        <div class="sidebar-block">
            <div class="sidebar-block-title" onclick="toggleVisibleMob('criteria-list')">
                <h2>{{ 'search.sidebar.refine_by'|trans }}</h2>
                <i class="criteria-list-arrow arrow down"></i>
            </div>

            <div class="dropdown-mobile criteria-list">
                {% for child in form.children %}
                    {% if child.vars.multiple is defined and child.vars.multiple and child.vars.name != "sort" %}
                        <div class="h-line"></div>
                        <div class="criteria-control">
                            <div class="select-label">
                                {{ form_label(child) }}
                            </div>
                            <div class="criteria-checkbox">
                                {{ form_widget(child) }}
                            </div>
                        </div>
                    {% elseif child.vars.multiple is defined and not child.vars.multiple  and child.vars.name != "sort" %}
                        <div class="h-line"></div>
                        <div class="criteria-control">
                            <div class="select-label">
                                {{ form_label(child) }}
                            </div>
                            <div class="select-search sort-element">
                                <div class="js-select-wrapper select-wrapper has-text">
                                    {{ form_widget(child) }}
                                    <i class="arrow down"></i>
                                </div>
                            </div>
                        </div>
                    {% else %}
                    {% endif %}
                {% endfor %}
            </div>
        </div>
    </div>

    {{ form_row(form.submit) }}

    {{ form_end(form) }}
</div>


{% block javascripts %}
    <script type="text/javascript">
        window.UI.Select.init();

        function toggleVisibleMob(classObject) {
            let object = document.getElementsByClassName(classObject);
            let arrow = document.getElementsByClassName(classObject + '-arrow');
            if(object.length > 0 && arrow.length > 0) {
                object[0].classList.toggle('show');
                arrow[0].classList.toggle('up');
            }
        }
    </script>
{% endblock %}