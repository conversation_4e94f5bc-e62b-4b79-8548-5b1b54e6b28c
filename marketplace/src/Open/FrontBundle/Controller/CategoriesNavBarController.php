<?php

namespace Open\FrontBundle\Controller;

use AppBundle\Controller\MkoController;
use Exception;
use Open\IzbergBundle\Service\CategoryService;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

class CategoriesNavBarController extends MkoController
{
    /**
     * @throws Exception
     */
    public function getAction(Request $request, CategoryService $categoryService): Response
    {
        $categories = $categoryService->getCachedClassifiedCategories();

        return $this->render('@OpenFront/menu:header_navbar.html.twig', [
            "categories" => $categories,
            "ignored_categories" => $this->getParameter("ignored_categories_id"),
            "from" => $request->query->get("from")
        ]);
    }

    /**
     * @throws Exception
     */
    public function getMobileAction(Request $request, CategoryService $categoryService): Response
    {
        $categories = $categoryService->getCachedClassifiedCategories();
        return $this->render('@OpenFront/menu:dropdown-category-mobile.html.twig', [
            "categories" => $categories,
            "from" => $request->query->get("from")
        ]);
    }
}