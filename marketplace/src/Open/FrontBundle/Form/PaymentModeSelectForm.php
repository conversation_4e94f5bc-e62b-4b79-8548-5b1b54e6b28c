<?php
/**
 * Created by PhpStorm.
 * User: PPH04712
 * Date: 05/06/2018
 * Time: 17:03
 */


namespace Open\FrontBundle\Form;

use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\HiddenType;
use Symfony\Component\Form\Extension\Core\Type\SubmitType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

class PaymentModeSelectForm extends AbstractType {

    public const PAYMENT_PRE_CARD = 'preCreditCard';
    public const PAYMENT_PRE_WIRE = 'preTransferWire';
    public const PAYMENT_TERM = 'termTransferWire';
    public const PAYMENT_TERM_LABEL = 'paymentTermLabel';

    public const TRANS_PREFIX = 'payment.select_mode.';

    public function buildForm(FormBuilderInterface $builder, array $options)
    {
        $choices = [];
        $data = "";

        if ($options[self::PAYMENT_TERM]){
            $choices[self::TRANS_PREFIX.self::PAYMENT_TERM] = self::PAYMENT_TERM;
            $data = self::PAYMENT_TERM;
        }

        if ($options[self::PAYMENT_PRE_WIRE]){
            $choices[self::TRANS_PREFIX.self::PAYMENT_PRE_WIRE] = self::PAYMENT_PRE_WIRE;
            if (empty($data)){
                $data = self::PAYMENT_PRE_WIRE;
            }
        }

        if ($options[self::PAYMENT_PRE_CARD]){
            $choices[self::TRANS_PREFIX.self::PAYMENT_PRE_CARD] = self::PAYMENT_PRE_CARD;
            if (empty($data)){
                $data = self::PAYMENT_PRE_CARD;
            }
        }

        $builder
            ->add(
                "term",
                ChoiceType::class,
                array(
                    'choices' => $choices,
                    'multiple' => false,
                    'expanded' => true,
                    "data" => $data
                )

            )
            ->add(
                "addressId",
                HiddenType::class
            )
            ->add(
                "submit",
            SubmitType::class,
                array(
                    "attr" => array (
                        "value" => "submit",
                        "class" => "Button button_margin"
                    ),
                    'label' => self::TRANS_PREFIX.'select'
                )
            )->add(
                "validationNumber",
                TextType::class
            )->setMethod('POST');
    }

    public function configureOptions(OptionsResolver $resolver)
    {
        $resolver->setDefaults(array(
            self::PAYMENT_PRE_CARD => false,
            self::PAYMENT_PRE_WIRE => false,
            self::PAYMENT_TERM => false,
            'translation_domain' => 'AppBundle'
        ));
    }


}