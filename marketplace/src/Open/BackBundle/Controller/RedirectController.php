<?php

namespace Open\BackBundle\Controller;

use App<PERSON><PERSON>le\Controller\MkoController;
use AppB<PERSON>le\Entity\Redirect;
use AppBundle\Repository\RedirectRepository;
use AppBundle\Services\RedirectBddService;
use Doctrine\ORM\EntityManagerInterface;
use Open\BackBundle\Form\FilterPageType;
use Open\BackBundle\Form\RedirectForm;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Security;
use Symfony\Component\Form\FormError;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Contracts\Translation\TranslatorInterface;

class RedirectController extends MkoController
{

    /**
     * @Route("/redirect/list/{qualifier}", name="admin.redirect.list")
     * @Security("is_granted('ROLE_ADMIN') or is_granted('ROLE_OPERATOR')")
     */
    public function listRedirectAction(Request $request, RedirectBddService $redirectBddService, $qualifier): Response
    {
        $form = $this->createForm(FilterPageType::class);
        $form->handleRequest($request);
        if ($form->isSubmitted() && $form->isValid()) {
            $filter = $form->getData();
        } else {
            $filter = [];
        }

        $paginator = $redirectBddService->getCustomFilteredPaginator(1, 25, $request, $filter, $qualifier);


        //render list of users
        return $this->render('@OpenBack/Redirect/list.html.twig', [
            'pagination' => $paginator,
            'qualifier_val' => $qualifier,
            'form' => $form->createView(),
            'locale' => $request->getLocale()
        ]);
    }

    /**
     * @Route("/redirect/{id}/delete", name="admin.redirect.delete")
     * @Security("is_granted('ROLE_ADMIN') or is_granted('ROLE_OPERATOR')")
     */
    public function deleteRedirectAction(EntityManagerInterface $em,RedirectRepository $redirectRepository, TranslatorInterface $translator, $id): RedirectResponse
    {
        $redirect = $redirectRepository->find($id);

        try {
            $em->remove($redirect);
            $em->flush();

            $this->addFlash('success', $translator->trans('redirect.form.delete.success', [], 'AppBundle'));
        } catch (\Exception $e) {
            $this->addFlash('success', $translator->trans('redirect.form.delete.error', [], 'AppBundle'));
        }

        return $this->redirectToRoute('admin.redirect.list', ['qualifier' => 'all']);
    }

    /**
     * @Route("/redirect/{id}/edit", name="admin.redirect.edit")
     * @Route("/redirect/add", name="admin.redirect.add")
     * @Security("is_granted('ROLE_ADMIN') or is_granted('ROLE_OPERATOR')")
     */
    public function addRedirectAction(Request $request, EntityManagerInterface $em, TranslatorInterface $translator, $id = null)
    {
        $redirectRepository = $em->getRepository(Redirect::class);

        if (is_null($id)) {
            $redirect = new Redirect();
        } else {
            $redirect = $redirectRepository->find($id);
        }

        $form = $this->createForm(
            RedirectForm::class,
            $redirect
        );

        if ($redirect->getOrigin() === $redirect->getDestination()) {
            $form->get('origin')->addError(new FormError($translator->trans('form.redirect.samedest', [], 'validators')));
            $form->get('destination')->addError(new FormError($translator->trans('form.redirect.sameorigin', [], 'validators')));
        }

        $form->handleRequest($request);
        if ($form->isSubmitted() && $form->isValid()) {

            /** @var Redirect $redirect */
            $redirect = $form->getData();

            // Remove trailing slashes
            $dest = rtrim($redirect->getDestination(), '/');

            // Update value in entity
            $redirect->setDestination($dest);

            try {
                $em->persist($redirect);
                $em->flush();

                if (!is_null($id)) {
                    $this->addFlash('success', $translator->trans('redirect.form.submit.success.update', [], 'AppBundle'));
                } else {
                    $this->addFlash('success', $translator->trans('redirect.form.submit.success.create', [], 'AppBundle'));
                }

                // Force redirect to see the slashes rtrim effects
                return $this->redirectToRoute('admin.redirect.edit', ['id' => $redirect->getId()]);

            } catch (\Exception $e) {

                if (!is_null($id)) {
                    $this->addFlash('error', $translator->trans('redirect.form.submit.error.update', [], 'AppBundle'));
                } else {
                    $this->addFlash('error', $translator->trans('redirect.form.submit.error.create', [], 'AppBundle'));
                }
            }

        }

        return $this->render(
            '@OpenBack/Redirect/edit.html.twig',
            [
                'form' => $form->createView(),
                'id' => $id
            ]
        );
    }
}