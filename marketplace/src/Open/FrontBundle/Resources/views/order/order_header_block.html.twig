{% set locale = app.request.locale %}
{% set shippingDelay = 2 %}

{% if order.buyerShippingAddress %}
    {% set recipientPhone = order.buyerShippingAddress.phone %}
{% else %}
    {% set recipientPhone = ' - ' %}
{% endif %}
{% trans_default_domain 'AppBundle' %}

<div class="detail-order-header-back">
    <section class="detail-order-header-back-link desktop-only">
        <a class="previous-page" href="{{ path('front.ordersToValidateList.status', {'status': order.status}) }}?rid={{ order.id }}#__{{order.status}}"><i class="arrow left"></i> {{ 'orders.detail.go_back'|trans }}</a>
    </section>
    <section class="detail-order-header-back-link mobile-only">
        <a class="previous-page" href="{{ path('front.ordersToValidateList.status', {'status': order.status, 'isMobile': true }) }}">
            <img alt="go back" src="{{ asset('images/red-arrow.svg') }}">
            <img alt="go back" src="{{ asset('images/red-arrow.svg') }}">
            {{ 'orders.list.back'|trans }}
        </a>
    </section>
    <div style="text-align:center" class="mobile-only">
        <section class="account-header account-title">{{ 'orders.detail.mobile_title' | trans }}</section>
    </div>
</div>
<div class="detail-order-info">
    <section class="detail-order-info-col">
        {% set shippingAddress = order.buyerShippingAddress %}
        <section class="detail-order-info-row">
            <span>{{ 'orders.detail.issuer' | trans }} : </span>
            <section>{{ order.creator.firstname }} {{ order.creator.lastname }}</section>
        </section>
        {% if shippingAddress is not null %}
            <section class="detail-order-info-row">
                <span>{{ 'orders.detail.recipient' | trans }} : </span>
                <section>{% if shippingAddress.contact is not null %}{{ shippingAddress.contact }}{% endif %}</section>
            </section>
            <section class="detail-order-info-row">
                <span>{{ 'orders.detail.tel' | trans }} : </span>
                <section>{{ recipientPhone }}</section>
            </section>
            <section class="detail-order-info-row adress-row">
                <span>{{ 'orders.detail.shipping_address' | trans }} : </span>
                <section class="uppercase">
                    {{ shippingAddress.address }}
                    {% if shippingAddress.address2 is not null %}{{ shippingAddress.address2 }}{% endif %}
                    ,
                    {{ shippingAddress.zipcode }}
                    {{ shippingAddress.city }}
                    {% set country = 'country.'~shippingAddress.country.code %}
                    {{ country | trans }}
                </section>
            </section>
            <section class="detail-order-info-row">
                {% if shippingAddress is not null and shippingAddress.comment|trim is not empty %}
                    <span>{{ 'orders.detail.comments' | trans }} : </span><section> {{ shippingAddress.comment }}</section>
                {% endif %}
            </section>
        {% endif %}
    </section>

    <section class="detail-order-info-col f-ai-fe">

        <section class="detail-order-info-row j-c-right">
            <span>{{ 'orders.detail.order_date'|trans }} : </span> {{ order.orderCreatedAt | localizeddate('short', 'none', locale)}}
        </section>
{#        {% if order.status == "ORDERED" %}#}
{#            <section class="detail-order-info-row">#}
{#                <span>{{ 'orders.detail.order_id' | trans }} : </span> {{ order.order.izbergId }}#}
{#            </section>#}
{#        {% endif %}#}
        <section class="detail-order-info-row">
            <span>{{ 'orders.detail.cart_id' | trans }} : </span> {{ order.id }}
        </section>
{#        {% if not order.status %}#}
{#            <section class="detail-order-info-row">#}
{#                <span>{{ 'orders.detail.cart_id' | trans }} : </span> {{ order.merchantOrderEntity.metaCart.id }}#}
{#            </section>#}
{#            <section class="detail-order-info-row">#}
{#                <span>{{ 'orders.detail.shipping_delay' | trans }} : </span> {{ shippingDelay }}{{ '' }}#}
{#            </section>#}
{#        {% else %}#}
{#            <section class="detail-order-info-row">#}
{#                <span>{{ 'orders.detail.shipping_date' | trans }} : </span> {{ shippingDate }}#}
{#            </section>#}
{#        {% endif %}#}
        <section class="detail-order-info-row">
            <span>{{ 'orders.detail.status' | trans }} : </span> {{ ('orders.header.'~order.status) | trans  }}
        </section>
        {% if order.status == 'REJECTED' or order.status == 'CANCELLED' %}
        <section class="detail-order-info-row">
            <span>{{ 'cart.reject.comment' | trans  }} : </span>
            {{order.statusComment}}
        </section>
        {% endif %}
        <section class="detail-order-info-btn j-c-right action-btn-container">
            {% if order.assigned %}
                <a href="#" class="btn-white mr-10" style="background: #28a745 !important; color: white" onclick="window.UI.Modal.onValidate('{{ path('cart.details.accept') }}', {{ order.id }}, '{{ 'orders.modal.confirm_validation'|trans({}, 'AppBundle')|raw }}', '{{ 'orders.modal.confirm_error'|trans({}, 'AppBundle')|raw }}', '{{ path('front.ordersToValidate.list') }}', '{{ csrf_token('cart_accept') }}')">
                    {{ 'btn.accept' | trans | upper }}
                </a>
                <a href="#" style="background: #dc3545 !important; color: white"  class="btn-white mt-10" onclick="window.UI.Modal.onReject('{{ path('cart.details.reject') }}', {{ order.id }}, '{{ 'orders.modal.confirm_reject'|trans({}, 'AppBundle')|raw }}', '{{ 'wishlist.item.delete.error'|trans({}, 'AppBundle')|raw }}', '{{ path('front.ordersToValidate.list') }}', '{{ csrf_token('cart_reject') }}')">
                    {{ 'btn.reject' | trans | upper }}
                </a>
            {% endif %}
        </section>
    </section>
</div>
