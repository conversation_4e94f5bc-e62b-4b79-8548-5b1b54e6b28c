<?php

namespace Open\IzbergBundle\Dto;

class Request
{
    public const POST = "POST";
    public const PUT = "PUT";
    public const GET = "GET";
    public const PATCH = "PATCH";
    public const DELETE = "DELETE";

    private string $url;
    private ?string $method;
    private ?array $data = null;
    private ?array $header = null;
    private ?array $query = null;
    private bool $returnData = true;

    /**
     * @param string $url
     */
    public function __construct(string $url)
    {
        $this->url = $url;
    }

    /**
     * @return string
     */
    public function getUrl(): string
    {
        return $this->url;
    }

    /**
     * @param string $url
     * @return Request
     */
    public function setUrl(string $url): Request
    {
        $this->url = $url;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getMethod(): ?string
    {
        return $this->method;
    }

    /**
     * @param string|null $method
     * @return Request
     */
    public function setMethod(?string $method): Request
    {
        $this->method = $method;
        return $this;
    }

    /**
     * @return array|null
     */
    public function getData(): ?array
    {
        return $this->data;
    }

    /**
     * @param array|null $data
     * @return Request
     */
    public function setData(?array $data): Request
    {
        $this->data = $data;
        return $this;
    }

    /**
     * @return array|null
     */
    public function getHeader(): ?array
    {
        return $this->header;
    }

    /**
     * @param array|null $header
     * @return Request
     */
    public function setHeader(?array $header): Request
    {
        $this->header = $header;
        return $this;
    }

    /**
     * @return bool
     */
    public function isReturnData(): bool
    {
        return $this->returnData;
    }

    /**
     * @param bool $returnData
     * @return Request
     */
    public function setReturnData(bool $returnData): Request
    {
        $this->returnData = $returnData;
        return $this;
    }

    /**
     * @return array|null
     */
    public function getQuery(): ?array
    {
        return $this->query;
    }

    /**
     * @param array|null $query
     * @return Request
     */
    public function setQuery(?array $query): Request
    {
        $this->query = $query;
        return $this;
    }
}
