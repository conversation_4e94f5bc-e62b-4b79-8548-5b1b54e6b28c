{% extends '@OpenFront/order/orders_list.html.twig' %}
{% trans_default_domain 'AppBundle' %}

{% block orderlist %}
    {% if orders|length > 0 %}
        <table class="table desktop-only orders-table">
            <thead>
            <tr>
                <th style="width: 120px;">{{ 'orders.list.block.order_title' | trans }}</th>
                <th>{{ 'orders.list.block.supplier' | trans }}</th>
                <th>{{ 'orders.list.block.date_title' | trans }}</th>
                <th>{{ 'orders.list.block.cart_number' | trans }}</th>
                <th>{{ 'orders.list.block.address_title' | trans }}</th>
                <th>{{ 'orders.list.block.recipient' | trans }}</th>
                <th class="bd-none talign-r min-w-90">{{ 'orders.list.block.total_title' | trans }}</th>
                <th></th>
            </tr>
            </thead>
            <tbody>
            {# @var order \AppBundle\Entity\MerchantOrder#}
            {% for order in orders %}
                <tr onclick="window.location ='{{ path('front.order.detail', {'id': order.id}) }}';">
                    <td>{{ order.id }} {% if order.gift %}
                            <img class="pl-3" src="{{ asset('images/gift.svg') }}" alt="" height="16">
                        {% endif %} {% if order.risk %}
                            <img class="pl-3" src="{{ asset('images/ico-chimie3.svg') }}" title="{{ 'cart.risk_info' | trans }}" alt="" height="16">
                        {% endif %}
                    </td>
                    <td>{{ order.merchantName }}</td>
                    <td>{{ order.createdAt | localizeddate('short', 'none', locale)}}</td>
                    <td>{{ order.metaCart.id }}</td>
                    <td>
                        {% set shippingAddress = order.metaCart.buyerShippingAddress %}

                        {% if shippingAddress is not null %}
                            {{ shippingAddress.address }}
                            {% if shippingAddress.address2 is not null %}{{ shippingAddress.address2 }}{% endif %}
                            ,
                            {{ shippingAddress.zipcode }}
                            {{ shippingAddress.city }}
                            {% set country = 'country.'~shippingAddress.country.code %}
                            {{ country | trans }}
                        {% endif %}
                    </td>
                    <td>
                        {% if shippingAddress is not null %}
                            {% if shippingAddress.contact is not null %}{{ shippingAddress.contact }}{% endif %}
                        {% endif %}
                    </td>
                    <td class="bd-none talign-r min-w-90">{{ order.amount|number_format(2, '.') }} {{ order.currency }}</td>
                    {# Reorder #}
                    <td style="white-space: nowrap;">
                        <div class="tooltip-total tooltip-buy-again" style="margin-right: 0">
                            <button class="btn btn-sm btn-danger" type="button" onclick="event.stopPropagation(); window.UI.Modal.onCancel('{{ path('cart.details.cancel') }}', {{ order.metaCart.id }}, {{ order.id }} , '{{ 'orders.modal.confirm_cancel'|trans({}, 'AppBundle')|raw }}','{{ 'orders.modal.field.reminder'|trans({}, 'AppBundle')|raw }}', '{{ 'wishlist.item.delete.error'|trans({}, 'AppBundle')|raw }}', '{{ path('front.orders.list', {status:'pending-manager-validation'}) }}','{{ order.status }}', '{{ csrf_token('cart_cancel') }}')" style="
                            height: auto !important;
                            padding: 0px 6px !important;
                            font-size: 12px;
                            letter-spacing: initial;
                        ">{{ 'btn.cancel' | trans }}</button>
                            <div class="tooltiptext">
                                {{ 'orders.list.link.cancel' | trans }}
                            </div>
                        </div>
                        <div data-reorder="{{ path('front.order.reorder', {'merchantOrderId': order.id}) }}"  data-checkReOrder="{{ path('front.order.reorder.check', {'merchantOrderId': order.id}) }}" class="tooltip-total tooltip-buy-again">
                            <img src="{{ asset('images/icone-panier-black.svg') }}">
                            <div class="tooltiptext">
                                {{ 'orders.list.link.buy_again' | trans }}
                            </div>
                        </div>
                    </td>
                </tr>
            {% endfor%}
            </tbody>
        </table>
    {% else %}
        <div class="orders-list-empty">
            <h6 style="text-align: center">{{ 'orders.empty.validation' | trans }}</h6>
        </div>
    {% endif %}
{% endblock %}
