<?php

namespace Open\IzbergBundle\Model;

use JMS\Serializer\Annotation as JSM;
use JMS\Serializer\Annotation\Type;

/**
 * Object to map an izberg credit note line object
 * @JSM\ExclusionPolicy("none")
 */
class CreditNoteLine
{
    /**
     * @Type("string")
     */
    private $orderItem;

    /**
     * @return mixed
     */
    public function getOrderItem()
    {
        return $this->orderItem;
    }

    /**
     * @param mixed $orderItem
     */
    public function setOrderItem($orderItem): void
    {
        $this->orderItem = $orderItem;
    }
}
