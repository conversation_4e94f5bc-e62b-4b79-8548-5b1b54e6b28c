<?php

namespace Open\FrontBundle\Service;

class SuggestionResult
{

    private string $term;
    private string $type;
    private ?string $image;
    private ?int $id;
    private string $nameSlug;
    private string $categorySlug;


    /**
     * @return string
     */
    public function getTerm(): string
    {
        return $this->term;
    }

    /**
     * @param string $term
     * @return SuggestionResult
     */
    public function setTerm(string $term): SuggestionResult
    {
        $this->term = $term;
        return $this;
    }

    /**
     * @return string
     */
    public function getType(): string
    {
        return $this->type;
    }

    /**
     * @param string $type
     * @return SuggestionResult
     */
    public function setType(string $type): SuggestionResult
    {
        $this->type = $type;
        return $this;
    }

    /**
     * @return int|null
     */
    public function getId(): ?int
    {
        return $this->id;
    }

    /**
     * @param int|null $id
     * @return SuggestionResult
     */
    public function setId(?int $id): SuggestionResult
    {
        $this->id = $id;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getImage(): ?string
    {
        return $this->image;
    }

    /**
     * @param string|null $image
     * @return $this
     */
    public function setImage(?string $image): self
    {
        $this->image = $image;
        return $this;
    }

    /**
     * @return string
     */
    public function getNameSlug(): string
    {
        return $this->nameSlug;
    }

    /**
     * @param string $nameSlug
     * @return SuggestionResult
     */
    public function setNameSlug(string $nameSlug): SuggestionResult
    {
        $this->nameSlug = $nameSlug;
        return $this;
    }

}
