<?php

namespace <PERSON><PERSON><PERSON>\Handler;

use <PERSON><PERSON><PERSON>\Model\Messenger\OfferMessage;
use <PERSON><PERSON><PERSON>\Service\ElasticService;
use Psr\Log\LoggerInterface;
use Symfony\Component\Messenger\Handler\MessageHandlerInterface;

class OfferMessageHandler implements MessageHandlerInterface
{
    private LoggerInterface $logger;
    private ElasticService $elasticService;

    public function __construct(
        LoggerInterface $logger,
        ElasticService  $elasticService
    )
    {
        $this->logger = $logger;
        $this->elasticService = $elasticService;
    }

    public function __invoke(OfferMessage $message)
    {
        $this->logger->debug("DEBUG - OfferMessage received: " . json_encode($message));
        $this->logger->debug("DEBUG - DocumentsList to be indexed: " . json_encode($message->getOffersDocumentsList()));

        $result = $this->elasticService->indexDocuments($message->getOffersDocumentsList());
        $this->elasticService->logResult($this->logger, $result);
    }
}