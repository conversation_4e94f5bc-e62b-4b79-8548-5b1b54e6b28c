<?php


namespace AppBundle\Form\Type;

use AppBundle\Entity\ShippingRecipient;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\TextareaType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

class ShippingRecipientType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add(
                'recipient',
                TextType::class,
                array(
                    'label' => 'cart.shipping.recipient_name',
                    'required' => true,
                ))
            ->add(
                'phone',
                TextType::class,
                array(
                    'label' => 'cart.shipping.recipient_tel',
                    'required' => false,
                ))
            ->add(
                'comment',
                TextareaType::class,
                array(
                    'label' => 'cart.shipping.recipient_comment',
                    'required' => false,
                ));
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults(array(
            'data_class' => ShippingRecipient::class,
            'translation_domain' => 'AppBundle',
        ));
    }
}