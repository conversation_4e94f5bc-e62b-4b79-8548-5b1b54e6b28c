[2025-05-22 10:37:00.559679] analysed_logs.INFO: user <EMAIL> is authenticated successfully {"event_name":"USER AUTHENTICATED","username":"********","user":{"AppBundle\\Entity\\User":{"username":"********","enabled":true,"email":"<EMAIL>","firstname":"<PERSON>","lastname":"<PERSON>YO<PERSON>"}}} []

[2025-05-22 10:37:45.959798] analysed_logs.ERROR: An error occurred while invoking Izberg API. Izberg code: {"event_name":"IZBERG_API_ERROR","username":"********","user_email":"8c6f81702cd1c23bf4bc2af5d4886694ed5974bd","applicationId":"85","httpStatusCode":404,"izbergMessage":null,"izbergCode":null,"apiCall":"https://api.iceberg.technology/v1/productoffer/73785156/","response time":0.19252490997314453} []
[2025-05-22 10:44:19.703471] analysed_logs.ERROR: An error occurred while invoking Izberg API. Izberg code: {"event_name":"IZBERG_API_ERROR","username":"********","user_email":"8c6f81702cd1c23bf4bc2af5d4886694ed5974bd","applicationId":"85","httpStatusCode":404,"izbergMessage":null,"izbergCode":null,"apiCall":"https://api.iceberg.technology/v1/productoffer/73785156/","response time":0.17658305168151855} []
