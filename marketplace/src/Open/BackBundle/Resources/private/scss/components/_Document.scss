@import "../variables";

.DocumentForm-uploadField .has-error li{
  list-style-type: none;
  color :red;
}

.DocumentForm-nodocs .Message-item {
  margin-left: 20px;
  border-radius: 5px;
  font-style: italic;
  color: #9b9b9b;
}

/*.Button--upload {
  float: right;
  right: 0;
  top: -6px;
  padding: 5px 10px;
  //border: 1px solid $APPLI_PURPLE;
  color: #4E4F56;
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
  font-weight: normal;
  background: transparent;
  cursor: pointer;
}*/

.DocumentForm-docs {
  margin-top: 10px;
  margin-bottom: 20px;
  width: 100%;
  list-style-type: none;
  padding-left: 0;
}

.DocumentForm-docs li {
  list-style-type: none;
  position: relative;
  font-style: italic;
  margin-left:20px;
  padding-bottom: 15px;
}

.DocumentForm-docs li a:not([href]):not([tabindex]) {
  color: #9b9b9b;
  display: inline-block;
}

.DocumentForm-docs li a {
  color: #9b9b9b;
  display: inline-block;
}

.DocumentForm-docs li a:hover {
  text-decoration: none;
  color: #A2C617;
}

.DocumentForm-docs li .IconMax {
  position: absolute;
  right: 5px;
  top: 6px;
  fill: #9b9b9b;
  pointer-events: all;
}

.DocumentForm-docs li .IconMax :hover {
  fill: #A2C617;
}

input[type="file"] {
  display: none;
}

.IconMax {
  width: 32px;
  height: 32px;
  display: block;
  cursor: pointer;
}
