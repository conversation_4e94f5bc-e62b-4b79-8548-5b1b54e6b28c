<?php

namespace Open\IdealBundle\Model;

use AppBundle\Util\Str;

class MainRecord
{
    private ?string $Id = null;
    private ?string $EmailAddress = null;
    private ?string $SecondaryEmailAddress = null;
    private ?string $EmployeeId = null;
    private ?string $FirstName = null;
    private ?string $PhoneNumber = null;
    private ?string $InternalPhoneNumber = null;
    private ?string $LastName = null;
    private ?string $MobileNumber = null;
    private ?string $RIGPhoneNumber = null;
    private ?string $ToipPhoneNumber = null;
    private ?string $SecondRIGPhoneNumber = null;
    private ?string $SecondInternalPhoneNumber = null;
    private ?string $SecondMobileNumber = null;
    private ?string $UserRecordWhenIn = null;
    private ?string $UserRecordWhenOut = null;
    private ?string $CostCenterText = null;
    private ?EmployeeType $EmployeeType = null;
    private ?PersonalTitle $PersonalTitle = null;
    private ?Organization $Organization = null;
    private ?Company $Company = null;
    private ?AssignmentLocation $AssignmentLocation = null;
    private ?WorkingLocation $WorkingLocation = null;
    private ?Manager $Manager = null;
    private ?PreferredLanguage $PreferredLanguage = null;
    private ?CostCenter $CostCenter = null;
    private ?AssignmentCompany $AssignmentCompany = null;

    /**
     * @return string|null
     */
    public function getId(): ?string
    {
        return $this->Id;
    }

    /**
     * @param string|null $Id
     * @return MainRecord
     */
    public function setId(?string $Id): MainRecord
    {
        $this->Id = $Id;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getEmailAddress(): ?string
    {
        return $this->EmailAddress;
    }

    /**
     * @param string|null $EmailAddress
     * @return MainRecord
     */
    public function setEmailAddress(?string $EmailAddress): MainRecord
    {
        $this->EmailAddress = $EmailAddress;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getEmployeeId(): ?string
    {
        return $this->EmployeeId;
    }

    /**
     * @param string|null $EmployeeId
     * @return MainRecord
     */
    public function setEmployeeId(?string $EmployeeId): MainRecord
    {
        $this->EmployeeId = $EmployeeId;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getFirstName(): ?string
    {
        return $this->FirstName;
    }

    /**
     * @param string|null $FirstName
     * @return MainRecord
     */
    public function setFirstName(?string $FirstName): MainRecord
    {
        $this->FirstName = $FirstName;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getInternalPhoneNumber(): ?string
    {
        return $this->InternalPhoneNumber;
    }

    /**
     * @param string|null $InternalPhoneNumber
     * @return MainRecord
     */
    public function setInternalPhoneNumber(?string $InternalPhoneNumber): MainRecord
    {
        $this->InternalPhoneNumber = $InternalPhoneNumber;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getLastName(): ?string
    {
        return $this->LastName;
    }

    /**
     * @param string|null $LastName
     * @return MainRecord
     */
    public function setLastName(?string $LastName): MainRecord
    {
        $this->LastName = $LastName;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getMobileNumber(): ?string
    {
        return $this->MobileNumber;
    }

    /**
     * @param string|null $MobileNumber
     * @return MainRecord
     */
    public function setMobileNumber(?string $MobileNumber): MainRecord
    {
        $this->MobileNumber = $MobileNumber;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getRIGPhoneNumber(): ?string
    {
        return $this->RIGPhoneNumber;
    }

    /**
     * @param string|null $RIGPhoneNumber
     * @return MainRecord
     */
    public function setRIGPhoneNumber(?string $RIGPhoneNumber): MainRecord
    {
        $this->RIGPhoneNumber = $RIGPhoneNumber;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getToipPhoneNumber(): ?string
    {
        return $this->ToipPhoneNumber;
    }

    /**
     * @param string|null $ToipPhoneNumber
     * @return MainRecord
     */
    public function setToipPhoneNumber(?string $ToipPhoneNumber): MainRecord
    {
        $this->ToipPhoneNumber = $ToipPhoneNumber;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getUserRecordWhenIn(): ?string
    {
        return $this->UserRecordWhenIn;
    }

    /**
     * @param string|null $UserRecordWhenIn
     * @return MainRecord
     */
    public function setUserRecordWhenIn(?string $UserRecordWhenIn): MainRecord
    {
        $this->UserRecordWhenIn = $UserRecordWhenIn;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getUserRecordWhenOut(): ?string
    {
        return $this->UserRecordWhenOut;
    }

    /**
     * @param string|null $UserRecordWhenOut
     * @return MainRecord
     */
    public function setUserRecordWhenOut(?string $UserRecordWhenOut): MainRecord
    {
        $this->UserRecordWhenOut = $UserRecordWhenOut;
        return $this;
    }

    /**
     * @return PersonalTitle|null
     */
    public function getPersonalTitle(): ?PersonalTitle
    {
        return $this->PersonalTitle;
    }

    /**
     * @param PersonalTitle|null $PersonalTitle
     * @return MainRecord
     */
    public function setPersonalTitle(?PersonalTitle $PersonalTitle): MainRecord
    {
        $this->PersonalTitle = $PersonalTitle;
        return $this;
    }

    /**
     * @return Organization|null
     */
    public function getOrganization(): ?Organization
    {
        return $this->Organization;
    }

    /**
     * @param Organization|null $Organization
     * @return MainRecord
     */
    public function setOrganization(?Organization $Organization): MainRecord
    {
        $this->Organization = $Organization;
        return $this;
    }

    /**
     * @return Company|null
     */
    public function getCompany(): ?Company
    {
        return $this->Company;
    }

    /**
     * @param Company|null $Company
     * @return MainRecord
     */
    public function setCompany(?Company $Company): MainRecord
    {
        $this->Company = $Company;
        return $this;
    }

    /**
     * @return AssignmentLocation|null
     */
    public function getAssignmentLocation(): ?AssignmentLocation
    {
        return $this->AssignmentLocation;
    }

    /**
     * @param AssignmentLocation|null $AssignmentLocation
     * @return MainRecord
     */
    public function setAssignmentLocation(?AssignmentLocation $AssignmentLocation): MainRecord
    {
        $this->AssignmentLocation = $AssignmentLocation;
        return $this;
    }

    /**
     * @return WorkingLocation|null
     */
    public function getWorkingLocation(): ?WorkingLocation
    {
        return $this->WorkingLocation;
    }

    /**
     * @param WorkingLocation|null $WorkingLocation
     * @return MainRecord
     */
    public function setWorkingLocation(?WorkingLocation $WorkingLocation): MainRecord
    {
        $this->WorkingLocation = $WorkingLocation;
        return $this;
    }

    /**
     * @return Manager|null
     */
    public function getManager(): ?Manager
    {
        return $this->Manager;
    }

    /**
     * @param Manager|null $Manager
     * @return MainRecord
     */
    public function setManager(?Manager $Manager): MainRecord
    {
        $this->Manager = $Manager;
        return $this;
    }

    /**
     * @return PreferredLanguage|null
     */
    public function getPreferredLanguage(): ?PreferredLanguage
    {
        return $this->PreferredLanguage;
    }

    /**
     * @param PreferredLanguage|null $PreferredLanguage
     * @return MainRecord
     */
    public function setPreferredLanguage(?PreferredLanguage $PreferredLanguage): MainRecord
    {
        $this->PreferredLanguage = $PreferredLanguage;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getCostCenterText(): ?string
    {
        return $this->CostCenterText;
    }

    /**
     * @param string|null $CostCenterText
     * @return MainRecord
     */
    public function setCostCenterText(?string $CostCenterText): MainRecord
    {
        $this->CostCenterText = $CostCenterText;
        return $this;
    }

    /**
     * @return CostCenter|null
     */
    public function getCostCenter(): ?CostCenter
    {
        return $this->CostCenter;
    }

    /**
     * @param CostCenter|null $CostCenter
     * @return MainRecord
     */
    public function setCostCenter(?CostCenter $CostCenter): MainRecord
    {
        $this->CostCenter = $CostCenter;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getSecondaryEmailAddress(): ?string
    {
        return $this->SecondaryEmailAddress;
    }

    /**
     * @param string|null $SecondaryEmailAddress
     * @return MainRecord
     */
    public function setSecondaryEmailAddress(?string $SecondaryEmailAddress): MainRecord
    {
        $this->SecondaryEmailAddress = $SecondaryEmailAddress;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getSecondRIGPhoneNumber(): ?string
    {
        return $this->SecondRIGPhoneNumber;
    }

    /**
     * @param string|null $SecondRIGPhoneNumber
     * @return MainRecord
     */
    public function setSecondRIGPhoneNumber(?string $SecondRIGPhoneNumber): MainRecord
    {
        $this->SecondRIGPhoneNumber = $SecondRIGPhoneNumber;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getSecondInternalPhoneNumber(): ?string
    {
        return $this->SecondInternalPhoneNumber;
    }

    /**
     * @param string|null $SecondInternalPhoneNumber
     * @return MainRecord
     */
    public function setSecondInternalPhoneNumber(?string $SecondInternalPhoneNumber): MainRecord
    {
        $this->SecondInternalPhoneNumber = $SecondInternalPhoneNumber;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getSecondMobileNumber(): ?string
    {
        return $this->SecondMobileNumber;
    }

    /**
     * @param string|null $SecondMobileNumber
     * @return MainRecord
     */
    public function setSecondMobileNumber(?string $SecondMobileNumber): MainRecord
    {
        $this->SecondMobileNumber = $SecondMobileNumber;
        return $this;
    }

    /**
     * @return AssignmentCompany|null
     */
    public function getAssignmentCompany(): ?AssignmentCompany
    {
        return $this->AssignmentCompany;
    }

    /**
     * @param AssignmentCompany|null $AssignmentCompany
     * @return MainRecord
     */
    public function setAssignmentCompany(?AssignmentCompany $AssignmentCompany): MainRecord
    {
        $this->AssignmentCompany = $AssignmentCompany;
        return $this;
    }


    public function getEmail(): ?string
    {
        if (!Str::isNullOrEmpty($this->SecondaryEmailAddress)) {
            return $this->SecondaryEmailAddress;
        }
        return $this->EmailAddress;
    }

    /**
     * @return string|null
     */
    public function getPhoneNumber(): ?string
    {
        return $this->PhoneNumber;
    }

    /**
     * @param string|null $PhoneNumber
     * @return MainRecord
     */
    public function setPhoneNumber(?string $PhoneNumber): MainRecord
    {
        $this->PhoneNumber = $PhoneNumber;
        return $this;
    }

    /**
     * @return EmployeeType|null
     */
    public function getEmployeeType(): ?EmployeeType
    {
        return $this->EmployeeType;
    }

    /**
     * @param EmployeeType|null $EmployeeType
     * @return MainRecord
     */
    public function setEmployeeType(?EmployeeType $EmployeeType): MainRecord
    {
        $this->EmployeeType = $EmployeeType;
        return $this;
    }

    public function getPhone(): ?string
    {
        if (!Str::isNullOrEmpty($this->PhoneNumber)) {
            return $this->PhoneNumber;
        }
        return $this->getToipPhoneNumber();
    }

    public function getSiteName(): ?string
    {
        #WorkingLocation default value
        $default = "SITE EXTERNE(FRA)";
        $workingLocation = $this->getWorkingLocation();
        if ($workingLocation == null) {
            return $default;
        }

        if (!Str::isNullOrEmpty($workingLocation->getNameFr())) {
            return $workingLocation->getNameFr();
        }
        return $default;
    }

    public function getSiteStreet(): ?string
    {
        $default = "SITE EXTERNE(FRA)";
        $workingLocation = $this->getWorkingLocation();
        if ($workingLocation === null) {
            return $default;
        }
        if (!Str::isNullOrEmpty($workingLocation->getNameFr())) {
            return 'TOTAL ' . $workingLocation->getNameFr();
        }
        return $default;

    }

    public function getSiteStreet2(): ?string
    {

        $result = "SITE EXTERNE(FRA)";
        $workingLocation = $this->getWorkingLocation();
        if ($workingLocation == null) {
            return $result;
        }
        if (!Str::isNullOrEmpty($workingLocation->getNameFr()) ||
            (
                !Str::isNullOrEmpty($workingLocation->getStreet()) &&
                !Str::isNullOrEmpty($workingLocation->getStreet2()) &&
                !Str::isNullOrEmpty($workingLocation->getStreet3()) &&
                !Str::isNullOrEmpty($workingLocation->getCedex())
            )
        ) {
            $result = $workingLocation->getStreet() . ' ' . $workingLocation->getStreet2() . ' ' . $workingLocation->getStreet3() . ' ' . $workingLocation->getCedex();
        }
        return $result;

    }

    public function getSiteZipCode(): ?string
    {
        $postalCode = "75000";
        $workingLocation = $this->getWorkingLocation();
        if ($workingLocation == null) {
            return $postalCode;
        }
        if (!Str::isNullOrEmpty($workingLocation->getPostalCode())) {
            $postalCode = $workingLocation->getPostalCode();
        }
        return $postalCode;
    }

    public function getSiteLocality(): ?string
    {
        $locality = "Paris";
        $workingLocation = $this->getWorkingLocation();
        if ($workingLocation == null) {
            return $locality;
        }
        if (!Str::isNullOrEmpty($workingLocation->getLocality())) {
            $locality = $workingLocation->getLocality();
        }
        return $locality;

    }

    public function getSiteCountry(): ?string
    {
        $default = "France";
        $workingLocation = $this->getWorkingLocation();
        if ($workingLocation === null) {
            return $default;
        }
        $parentLocation = $workingLocation->getParentLocation();
        if ($parentLocation === null) {
            return $default;
        }
        $parentParentLocation = $parentLocation->getParentLocation();

        if (Str::isNullOrEmpty($workingLocation->getNameFr())) {
            $country = $default;
        } elseif ($parentParentLocation != null && !Str::isNullOrEmpty($parentParentLocation->getNameFr())) {
            $country = $parentParentLocation->getNameFr();
        } else {
            $country = $parentLocation->getNameFr();
        }

        return $country;
    }

    public function getCountryCode(): ?string
    {
        $default = "FRA";
        $workingLocation = $this->getWorkingLocation();
        if ($workingLocation === null) {
            return $default;
        }

        $parentLocation = $workingLocation->getParentLocation();
        if ($parentLocation === null) {
            return $default;
        }
        $parentParentLocation = $parentLocation->getParentLocation();

        if (Str::isNullOrEmpty($workingLocation->getNameFr()) || Str::isNullOrEmpty($parentLocation->getUid())) {
            $countryCode = $default;
        } elseif ($parentParentLocation != null && !Str::isNullOrEmpty($parentParentLocation->getUid())) {
            $countryCode = $parentParentLocation->getUid();
        } else {
            $countryCode = $parentLocation->getUid();
        }

        return $countryCode;

    }

    public function getInvoiceEntityName(): ?string
    {
        $default = "";
        $shortNameEn = "";
        $assignmentCompany = $this->getAssignmentCompany();
        $organization = $this->getOrganization();

        if ($organization != null) {
            $organizationType = $organization->getOrganizationType();
            if (!Str::isNullOrEmpty($organizationType->getShortNameEn())) {
                $shortNameEn = $organizationType->getShortNameEn();
            }
        }

        $company = "";
        if ($assignmentCompany === null) {
            return $company;
        }
        if (Str::isNullOrEmpty($assignmentCompany->getName())) {
            return $company;
        } elseif ($assignmentCompany->getUid() === "H505" || $assignmentCompany->getUid() === "A010") {
            $company = $assignmentCompany->getName() . " - Branche " . $shortNameEn;
        } else {
            $company = $assignmentCompany->getName();
        }
        return $company;

    }

    public function getInvoicingEntityCountry(): ?string
    {
        $assigment = $this->getAssignmentCompany();
        if ($assigment != null) {
            $location = $assigment->getLocation();
            if ($location != null) {
                return $location->getNameFr();
            }
        }
        return null;
    }

    public function getInvoicingEntityCountryCode(): ?string
    {
        $assigment = $this->getAssignmentCompany();
        if ($assigment != null) {
            $location = $assigment->getLocation();
            if ($location != null) {
                return $location->getUid();
            }
        }
        return null;
    }

    public function getContrat(): ?string
    {
        $employeType = $this->getEmployeeType();
        if ($employeType != null) {
            return $employeType->getNameFr();
        }
        return null;
    }

}