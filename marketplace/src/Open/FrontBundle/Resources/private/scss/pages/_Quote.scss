@import "../variables";

body[class*="Page--quotes"], body[class*="Page--nl_quotes"] {


    .quotes-table .sorting_desc {
        &::after, &::before {
            display: none !important;
        }
    }

    .hide {
        display: none;
    }

    .unread {
        font-weight: bold;
    }

    .count-badge {
        width: 20px;
        height: 20px;
        font-size: 0.7rem;
        border-radius: 50%;
        background-color: $TOTAL_BLUE;
        box-shadow: 0 0 8px #6B6F82;
        display: -ms-flexbox;
        display: flex;
        -ms-flex-pack: center;
        justify-content: center;
        -ms-flex-align: center;
        align-items: center;
        color: #FFF;
        font-weight: 400;
        margin: -20px 0 0 2px;
    }

    table {
        border: none !important;
        border-bottom: 1px solid #d7d7d7 !important;
    }

    table th:first-child {
        border-left: 1px solid #d7d7d7 !important;
    }

    table th:last-child {
        border: none !important;
        display: none !important;
    }

    .quotes-table {
        width: 100% !important;
    }

    .quotes-table th {
        border: 1px solid #d7d7d7 !important;
        border-left: none !important;
        padding: 0 10px !important;
        height: 35px;
        line-height: 35px;
        font-size: 13px;
        font-weight: bold;
        color: #343639;
    }

    .quotes-table tr:nth-child(2n) {
        background-color: rgba(239, 239, 239, 0.5);
    }

    .quotes-table td {
        border-right: 1px solid #d7d7d7 !important;
    }

    .quotes-table td:nth-child(5n) {
        border-right: none !important;
    }

    .quotes-table td:nth-child(6n) {
        border: none !important;
        display: none !important;
    }

    .quotes-table-validated td:nth-child(4n) {
        border-right: none !important;
    }

    .quotes-table-validated td:nth-child(5n) {
        border: none !important;
        display: none !important;
    }
}
