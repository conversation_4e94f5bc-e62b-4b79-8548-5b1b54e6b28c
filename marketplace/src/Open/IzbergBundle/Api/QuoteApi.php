<?php
namespace Open\IzbergBundle\Api;

use Doctrine\Common\Collections\ArrayCollection;
use Open\IzbergBundle\Api;
use Open\IzbergBundle\Model\Address;
use Open\IzbergBundle\Model\Quote;
use Unirest;

/**
 * Wrapper around Quote API
 * Class QuoteApi
 * @package Open\IzbergBundle\Api
 */
class QuoteApi extends Api
{
    const ADDRESS_SLUG = 'quote/';

    public function  getUri(){
        return 'quote';
    }

    public function getItemClass(){
        return Quote::class;
    }

    /**
     * @param Quote $quote
     * @param bool $operator whether this is a operator (true) call or a user call (false)
     * @return mixed
     */
    public function createQuote(Quote $quote)
    {
        $data = array(

            'application' => $this->getApiUrl() . "application/" . $this->getApplicationId() . "/",
            'merchant' => sprintf('/v1/merchant/%s/', $quote->getMerchant()),
            'customer' => sprintf('/v1/user/%s/', $quote->getCustomer()),
            'title' => $quote->getTitle(),
            '' => $quote->getDateOfIssue()
        );



        $response = $this->sendApiRequest(
            self::HTTP_POST_OPERATION,
            self::ADDRESS_SLUG,
            $data
        );

        // return address id if success
        return $response->body->id;
    }

}