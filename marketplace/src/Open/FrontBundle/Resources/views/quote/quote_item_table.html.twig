{% trans_default_domain 'AppBundle' %}

{% set maxPrice = 2000 %}
{% set vendor_currency = quote.vendor.currency %}
{% set buyer_currency = quote.buyer.countryOfdelivery.currency %}
{% set currency_rate = quote.currencyRateCountryOfdelivery %}
<section class="quote-items-list">
    <table class="table quote-table mt-40">
        <thead>
            <tr>
                <th>{{ 'quote.columns.description' | trans }}</th>
                <th class="text-center">{{ 'quote.columns.quantity' | trans }}</th>
                <th class="text-center">{{ 'quote.columns.unit_price' | trans }}</th>
                <th class="text-center">{{ 'quote.columns.total_price' | trans }}</th>
                <th style="display: none"></th>
            </tr>
        </thead>
        <tbody>
        {% for item in quoteItems %}
            <tr>
                <td>{{ item.description }}</td>
                <td class="text-center">{{ item.quantity }}</td>
                <td class="text-center">{{ currency( item.unitPrice, currency_rate , buyer_currency,  vendor_currency ) | price(locale) }} {{ buyer_currency|upper }}</td>
                <td class="text-center">{{ currency( item.totalPrice, currency_rate , buyer_currency,  vendor_currency ) | price(locale) }} {{ buyer_currency|upper }}</td>
                <td style="display: none"></td>
            </tr>
        {% endfor %}
        </tbody>
    </table>

    <section>
        {# ------ TOTAL WITHOUT VAT ------ #}
        <section class="table-resume-divider">
            <section class="table-resume-label">{{ 'orders.detail.total_without_vat' | trans }}</section>
            <section class="table-resume-value">{{ currency( quote.totalPrice , currency_rate , buyer_currency,  vendor_currency ) | price(locale) }} {{ buyer_currency|upper }}</section>
        </section>
    </section>

    <section class="quote-comment-content">
        <section>{{ quote.message|nl2br }}</section>

        <section class="btn-container">
            {% if quote.checkStatusTransition (constant('STATUS_VALIDATED', quote)) and quote.totalPrice <= maxPrice %}
                <button class="add-to-cart-btn"
                        onclick="event.stopPropagation(); window.UI.Modal.onValidate('{{ path('front.quote.action.add_to_cart') }}', {{ quote.quoteId }}, '{{ 'quote.modal.confirm.add_to_cart'|trans({}, 'AppBundle')|raw }}', '{{ 'quote.modal.confirm.error'|trans({}, 'AppBundle')|raw }}', '{{ path('cart.details') }}', '{{ csrf_token('quote_add_to_cart') }}')"
                >
                    {{ 'quote.detail.button.add_to_cart' | trans | upper }}
                </button>
            {% endif %}
            {% if quote.checkStatusTransition (constant('STATUS_REDRAFT', quote)) %}
                <button class="discuss-quote-btn"
                        onclick="event.stopPropagation(); window.UI.Modal.onReject('{{ path('front.quote.action.negociate') }}', {{ quote.quoteId }}, '{{ 'quote.modal.confirm.negociate'|trans({}, 'AppBundle')|raw }}', '{{ 'quote.modal.confirm.error'|trans({}, 'AppBundle')|raw }}', '{{ path('front.quotes.list') }}', '{{ csrf_token('quote_negociate') }}')"
                >
                    {{ 'quote.detail.button.negociate' | trans | upper }}
                </button>
            {% endif %}
            {% if quote.checkStatusTransition (constant('STATUS_CANCELLED', quote)) %}
                <button class="cancel-order-btn"
                        onclick="event.stopPropagation(); window.UI.Modal.onReject(
                                '{{ path('front.quote.action.cancel') }}',
                                '{{ quote.quoteId }}',
                                '{{ 'quote.modal.confirm.cancel'|trans({}, 'AppBundle')|raw }}',
                                '{{ 'quote.modal.confirm.error'|trans({}, 'AppBundle')|raw }}',
                                '{{ path('front.quotes.list') }}',
                                '{{ csrf_token('quote_cancel') }}'
                        )"
                >
                    {{ 'quote.detail.button.cancel' | trans | upper }}
                </button>
            {% endif %}
        </section>
        {% if quote.totalPrice > maxPrice %}
        <section>
            <p class="price-max-error">
                {{ 'quote.add.max_price_error' | trans }}
            </p>
        </section>
        {% endif %}
    </section>

</section>

{% block javascripts %}



{% endblock %}
