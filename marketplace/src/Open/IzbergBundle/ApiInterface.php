<?php

namespace Open\IzbergBundle;

use Open\IzbergBundle\Api\ApiException;
use Open\IzbergBundle\Dto\Request;
use Symfony\Contracts\HttpClient\HttpClientInterface;

interface ApiInterface
{
    public function setHttpClient(HttpClientInterface $httpClient);

    public function setApplicationId(string $applicationId);

    /**
     * @throws ApiException
     */
    public function post(Request $request): string;

    /**
     * @throws ApiException
     */
    public function get(Request $request): string;

    /**
     * @throws ApiException
     */
    public function patch(Request $request): string;
}
