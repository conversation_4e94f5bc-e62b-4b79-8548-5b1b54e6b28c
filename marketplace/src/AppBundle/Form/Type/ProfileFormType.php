<?php
/** Add new fields to the FOS User registration form */

namespace AppBundle\Form\Type;

use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\Form\Extension\Core\Type\EmailType;
use Symfony\Component\Security\Core\Authorization\AuthorizationCheckerInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;


class ProfileFormType extends AbstractType
{
    const LABEL = "label";
    const DOMAIN = "translation_domain";
    const DOMAIN_VALUE = "OpenFrontBundle";
    const LABEL_PHONE = "user.registration.phone";
    const REQUIRED = "required";
    const PLACEHOLDER= "placeholder";

    /** @var  AuthorizationCheckerInterface */
    private $authorizationChecker;

    /**
     * ProfileFormType constructor.
     * @param $authorization_checker
     */
    public function __construct(AuthorizationCheckerInterface $authorization_checker)
    {
        $this->authorizationChecker = $authorization_checker;
    }

    public function buildForm(FormBuilderInterface $builder, array $options)
    {
        // Remove fields that are not editable on Izberg API
        $builder->remove('username');
        $builder->remove('email');

        // Add first name field
        $builder->add(
            'first_name',
            null,
            array(
                self::LABEL => 'profile.firstname'
            )
        );

        // Add last name field
        $builder->add(
            'last_name',
            null,
            array(
                self::LABEL => 'profile.lastname'
            )
        );

		$builder->add(
			'civ',
			CivType::class,
			array(
				self::LABEL => 'profile.civ.label'
			)
		);

        $builder->add(
            'email',
            EmailType::class,
            array(
                self::LABEL => 'profile.email',
                'attr' => array(
                    self::PLACEHOLDER => 'profile.email_placeholder'
                )
            )
        );
    }

    public function configureOptions(OptionsResolver $resolver)
    {

        $resolver->setDefaults(array(
            'validation_groups' => array('Default', 'Registration','Profile')
        ));
    }

    public function getParent()
    {
        return 'FOS\UserBundle\Form\Type\ProfileFormType';
    }

    /**
     * {@inheritdoc}
     */
    public function getBlockPrefix()
    {
        return 'front_user_profile';
    }

    // For Symfony 2.x
    /**
     * {@inheritdoc}
     */
    public function getName()
    {
        return $this->getBlockPrefix();
    }

}