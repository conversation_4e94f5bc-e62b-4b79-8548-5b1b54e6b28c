import React, {Component} from "react";
import {Status, Themer} from "@izberg/izberg-ui-beta";
import {Trans} from "react-i18next";
import * as CONSTANT from "../../config/const";

export default class StatusState extends Component {
    render() {
        return (
            <Themer>
                {this.props.children === CONSTANT.PENDING_STATUS && (
                    <Status type="info">
                        <Trans>Pending</Trans>
                    </Status>
                )}
                {this.props.children === CONSTANT.ACCEPTED_STATUS && (
                    <Status type="success">
                        <Trans>Accepted</Trans>
                    </Status>
                )}
                {this.props.children === CONSTANT.REJECTED_STATUS && (
                    <Status type="error">
                        <Trans>Rejected</Trans>
                    </Status>
                )}
            </Themer>
        );
    }
}
