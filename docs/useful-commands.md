### [< Back...](README.md)

### Composer install

```
make composer.install
```
###  Install react app dependencies

```
make react.install
```

###  Building react app

```
make react.build
```

### Database schema update

```
make schema.update
```

### Import users

```
docker exec -it click-and-buy bash  
php bin/console open:buyer:import app/import/total_buyers.csv
```

### Import Email Templates

```
docker exec -it click-and-buy bash  
php bin/console open:import:emails
```

### Create a super admin user

```
docker exec -it click-and-buy bash  
php bin/console open:user:create <EMAIL> --super-admin
```

### Import Country

```
docker exec -it click-and-buy bash
php bin/console open:import:countries countries.csv
```

### Import Regions

```
docker exec -it click-and-buy bash
php bin/console open:import:regions regions.csv
```

#### Import Tva

```
docker exec -it click-and-buy bash
php bin/console open:import:tva tva.csv
```


### Import France Zipcode
```
docker exec -it click-and-buy bash
php bin/console open:import:zipcode zipcodes_france.csv france
```
