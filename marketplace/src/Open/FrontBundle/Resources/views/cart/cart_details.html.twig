{% extends '@OpenFront/base.html.twig' %}
{% trans_default_domain 'AppBundle' %}
{% set isUserAuthorizeToEditCart = true %}
{% set isCartCreator = true %}
{% set isUserValidAssign = true %}
{% set isUserAuthorizeReject = false %}

{% block title %} {{ 'cart.detail' | trans }}{% endblock %}

{% block stylesheets %}

{% endblock %}

{% block body %}

    {% block searchbar %}
        {% if formSearchBar is defined %}
            {% include('@OpenFront/component/search-bar.html.twig') %}
        {% else %}
            {{ render(controller("Open\\FrontBundle\\Controller\\SearchBarController::formAction")) }}
        {% endif %}
    {% endblock %}

    {% if cart is not empty and cart.itemsCount > 0 %}
        <div id="shipping-address">
            <div class="shipping-form-container selected-address">
                {% include('@OpenFront/cart/cart_shipping_address.html.twig') %}
            </div>
        </div>

        <div class="Page-cart-inner">
        {% set locale = app.session.get("_locale") %}

        <div class="Page-content">
            {% include('@OpenFront/cart/cart_tables.html.twig') with {'cart': cart, 'risk' : risk, 'multiMerchants' : multiMerchants} %}
        </div>

        {# Tableau historique du panier (Modal?) #}

    {% else %}
        <div class="empty-cart">
            <div>
                <h1>{{ 'cart.empty.title' | trans }}</h1>

                <p>{{ 'cart.empty.text' | trans | raw }}</p>
                <a class="color-primary" href="{{ path('front.search') }}">{{ 'cart.empty.back' | trans }}</a>
            </div>

            <img alt="empty cart" class="cart-empty-img desktop-only"
                 src="{{ asset('images/empty-cart-temporary.png') }}">
        </div>

        </div>
    {% endif %}
{% endblock %}

{% block javascripts %}
    {% if cart is not null %}
        {#
        ##########################################
            Cart stock and quantity Management
        ##########################################
        #}
        <script type="text/javascript">
            let loadingModal = null;
            let assignModal = null;
            let messageModal = null;
            let currency = '{{ cart.currency }}';
            let labelTaxes = '{{ 'cart.table_label.taxes'|trans }}';

            var closeLoading = function () {
                if (loadingModal !== null) {
                    loadingModal.close();
                }
            };

            var quantityChange = function (spinnerId, moq, stock, batchSize) {

                let quantityInput = $('#quantity_' + spinnerId);
                let qty = quantityInput.val();
                qty = parseInt(qty);
                moq = parseInt(moq);
                stock = parseInt(stock);
                batchSize = parseInt(batchSize);

                if (qty <= 0) {
                    window.UI.Modal.alert("{{ 'offer_detail.wrong_quantity'|trans|raw }}", function () {
                        quantityInput.val(initialQuantities[spinnerId]);
                    });
                } else if (qty < moq) {
                    let wrongQuantity = "{{ 'offer_detail.too_small_quantity'|trans|raw }}";
                    wrongQuantity = wrongQuantity.replace(new RegExp('%min%', 'g'), moq);
                    window.UI.Modal.alert(wrongQuantity);
                } else if (qty > stock) {
                    let wrongQuantity = "{{ 'offer_detail.too_much_quantity'|trans|raw }}";
                    wrongQuantity = wrongQuantity.replace(new RegExp('%max%', 'g'), stock);
                    window.UI.Modal.alert(wrongQuantity, function () {
                        quantityInput.val(initialQuantities[spinnerId]);
                    });
                } else if (qty % batchSize > 0) {
                    let wrongBatchSize = "{{ 'offer_detail.not_batch_size_multiple'|trans|raw }}";
                    wrongBatchSize = wrongBatchSize.replace(new RegExp('%batchSize%', 'g'), batchSize);
                    window.UI.Modal.alert(wrongBatchSize, function () {
                        quantityInput.val(initialQuantities[spinnerId]);
                    });
                } else {
                    loadingModal = window.UI.Modal.showLoading();
                    let form = $('#quantity_update_' + spinnerId);

                    $.ajax({
                        type: 'POST',
                        url: '{{ path('front.cart.addFromCart.withCartId') }}',
                        data: form ? form.serialize() : null,
                        success: function (data) {
                            window.location.reload();
                        },
                        error: function (XMLHttpRequest, textStatus, errorThrown) {
                            closeLoading();
                        }
                    });

                }
            };

            var shippingModeChange = function (merchantId) {
                let form = $('#shipping-mode_' + merchantId);
                loadingModal = window.UI.Modal.showLoading();

                $.ajax({
                    type: 'POST',
                    url: '{{ path('front.cart.shipping') }}',
                    data: form ? form.serialize() : null,
                    success: function (data) {
                        cartRefresh();
                    },
                    error: function () {
                        closeLoading();
                    }
                });
            };

            var batchSize = parseInt($('.qtyInput').data('batchsize'));
            var moq = parseInt($('.qtyInput').data('moq'));


            var stockManagement = function ($qtyComponent) {
                var $inputQuantity = $('input[name="form[quantity]"]', $qtyComponent);
                var $plusButton = $('.qtyModif.plus', $qtyComponent);
                var $addToCartButton = $('button[name="form[addOfferToCart]"]', $qtyComponent);
                var offerStock = parseInt($inputQuantity.attr('max'));
                var quantity = parseInt($inputQuantity.val());

                // disable plus quantity button and add to cart button when stock = 0
                if (offerStock === 0) {
                    $inputQuantity.attr('disabled', true);
                    $plusButton.attr('disabled', true);
                    $addToCartButton.attr('disabled', true);
                }

                // disable plus quantity button when quantity = offerStock
                if (offerStock === quantity) {
                    $plusButton.attr('disabled', true);
                }

                // enable plus quantity button when quantity < offerStock
                if (offerStock > quantity) {
                    $plusButton.attr('disabled', false);
                }
            };

            $('.qtyComponent').each(function () {
                var $qtyComponent = $(this);
                stockManagement($qtyComponent);
            });

            // todo review this code with stock management
            $(document).on('click', '.qtyModif.plus', function () {
                var $qtyComponent = $(this).parent().find('.qtyComponent');
                var currentQty = parseInt($(this).parent().find('.qtyInput').val());
                var diff = currentQty % batchSize;
                var newQty = currentQty + (batchSize - diff);
                if (newQty < moq) {
                    newQty = moq;
                }
                $(this).parent().find('.qtyInput').val(newQty);
                checkMin();
                $(this).parent().find('.qtyInput').change();
                stockManagement($qtyComponent);
            });

            $(document).on('click', '.qtyModif.min', function () {
                var $qtyComponent = $(this).parent().find('.qtyComponent');
                var currentQty = parseInt($(this).parent().find('.qtyInput').val());
                var diff = currentQty % batchSize;
                var newQty = currentQty - batchSize;
                if (diff) {
                    newQty = currentQty - (batchSize - (batchSize - diff));
                }

                if (newQty < moq) {
                    newQty = moq;
                }
                $(this).parent().find('.qtyInput').val(newQty);
                checkMin();
                $(this).parent().find('.qtyInput').change();
                stockManagement($qtyComponent);
            });

            var checkMin = function () {
                $('.qtyModif.min').each(function () {
                    var qtyInput = $(this).parent().find('.qtyInput');
                    var moq = parseInt($(qtyInput).data('moq'));
                    var qty = parseInt($(qtyInput).val());
                    if (qty <= moq) {
                        $(qtyInput).val(moq);
                        $(this).attr('disabled', true);
                    } else {
                        $(this).attr('disabled', false);
                    }
                })
            };

            var addProduct = function (productId, qty) {
                var url = '{{ path("front.cart.addFromCart.withCartId", {"productId" : "productId", "qty" : "quantityNumber", "cartId" : cart.id }) }}';
                url = url.replace("productId", productId);
                url = url.replace("quantityNumber", qty);
                $.ajax({
                    type: 'POST',
                    url: url,
                    success: function (data) {
                        cartRefresh();
                    },
                    error: function (XMLHttpRequest, textStatus, errorThrown) {
                        closeLoading();
                    }
                });
            };

            var cartRefresh = function () {
                loadingModal = window.UI.Modal.showLoading();
                $.ajax({
                    type: 'GET',
                    url: '{{ path("cart.details") }}',
                    success: function (htmlCart) {
                        $('.Page-content').html(htmlCart);

                        var $dataItems = $('.Page-content').find('[data-itemincart]').eq(0);
                        if ($dataItems.length > 0) {
                            var itemInCart = $dataItems.data('itemincart');

                            $(document).trigger({
                                type: "UpdateItemInCart",
                                itemInCart: itemInCart
                            });
                        }
                        checkMin();

                        $('.qtyComponent').each(function () {
                            var $qtyComponent = $(this);
                            stockManagement($qtyComponent);
                        });
                        closeLoading();
                    },
                    error: function () {
                        closeLoading();
                    }
                });
            };

            var removeItem = function (itemId) {
                loadingModal = window.UI.Modal.showLoading();
                var url = '{{ path("cart.details.remove_item") }}';
                let form = $('#remove_item_' + itemId);

                $.ajax({
                    type: 'DELETE',
                    url: url,
                    data: form ? form.serialize() : null,
                    success: function () {
                        cartRefresh();
                    },
                    error: function () {
                        closeLoading();
                    }
                });
            };

            var removeCartQuotationItem = function (itemId, message) {
                const onConfirm = function () {
                    var url = '{{ path("cart.details.remove_item") }}';
                    let form = $('#remove_item_' + itemId);

                    $.ajax({
                        type: 'DELETE',
                        url: url,
                        data: form ? form.serialize() : null,
                        success: function () {
                            cartRefresh();
                        },
                        error: function () {
                            closeLoading();
                        }
                    });
                };

                const onCancel = function () {

                }
                window.UI.Modal.confirm('', message, onConfirm, onCancel);
            };

            var rejectCart = function (id) {
                rejectModal.close();
                loadingModal = window.UI.Modal.showLoading();
                let message = $(id).val();
                $.ajax({
                    type: 'POST',
                    url: '{{ path('cart.details.reject') }}',
                    data: {
                        cartId: {{ cart.id }},
                        comment: message
                    },
                    success: function (data) {
                        closeLoading();
                        var href = '{{ path('cart.details.before_buy', {'currencyOrCartId' : 'data'}) }}';
                        href = href.replace('data', data);
                        window.UI.Modal.alert("{{ 'cart.reject.success'|trans|raw }}", function () {
                            window.location.href = href;
                        });
                    },
                    error: function (XMLHttpRequest, textStatus, errorThrown) {
                        closeLoading();
                        window.UI.Modal.alert("{{ 'cart.reject.error'|trans|raw }}");
                    }
                });
            };

            var toggleSeller = function (idTable) {
                let tableContent = $(idTable).find('tbody');
                tableContent.toggleClass('hide');
                let arrow = $(idTable).find('.arrow');
                if (arrow.hasClass('down')) {
                    arrow.removeClass('down');
                    arrow.addClass('up');
                } else {
                    arrow.removeClass('up');
                    arrow.addClass('down');
                }
            };

            var onClickReject = function () {
                let modalHtml = $('#js-reject-modal-tpl').html();
                rejectModal = window.UI.Modal.show('js-reject-modal', 'Modal--reject', $(modalHtml), true);
            };

            var showAssign = function () {
                let modalHTML = $('#js-assign-modal-tpl').html();
                return window.UI.Modal.show('js-assign-modal', 'Modal--assign', $(modalHTML), true);
            };

            var noUserToAssign = function () {
                let modalHTML = $('#js-no-user-to-assign-modal-tpl').html();
                return window.UI.Modal.show('js-assign-modal', 'Modal--assign', $(modalHTML), true);
            };

            var buildAssignUsers = function (users) {
                let options = [];
                for (let i = 0; i < users.length; i++) {
                    let option = $("<option></option>");
                    option.attr('value', users[i].id);
                    option.text(users[i].lastname + " " + users[i].firstname);
                    if (i === 0) {
                        option.attr('selected', 'true');
                    }
                    options.push(option);
                }
                return options;
            };


            var refreshTable = function (cart) {

                let itemCount = $("#items-count");
                itemCount.find('.count').html(cart.itemsCount);

                let cartIcon = $("#cart-quantity-" + currency.toLowerCase());
                cartIcon.html(cart.itemsCount);

                for (let i = 0; i < cart.merchants.length; i++) {
                    for (let j = 0; j < cart.merchants[i].items.length; j++) {
                        refreshItem(cart.merchants[i].items[j]);
                    }
                    refreshTableMerchant(i + 1, cart.merchants[i]);
                }
                refreshTableMerchant('total', cart);
            };

            var refreshItem = function (item) {

                let line = $("#item-" + item.id);
                let quantity = line.find("#quantity_" + item.id);
                let totalPrice = line.find(".item-total-price");

                quantity.attr('value', item.quantity);
                totalPrice.html(item.totalItem + " " + currency);
            };

            var refreshTableMerchant = function (id, cart) {
                let table = $("#table-merchant-" + id);

                table.find(".total-without-vat").html(cart.subTotalWithoutVat.toFixed(2) + " " + currency);

                let lineTaxes = table.find(".taxes");
                //let labelTaxes = lineTaxes.find(".label-taxes").html();

                for (let i = 0; i < lineTaxes.length; i++) {
                    $(lineTaxes[i].remove());
                }

                let i = 1;
                for (let vat in cart.subTotalVat) {
                    let line = $("<tr></tr>");
                    line.addClass('total-table-taxes resume-table taxes');
                    line.append("<td></td>");
                    line.append("<td></td>");
                    line.append("<td></td>");

                    let label = $("<p class='label'>" + labelTaxes + "&nbsp;</p>");
                    let vatPercent = $("<p class='vat-percent'></p>");
                    vatPercent.html(Number.parseFloat(vat).toFixed(2) + "%");
                    let vatColumn = $("<td class='taxes-label-cell'></td>");
                    vatColumn.append(label);
                    vatColumn.append(vatPercent);
                    line.append(vatColumn);

                    line.append("<td></td>");
                    line.append("<td class='vat-value'>" + cart.subTotalVat[vat].toFixed(2) + " " + currency + "</td>");

                    line.insertBefore("#subtotal-vat-" + id);
                    i++;
                }

                table.find(".total-cart").html(cart.total.toFixed(2) + " " + currency);
            };

            $(document).ready(checkMin, cartRefresh);
        </script>

        {#
        ###################################
            Wish List form Management
        ###################################
        #}
        <script type="text/javascript">
            const wishListForm = {};

            wishListForm.showWishlistForm = function () {
                let modalContent = $($('#js-save-in-wishlist-modal-tpl').html());
                this.modal = UI.Modal.show('js-save-in-wishlist-modal', 'Modal--save', modalContent, true, true);
                this.form = $('form', '#js-save-in-wishlist-modal');
                this.select = $('select', this.form);
                this.submitButton = $(':submit', this.form);
                this.selectedWishlist = 0;
                this.newWishListForm = $('#newWishListForm', this.form);
                UI.Select.init();
                this.selectWishListListener();
                this.submitListener();
            },

                wishListForm.showNewWishListForm = function () {
                    $('input[name="form[name]"]', this.newWishListForm).attr('required', true);
                    this.newWishListForm.show();
                },

                wishListForm.hideNewWishListForm = function () {
                    $('input[name="form[name]"]', this.newWishListForm).attr('required', false);
                    this.newWishListForm.hide();
                },

                wishListForm.selectWishListListener = function () {
                    let that = this;
                    this.select.on('change', function () {
                        let $this = $(this);
                        if (that.select.val()) {
                            that.selectedWishlist = $this.val();
                            that.hideNewWishListForm()
                        } else {
                            that.selectedWishlist = 0;
                            that.showNewWishListForm();
                        }
                    }).change();
                },

                wishListForm.productSuccessfullyAddedToWishlist = function () {
                    window.location.reload();
                },

                wishListForm.submitListener = function () {
                    let that = this;
                    this.form.on('submit', function (event) {
                        event.preventDefault();
                        let modalLoading = window.UI.Modal.showLoading();
                        let url = that.form.attr('action');
                        let data = new FormData(that.form.get(0));

                        $.ajax({
                            url: url,
                            data: data,
                            type: 'POST',
                            cache: false,
                            processData: false,
                            contentType: false,
                            success: function (wishList) {
                                that.modal.close();
                                that.productSuccessfullyAddedToWishlist();
                            },
                            error: function (data) {
                                modalLoading.close();
                            }
                        });
                    });
                }
        </script>

        {#
        ###################################
            Shipping Address Management
        ###################################
        #}
        <script type="text/javascript">
            let shippingAddressUpdateModal = null;

            let closeShippingAddressUpdateModal = function () {
                if (shippingAddressUpdateModal) {
                    shippingAddressUpdateModal.close();
                }
            }

            let showButtonModifyAdress = function(){
                $('#shipping-address-check-container').removeClass('d-none');
                $('#entity-address').removeClass('d-none');
                $('#shipping-address-select-container').addClass('d-none');
            }

            let mapAddress = function (item, bindUpdate, callback = null) {
                let $cartShippingDisplay = $('#shipping-address > div').eq(1);
                if (bindUpdate) {
                    $cartShippingDisplay.find('input[name="addressId"]').val(
                        parseInt($cartShippingDisplay.find('input[name="addressId"]').val(), 10) + 1
                    );
                }

                // form update
                if($("#input-technical-id").val() !== item.data.technical_id || item.data.technical_id ==="") {
                    $("#input-technical-id").val(item.data.technical_id);
                    $("#input-type").val(item.data.type);
                    $("#input-name").val(item.data.name);
                    $("#input-address").val(item.data.address);
                    $("#input-address2").val(item.data.address2);
                    $("#input-city").val(item.data.city);
                    $("#input-zipcode").val(item.data.zipcode);
                    $("#input-country-code").val(item.data.country_code);
                    // view update
                    $("#span-address").html(item.data.address);
                    $("#span-address2").html(item.data.address2);
                    $("#span-zipCode").html(item.data.zipcode + ' ' + item.data.city);
                    $("#span-country").html(item.data.country);

                    cartSubmit.vars.shippingAddressChecked = false;
                    cartSubmit.buttonControl();
                }
                $('#buyer_address_form_contact').val(item.data.recipient);
                $('#buyer_address_form_phone').val(item.data.recipient_phone);
                $('#buyer_address_form_comment').val(item.data.recipient_comment);
                if (bindUpdate) {
                    modifyRecipient(callback);
                }
            };

            let recipientMandatory = function () {

                let contactElement = $('#shipping-address [name*="[contact]"]').eq(0);
                let phoneElement = $('#shipping-address [name*="[phone]"]').eq(0);
                if (contactElement.val().length <= 0) {
                    let contactVal = contactElement.attr('placeholder') && contactElement.attr('placeholder').length ? contactElement.attr('placeholder') : '';
                    contactElement.val(contactVal);
                }
                if (phoneElement.val().length <= 0) {
                    let phoneVal = phoneElement.attr('placeholder') && phoneElement.attr('placeholder').length ? phoneElement.attr('placeholder') : '';
                    phoneElement.val(phoneVal);
                }
            };

            let enableAddressSubmit = function (e) {
                let contactElement = $('#shipping-address [name*="[contact]"]').eq(0);
                let phoneElement = $('#shipping-address [name*="[phone]"]').eq(0);

                if (contactElement.val().length && phoneElement.val().length) {
                    return true;
                } else {
                    e.preventDefault();

                    $('[href="#anchorCheckout"] button').attr('disabled', 'disabled').css('opacity', 0.25);
                    window.UI.Modal.alert("{{ 'cart.recipient_mandatory'|trans }}");

                    return false;
                }
            };

            let modifyRecipient = function (callback = null) {
                recipientMandatory();

                if (!enableAddressSubmit()) {
                    return;
                }
                let modalLoading = window.UI.Modal.showLoading();
                $.ajax({
                    url: "{{ path('front.cart.shippingAddress.update') }}",
                    data: {
                        'form[_token]': '{{ csrf_token('cart_shipping_address_form') }}',

                        'form[technical_id]': $('#shipping-address [name="technical_id"]').val(),
                        'form[type]': $('#shipping-address [name="type"]').val(),

                        'form[name]': $('#shipping-address [name="name"]').val(),
                        'form[address]': $('#shipping-address [name="address"]').val(),
                        'form[address2]': $('#shipping-address [name="address2"]').val(),
                        'form[zipcode]': $('#shipping-address [name="zipcode"]').val(),
                        'form[city]': $('#shipping-address [name="city"]').val(),
                        'form[country_code]': $('#shipping-address [name="country_code"]').val(),

                        'form[contact]': $('#shipping-address [name*="[contact]"]').eq(0).val(),
                        'form[phone]': $('#shipping-address [name*="[phone]"]').eq(0).val(),
                        'form[comment]': $('#shipping-address [name*="[comment]"]').eq(0).val()
                    },
                    type: 'POST',
                    success: function (data) {
                        mapAddress(data, false);
                        if (typeof callback === 'function') {
                            callback();
                        }
                        window.UI.Modal.showLoading();
                        window.location.reload();

                    },
                    error: function ( jqXHR, textStatus, errorThrown) {
                        modalLoading.close();
                    }
                });
            };

            let openNewShippingAddress = function () {
                $('#js-shipping-modal').find('form').submit(function (e) {
                    e.preventDefault();

                    let modalLoading = window.UI.Modal.showLoading();

                    $.ajax({
                        url: "{{ path('new.shipping.address') }}",
                        data: $(this).serialize(),
                        type: 'POST',
                        success: function (data) {
                            mapAddress(data, true);
                            modalLoading.close();
                            modifyRecipient();
                            let closebutton = $('#js-shipping-modal').find('.js-close-icon').get(0);
                            closebutton.dispatchEvent(new Event('click'));

                        },
                        error: function () {
                            modalLoading.close();
                            let closebutton = $('#js-shipping-modal').find('.js-close-icon').get(0);
                            closebutton.dispatchEvent(new Event('click'));
                        }
                    });
                });
            };

            let closeNewShippingAddress = function () {
                const shippingModal = $('#js-shipping-modal');

                shippingModal.find('input, textarea').val('');
                shippingModal.find('select').val(null);
                closeShippingAddressUpdateModal();
            };

            let newShippingAddress = function () {
                let modalHTML = $('#js-shipping-modal-tpl').html();
                return window.UI.Modal.show('js-shipping-modal', 'Modal--shipping', $(modalHTML), true, openNewShippingAddress, closeNewShippingAddress);
            };

            let fetchLastAdresses = function() {
                const MAX_ADDRESS_TO_SHOW = 6;
                const template = '<div class="shipping-address-element"> <div class="shipping-address-element-address" data-address=\'{SHIPPING_ADDRESS}\'>{ADDRESS}</div> </div>';

                let content = '';
                let hiddenContent = '';
                let modalLoading = window.UI.Modal.showLoading();
                $.ajax({
                    url: "{{ path('shipping.address.last_used') }}",
                    data: {
                        '_token': '{{ csrf_token('cart_shipping_last_used') }}'
                    },
                    type: 'POST',
                    success: function (data) {
                        modalLoading.close();
                        if (data.length > 0) {

                            if (data.length < MAX_ADDRESS_TO_SHOW) {
                                for (const address of data) {
                                    jsonEscaped =JSON.stringify(address).replaceAll("'",'&#39;');
                                    content += template.replaceAll('{SHIPPING_ADDRESS}', jsonEscaped).replace('{ADDRESS}', address.label);
                                }
                            } else {
                                for (let cpt = 0; cpt < data.length; cpt++) {
                                    const address = data[cpt];
                                    jsonEscaped =JSON.stringify(address).replaceAll("'",'&#39;');
                                    if (cpt < MAX_ADDRESS_TO_SHOW) {
                                        content += template.replaceAll('{SHIPPING_ADDRESS}', jsonEscaped).replace('{ADDRESS}', address.label);
                                    } else {
                                        hiddenContent += template.replaceAll('{SHIPPING_ADDRESS}', jsonEscaped).replace('{ADDRESS}', address.label);
                                    }
                                }
                            }
                        }

                        let modalHTML = $('#js-change-shipping-address-modal-tpl').html();
                        shippingAddressUpdateModal = window.UI.Modal.show('js-assign-modal', 'Modal--assign', $(modalHTML), true);
                        $('.shipping-address-list').html(content);

                        if (data.length > 0) {
                            if (data.length > MAX_ADDRESS_TO_SHOW) {
                                $('.shipping-address-list-hidden-to-show').html(hiddenContent);
                                $('.shipping-address-list-hidden').css('display', 'block');
                            }
                        } else {
                            $(".shipping-address-list-container").css('display', 'none');
                        }
                    },
                    error: function () {
                        modalLoading.close();
                    }
                });
            };

            $(document).ready(function () {
                /**
                 * Modify shipping address
                 */

                $("#modify-shipping-address, #select-shipping-address").click(function (e) {
                    e.preventDefault();
                    fetchLastAdresses();
                });

                $(document).on('click', '#shipping-address-list-hidden-link', function (event) {
                    event.preventDefault();
                    if ($(this).data('shown') !== 'false') {
                        $(this).data('shown', 'false');
                        $(this).html($(this).data('hideText'));
                        $('.shipping-address-list-hidden-to-show').css('display', 'flex');
                    } else {
                        $(this).data('shown', 'true');
                        $(this).html($(this).data('showText'));

                        $('.shipping-address-list-hidden-to-show').css('display', 'none');
                    }
                });

                $(document).on('click', '.shipping-address-element-address', function () {
                    showButtonModifyAdress();
                    mapAddress($(this).data("address"), true, closeShippingAddressUpdateModal);
                });

                $(document).on('click', '.update-shipping-address', function () {
                    const address = $(this).data('address');
                    const url = '{{ url('modify.shipping.address', {'technicalId': 'ADDRESS_TECHNICAL_ID'}) }}';

                    window.location.href = url.replace('ADDRESS_TECHNICAL_ID', address.data.technical_id);
                });

                $(document).on('click', '.remove-shipping-address', function () {
                    const address = $(this).data('address');
                    const modalLoading = window.UI.Modal.showLoading();
                    const url = '{{ url('delete.shipping.address', {'technicalId': 'ADDRESS_TECHNICAL_ID'}) }}';

                    $.ajax({
                        url: url.replace('ADDRESS_TECHNICAL_ID', address.data.technical_id),
                        type: 'DELETE',
                        ajaxSend: function () {
                            modalLoading.show();
                        },
                        success: function () {
                            modalLoading.close();
                            closeShippingAddressUpdateModal();
                        },
                        error: function () {
                            modalLoading.close();
                        }
                    });
                });

                /**
                 * Modify shipping address autocomplete
                 */
                $(document).on('keyup', '#autocomplete-shipping_address', function () {
                    const value = $(this).val();
                    const itemTemplate = '<a class="address-autocomplete-item" data-technical_id="{TECHNICAL_ID}" data-type="{TYPE}" data-name="{NAME}" data-address="{ADDRESS}" data-address2="{ADDRESS2}" data-city="{CITY}" data-zipcode="{ZIPCODE}" data-country_code="{COUNTRY_CODE}" data-country="{COUNTRY}"  data-recipient="{RECIPIENT}" data-recipient_phone="{RECIPIENT_PHONE}" data-recipient_comment="{RECIPIENT_COMMENT}"href="#">{VALUE}</a>';
                    const $addressAutocompleteList = $('#address-autocomplete-list');
                    let content = "";

                    if (value.length >= 3) {
                        $.ajax({
                            url: "{{ path('autocomplete.shipping.address') }}",
                            data: {
                                'term': value
                            },
                            type: 'POST',
                            success: function (data) {
                                if (data.length > 0) {
                                    for (const address of data) {
                                        content += itemTemplate
                                            .replace('{VALUE}', address.label)
                                            .replace('{TECHNICAL_ID}', address.data.technical_id)
                                            .replace('{TYPE}', address.data.type)
                                            .replace('{NAME}', address.data.name)
                                            .replace('{ADDRESS}', address.data.address)
                                            .replace('{ADDRESS2}', address.data.address2)
                                            .replace('{CITY}', address.data.city)
                                            .replace('{ZIPCODE}', address.data.zipcode)
                                            .replace('{COUNTRY_CODE}', address.data.country_code)
                                            .replace('{COUNTRY}', address.data.country)
                                            .replace('{RECIPIENT}', address.data.recipient)
                                            .replace('{RECIPIENT_PHONE}', address.data.recipient_phone)
                                            .replace('{RECIPIENT_COMMENT}', address.data.recipient_comment)
                                        ;
                                    }

                                    $addressAutocompleteList.html(content);
                                    $addressAutocompleteList.css('display', 'block');
                                } else {
                                    $addressAutocompleteList.css('display', 'none');
                                }
                            },
                            error: function () {
                                modalLoading.close();
                            }
                        });
                    } else {
                        $addressAutocompleteList.css('display', 'none');
                    }
                });

                $(document).on('click', '.address-autocomplete-item', function () {
                    $('#address-autocomplete-list').css('display', 'none');
                    $('#autocomplete-shipping_address').val($(this).val());

                    const item = {
                        data: {
                            technical_id: $(this).data('technical_id'),
                            type: $(this).data('type'),
                            name: $(this).data('name'),
                            address: $(this).data('address'),
                            address2: $(this).data('address2'),
                            city: $(this).data('city'),
                            zipcode: $(this).data('zipcode'),
                            country_code: $(this).data('country_code'),
                            country: $(this).data('country'),
                            recipient: $(this).data('recipient'),
                            recipient_phone: $(this).data('recipient_phone'),
                            recipient_comment: $(this).data('recipient_comment')
                        }
                    }
                    //close modal
                    showButtonModifyAdress();
                    mapAddress(item, true, closeShippingAddressUpdateModal);

                });

                /**
                 * Add new shipping address
                 */
                $(document).on('click', '#new-shipping_address', function (event) {
                    event.preventDefault();
                    newShippingAddress();
                });

                $('#cart-shipping-address form').submit(function (e) {
                    e.preventDefault();
                    return false;
                });

                $('#shipping-address').find('[name="addressId"], [name*="[contact]"], [name*="[phone]"], [name*="[comment]"]').change(modifyRecipient);

                $('[href="#anchorCheckout"]').parents('form').on(
                    'submit',
                    function (e) {
                        cartSubmit.buttonControl(e, true)
                    }
                );
                recipientMandatory();
            });
        </script>

        {# include cart modals templates #}
        {% include '@OpenFront/cart/cart-modals-templates.html.twig' %}

    {% endif %}
{% endblock %}
