{% extends '@OpenFront/menu/buyer_menu.html.twig' %}
{% trans_default_domain 'AppBundle' %}
{# @var order \AppBundle\Model\MerchantOrder #}
{# @var item \AppBundle\Model\Cart\CartItem #}

{% block title %}{{ 'orders.detail.order_detail' | trans }} {{ order.id }}{% endblock %}

{% block body %}

    {% block searchbar %}
        {% if formSearchBar is defined %}
            {% include('@OpenFront/component/search-bar.html.twig') %}
        {% else %}
            {{ render(controller("Open\\FrontBundle\\Controller\\SearchBarController::formAction")) }}
        {% endif %}
    {% endblock %}

    <div class="Page-cart-inner Page-order-detail">

        <div class="order-resume-block">
            {% include '@OpenFront/order/merchant_order_header_block.html.twig' with {'order': order} %}
        </div>

        <div class="order-detail-block">
            {% include '@OpenFront/order/merchant_order_details_block.html.twig' with {'order': order} %}
        </div>
    </div>
    {% if userIsMerchantOrderBuyer and order.merchantOrderEntity.confirmedBySupplier %}
        {% include '@OpenFront/order/merchant_order_feedback.html.twig' with {'form': form} %}
    {% endif %}
    <script type="text/template" id="js-supplier-order-question-modal-tpl">
        <div class="Modal-header">
            <button type="button" class="close js-cancel-button" data-dismiss="modal" aria-label="Close">
                <span>×</span>
            </button>
            <h5 class="Modal-title">{{ 'modal.placeholder'|trans([], 'AppBundle') }}</h5>
        </div>
        <form id="orderQuestionForm" method="post" name="orderQuestionForm">
            <div class="Modal-body">
                <p><span>{{ 'orders.modal.question.id_label'|trans([], 'AppBundle') }}</span> : {{ order.id }}</p>
                <input type="hidden" name="order" value="{{ order.id }}"/>
                {#- TODO add orderIdNumber for supplier #}
                {# <input type="hidden" name="orderNumberId" value={{ order.merchantOrderEntity.orderIdNumber }}/> #}
                <input type="hidden" name="merchant" value="{{ order.merchant.id }}"/>
                <input type="hidden" name="_token" value="{{ csrf_token('message_order_send') }}"/>
                <textarea placeholder="{{ 'modal.placeholder'|trans([], 'AppBundle') }}" rows="2" cols="5"
                          id="supplier-order-question-subject" name="message"></textarea>
            </div>
            <div class="Modal-footer">
                <button id="js-question-order-send-button" type="button" class="btn btn-primary buttonModal"
                        name="form[orderQuestionForm]">{{ 'modal.send'|trans({}, 'AppBundle')|upper }}</button>
            </div>
        </form>
    </script>

{% endblock %}
{% block javascripts %}
    <script>
        $(document).ready(function () {
            $('#notify-manager-again').on('click', function (e) {
                e.preventDefault();

                let modalLoading = window.UI.Modal.showLoading();

                $.ajax({
                    url: "{{ path('front.notify.pending-manager-validation') }}",
                    data: {
                        'form[_token]': '{{ csrf_token('notify_again') }}',
                        'form[cartId]': {{ order.merchantOrderEntity.metaCart.id }},
                    },
                    type: 'POST',
                    success: function (data) {
                        modalLoading.close();

                        if (data.success) {
                            window.UI.Modal.alert("{{ 'orders.modal.confim_notify_again'|trans }}");
                        } else {
                            window.UI.Modal.alert("{{ 'error.generic.support'|trans }}");
                        }
                    },
                    error: function () {
                        modalLoading.close();
                        window.UI.Modal.alert("{{ 'error.generic.support'|trans }}");
                    }
                });
            });

            function reorder ($url){

                $.getJSON(
                    $url,
                    null,
                    function(jsonData) {
                        window.location.href = "{{ path('cart.details') }}";
                    }
                ).fail(function (error) {
                    if (error.status === 400) {
                        window.UI.Modal.alert("{{ 'orders.errors.reorder' | trans | escape('js') }}");
                    } else {
                        window.location.reload();
                    }
                });
            }

            $('[data-reorder]').on('click', function(e) {
                e.stopPropagation();

                window.UI.Modal.showLoading();
                $orderUrl =  $(this).data('reorder');
                $.getJSON(
                    $(this).data('checkreorder'),
                    null,
                    function(jsonData) {
                        console.log(jsonData["status"]);
                        if(jsonData["status"]==="ALL"){
                            reorder($orderUrl);
                        }
                        if(jsonData["status"]==="NONE"){
                            window.UI.Modal.alert("{{ 'orders.reorder.unavailable' | trans | escape('js') }}");
                        }
                        if(jsonData["status"]==="SOME") {
                            removeModal = window.UI.Modal.confirm("{{ 'orders.reorder.partial'|trans({}, 'AppBundle')|raw }}", "",
                                function () {
                                    window.UI.Modal.showLoading();
                                    reorder($orderUrl);
                                }, function () {
                                    this.removeModal.close();
                                });
                        }
                    }
                ).fail(function (error) {
                    if (error.status === 400) {
                        window.UI.Modal.alert("{{ 'orders.errors.reorder' | trans | escape('js') }}");
                    } else {
                        window.location.reload();
                    }
                });

            });
        });
    </script>
{% endblock %}
