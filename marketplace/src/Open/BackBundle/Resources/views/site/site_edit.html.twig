{% extends '@OpenBack/base.html.twig' %}
{% trans_default_domain "AppBundle" %}
{% form_theme form 'bootstrap_4_layout.html.twig' %}

{% block body %}
    <div class="container">

        <div style="width:100%; padding: 0 0 20px 5px">
            <a href="{{ path('admin.site.info', {'id': id}) }}" style="display:flex; align-items: center; color: #9600FF">
                <div style="width: 10px; height: 10px; border: solid; border-color: #9600FF; border-width: 0 2px 2px 0;transform: rotate(135deg); margin-right: 5px"></div>
                {{ 'back.back' | trans }}
            </a>
        </div>

        <div class="SiteForm">
            {{ form_start(form) }}
            <div class="card">
                <div class="card-body">
                    {{ form_row(form.name) }}
                </div>
            </div>
            <div class="form-row Buttons">
                <div class="col-md-12">
                {{ form_row(form.save, {'attr':{'class':' btn btn-primary'}}) }}
                </div>
            </div>
            {{ form_end(form) }}
        </div>
    </div>
{% endblock %}

{% block javascripts %}

    <script type="text/javascript">

        'use strict';

        document.addEventListener('DOMContentLoaded', function() {
            var $form = $('#js-site-form');


            // Bind click events on remove buttons
            $form.on('click', '.js-doc-remove', function (ev) {
                var $target = $(ev.currentTarget);
                var docType = $target.data('docType');
                var docId = parseInt($target.data('docId'), 0);
                var siteId = parseInt($target.data('companyId'), 0);

                if (window.confirm('{{ 'document.upload.delete'|trans }}') === true) {
                    // Call backend to tell izberg to update the quantity
                    var $parent = $target.parent();
                    var $cont = $parent.parent();
                    $.ajax(
                        Routing.generate('document.remove'),
                        {
                            type : 'POST',
                            dataType : 'json',
                            data : {
                                docId : docId,
                                siteId : siteId,
                                docType : docType
                            },
                            success: function(data)
                            {
                                $parent.fadeOut('fast', function () {
                                    $parent.remove();

                                    $('#'+'js-' + docType  + '-upload-message').css('display', 'none');

                                    if ($cont.find('li').length === 0) {
                                        $('#'+'js-' + docType  + '-nodocs').css('display', 'block');
                                    }
                                });
                            },
                            error: function(response, textStatus, errorThrown)
                            {
                                alert('{{ 'document.upload.deleteError'|trans }}');
                                console.log('Error [' + errorThrown + '] : ' + response.responseText ); //Todo: flash user about the error
                            }
                        }
                    );
                }
            });
        });
    </script>
{% endblock %}