<?php

namespace Open\IzbergBundle\Api;

use Open\IzbergBundle\Api;
use Open\IzbergBundle\Model\Message;
use Open\TicketBundle\Model\MessageActor;
use phpDocumentor\Reflection\Types\Object_;
use Symfony\Component\HttpFoundation\File\UploadedFile;
use Unirest\Response;

class MessageApi extends Api
{
    public function  getUri(){
        return 'message';
    }

    public function getItemClass(){
        return Message::class;
    }

    public function contactMerchant($object, $message, $sender, $receiver, UploadedFile ...$attachments)
    {
        $multipartData = [
            [
                'name'     => 'sender',
                'contents' => sprintf('/v1/user/%s/', $sender),
            ],
            [
                'name'     => 'receiver',
                'contents' => sprintf('/v1/merchant/%s/', $receiver),
            ],
            [
                'name'     => 'subject',
                'contents' => $object,
            ],
            [
                'name'     => 'body',
                'contents' => $message,
            ],
        ];

        $multipartData = array_merge(
            $multipartData,
            array_map(
                function(UploadedFile $attachment) {
                    return [
                        'name' => 'attachment',
                        'contents' => fopen($attachment->getPathname(), 'r+'),
                        'headers' => [self::HEADER_CONTENT_TYPE => $attachment->getMimeType()],
                        'filename' => $attachment->getClientOriginalName(),
                    ];
                },
                $attachments
            )
        );
        $header = array_filter(
            $this->getAuthorizationHeaders(),
            function($headerName) {
                return ($headerName != self::HEADER_CONTENT_TYPE);
            },
            ARRAY_FILTER_USE_KEY
        );
        $options = [
            'headers' => $header ,
            'multipart' => $multipartData
        ];

        $response = $this->client->request(
            'POST',
            $this->getApiUrl() . 'message/',
            $options
        );
        $threadId = null;
        if($response->getStatusCode()==201){
            $data = json_decode ((string)$response->getBody(), true);
            $threadId = $data['id'];
        }
        else{
            $this->logService->error("creating thread status:".$response->getStatusCode(),"MESSAGE_API",null,["response"=>$response->getReasonPhrase(), "header"=>$header, 'applicationId'=>$this->getApplicationId(), 'multipart'=>$multipartData]);
        }
        return $threadId;
    }

    public function startQuoteThread($object, $message, $sender, $receiver, UploadedFile ...$attachments)
    {
        $object = mb_substr($object, 0, 200);

        $multipartData = [
            [
                'name'     => 'sender',
                'contents' => sprintf('/v1/user/%s/', $sender),
            ],
            [
                'name'     => 'receiver',
                'contents' => sprintf('/v1/merchant/%s/', $receiver),
            ],
            [
                'name'     => 'subject',
                'contents' => $object,
            ],
            [
                'name'     => 'body',
                'contents' => $message,
            ],
            [
                'name'     => 'message_type_key',
                'contents' => 'quote',
            ],
            [
                'name'     => 'message_type_name',
                'contents' => 'quote',
            ],

        ];

        $multipartData = array_merge(
            $multipartData,
            array_map(
                function(UploadedFile $attachment) {
                    return [
                        'name' => 'attachment',
                        'contents' => fopen($attachment->getPathname(), 'r+'),
                        'headers' => [self::HEADER_CONTENT_TYPE => $attachment->getMimeType()],
                        'filename' => $attachment->getClientOriginalName(),
                    ];
                },
                $attachments
            )
        );

        $header = array_filter(
            $this->getAuthorizationHeaders(),
            function($headerName) {
                return ($headerName != self::HEADER_CONTENT_TYPE);
            },
            ARRAY_FILTER_USE_KEY
        );

        $options = [
            'headers' =>  $header,
            'multipart' => $multipartData
        ];

        $response = $this->client->request(
            'POST',
            $this->getApiUrl() . 'message/',
            $options
        );
        $threadId = null;
        if($response->getStatusCode()==201){
           $data = json_decode ((string)$response->getBody(), true);
            $threadId = $data['id'];
        }else{
            $this->logService->error("creating quote thread status:".$response->getStatusCode(),"MESSAGE_API",null,["response"=>$response->getReasonPhrase()]);
        }
        return $threadId;
    }

    public function contactOperator(string $subject, string $message, int $senderId)
    {
        $data = [
            "sender"=> [
                'id' => $senderId,
                'resource_uri' => '/v1/user/'.$senderId.'/',
            ],
            "receiver" => [
                'id' => $this->getApplicationId(),
                'resource_uri' => '/v1/application/'.$this->getApplicationId().'/',
            ],
            "subject" => $subject,
            "body" => $message,
        ];

        $response = $this->sendApiRequest(
            self::HTTP_POST_OPERATION,
            'message/',
            $data,
            [],
            [
                self::OPTION_NO_DATA_RETURN => true,
            ]
        );

        return $response->code;
    }

    public function replyToOperatorMessage(string $subject, string $message, $senderId, int $messageId, UploadedFile ...$attachments)
    {
        $receiverUri = sprintf('/v1/application/%s/', $this->getApplicationId());
        $senderUri =  sprintf('/v1/user/%s/', $senderId);
        return $this->replyToMessage($subject, $message, $senderUri, $receiverUri, $messageId, ...$attachments);
    }

    public function replyToMerchantMessage(?string $subject, string $message, int $senderId, int $merchantId, int $messageId, UploadedFile ...$attachments)
    {
        $receiverUri = sprintf('/v1/merchant/%s/', $merchantId);
        $senderUri =  sprintf('/v1/user/%s/', $senderId);
        return $this->replyToMessage($subject, $message, $senderUri, $receiverUri, $messageId, ...$attachments);
    }

    public function replyToUser(?string $subject, string $message, int $userId, int $merchantId, int $messageId, UploadedFile ...$attachments)
    {
        $senderUri = sprintf('/v1/merchant/%s/', $merchantId);
        $receiverUri =  sprintf('/v1/user/%s/', $userId);
        return $this->replyToMessage($subject, $message, $senderUri, $receiverUri, $messageId, ...$attachments);
    }

    private function replyToMessage(?string $subject, string $message, string $senderUri, string $receiverUri, int $messageId, UploadedFile ...$attachments)
    {
        $multipartData = [
            [
                'name'     => 'application',
                'contents' => sprintf('/v1/application/%s/', $this->getApplicationId()),
            ],
            [
                'name'     => 'sender',
                'contents' => $senderUri,
            ],
            [
                'name'     => 'receiver',
                'contents' => $receiverUri,
            ],
            [
                'name'     => 'subject',
                'contents' => $subject,
            ],
            [
                'name'     => 'body',
                'contents' => $message,
            ],
            [
                'name' => 'root_msg',
                'contents' => sprintf('/v1/message/%s/', $messageId),
            ],
            [
                'name' => 'parent_msg',
                'contents' => sprintf('/v1/message/%s/', $messageId),
            ],
        ];

        $multipartData = array_merge(
            $multipartData,
            array_map(
                function(UploadedFile $attachment) {
                    return [
                        'name' => 'attachment',
                        'contents' => fopen($attachment->getPathname(), 'r+'),
                        'headers' => [self::HEADER_CONTENT_TYPE => $attachment->getMimeType()],
                        'filename' => $attachment->getClientOriginalName(),
                    ];
                },
                $attachments
            )
        );

        $options = [
            'headers' =>  array_filter(
                $this->getAuthorizationHeaders(),
                function($headerName) {
                    return ($headerName != self::HEADER_CONTENT_TYPE);
                },
                ARRAY_FILTER_USE_KEY
            ),
            'multipart' => $multipartData
        ];

        $response = $this->client->request(
            'POST',
            $this->getApiUrl() . 'message/',
           $options
        );

        return $response->getStatusCode();
    }

    public function replyToMerchantMessageQuote(?string $subject, string $message, int $senderId, int $merchantId, int $messageId, UploadedFile ...$attachments)
    {
        $receiverUri = sprintf('/v1/merchant/%s/', $merchantId);
        $senderUri =  sprintf('/v1/user/%s/', $senderId);
        return $this->replyToMessageQuote($subject, $message, $senderUri, $receiverUri, $messageId,false, ...$attachments);
    }

    public function replyToUserQuote(?string $subject, string $message, int $userId, int $merchantId, int $messageId, UploadedFile ...$attachments)
    {
        $senderUri = sprintf('/v1/merchant/%s/', $merchantId);
        $receiverUri =  sprintf('/v1/user/%s/', $userId);
        return $this->replyToMessageQuote($subject, $message, $senderUri, $receiverUri, $messageId, true, ...$attachments);
    }

    private function replyToMessageQuote(?string $subject, string $message, string $senderUri, string $receiverUri, int $messageId,bool $operator = false, UploadedFile ...$attachments)
    {

        $multipartData = [
            [
                'name'     => 'application',
                'contents' => sprintf('/v1/application/%s/', $this->getApplicationId()),
            ],
            [
                'name'     => 'sender',
                'contents' => $senderUri,
            ],
            [
                'name'     => 'receiver',
                'contents' => $receiverUri,
            ],
            [
                'name'     => 'subject',
                'contents' => $subject,
            ],
            [
                'name'     => 'body',
                'contents' => $message,
            ],
            [
                'name' => 'root_msg',
                'contents' => sprintf('/v1/message/%s/', $messageId),
            ],
            [
                'name' => 'parent_msg',
                'contents' => sprintf('/v1/message/%s/', $messageId),
            ],
        ];

        $multipartData = array_merge(
            $multipartData,
            array_map(
                function(UploadedFile $attachment) {
                    return [
                        'name' => 'attachment',
                        'contents' => fopen($attachment->getPathname(), 'r+'),
                        'headers' => [self::HEADER_CONTENT_TYPE => $attachment->getMimeType()],
                        'filename' => $attachment->getClientOriginalName(),
                    ];
                },
                $attachments
            )
        );
        $header = array_filter(
            $this->getAuthorizationHeaders($operator),
            function($headerName) {
                return ($headerName != self::HEADER_CONTENT_TYPE);
            },
            ARRAY_FILTER_USE_KEY
        );
        $options = [
            'headers' =>  $header,
            'multipart' => $multipartData
        ];

        $response = $this->client->request(
            'POST',
            $this->getApiUrl() . 'message/',
            $options
        );

        if($response->getStatusCode()!=201){

            $this->logService->error("message api error:".$response->getStatusCode(),"MESSAGE_API",null, ["message"=>$response->getReasonPhrase(),"header"=>$header]);
            return null;
        }
        $data = json_decode ((string)$response->getBody());
        return $data;
    }

    public function disputeMerchantOrder($object, $message, $sender, $receiver, $merchantOrderId)
    {
        $data = array(
            "sender"=> "/v1/user/".$sender."/",
            "receiver" => "/v1/merchant/".$receiver."/",
            "subject" => $object,
            "body" => $message,
            "merchant_order" => "/v1/merchant_order/" . $merchantOrderId . "/"
        );

        $response = $this->sendApiRequest(
            self::HTTP_POST_OPERATION,
            'message/',
            $data
        );

        return $response->body->id;
    }

    public function answerMerchant($object, $message, $sender, MessageActor $receiver, $urlParent){
        $application_id = $this->getApplicationId();

        $receiverUrlBuilder = function(MessageActor $receiverModel) {
            if ($receiverModel->isMerchantType()) {
                return sprintf('/v1/merchant/%d/', $receiverModel->getId());
            }

            if ($receiverModel->isApplicationType()) {
                return sprintf('/v1/application/%d/', $receiverModel->getId());
            }

            return null;
        };

        $data = [
            "sender"=> "/v1/user/".$sender."/",
            "receiver" => call_user_func($receiverUrlBuilder, $receiver),
            "subject" =>$object,
            "body" => $message,
            "application" => "/v1/application/".$application_id."/",
            "root_msg" => $urlParent,
            "parent_msg" => $urlParent
        ];

        $response = $this->sendApiRequest(
            self::HTTP_POST_OPERATION,
            'message/',
            $data,
            [],
            [
                self::OPTION_NO_DATA_RETURN => true,
            ]
        );

        return $response->code;
    }

    public function getBuyerThreads($offset = 0, $limit = 10)
    {
        $ret = array();
        $requestUrl = "message/?parent_msg=none&message_type_key__isnull=true&offset=$offset&limit=$limit";
            $response = $this->sendApiRequest(
                self::HTTP_GET_OPERATION,
                $requestUrl
            );
            $ret = $response->body;//dd($response);
            //$requestUrl = $response->body->meta->next;
        return $ret;
    }

    public function getBuyerOutboxMessages($id){
        $ret = array();

        $requestUrl = 'message/?parent_msg=none&from_user=' .$id;
        do {
            $response = $this->sendApiRequest(
                self::HTTP_GET_OPERATION,
                $requestUrl
            );

            $ret = array_merge($ret, $response->body->objects);
            $requestUrl = $response->body->meta->next;
        }while($requestUrl !== null);

        return $ret;
    }

    public function getBuyerInboxMessages($id)
    {
        $ret = [];
        $requestUrl = 'message/?parent_msg=none&to_user=' .$id;
        do {
            $response = $this->sendApiRequest(
                self::HTTP_GET_OPERATION,
                $requestUrl
            );

            $ret = array_merge($ret, $response->body->objects);

            $requestUrl = $response->body->meta->next;
        }while($requestUrl !== null);

        return $ret;
    }

    public function getUnreadMessageForUser($id)
    {
        try{
            $response = $this->sendApiRequest(
                self::HTTP_GET_OPERATION,
                'message/?status=unread&aggregate_count_on=root_msg&to_user=' .$id,
                [],
                [],
                [
                    self::OPTION_IS_OPERATOR => true
                ]
            );
            return $response->body;
        }catch (\Exception $exception) {
            $response = new Response(200, '{"meta": {}, "objects": []}','');
            return $response->body;
        }
    }

    public function getUnreadMessageListForUser($threadId, $userId)
    {
        $route = 'message/?status=unread&root_msg='.$threadId.'&to_id=' .$userId;
        $response = $this->sendApiRequest(
            self::HTTP_GET_OPERATION,
            $route,
            [],
            [],
            [
                self::OPTION_IS_OPERATOR => true
            ]

        );
        return $response->body;
    }

    public function markAsRead($id)
    {
        $response = $this->sendApiRequest(
            self::HTTP_POST_OPERATION,
            'message/' . $id . '/read/',
            null,
            [],
            [
                self::OPTION_NO_DATA_RETURN => true
            ]
        );

        return $response->code;
    }

    public function getMessageById($id)
    {
        $response = $this->sendApiRequest(
            self::HTTP_GET_OPERATION,
            'message/' . $id
        );

        return $response->body;
    }

    public function getMessagesByRootId($id)
    {
        $route = 'message/?limit=100&root_msg=' . $id;
        $response = $this->sendApiRequest(
            self::HTTP_GET_OPERATION,
            $route,
            [],
            [],
            [
                self::OPTION_IS_OPERATOR => true
            ]
        );
        return $response->body;
    }

    public function getMessageAttachments(int $messageId): array
    {
        $response = $this->sendApiRequest(
            self::HTTP_GET_OPERATION,
            'message-attachment/?message=' . $messageId,
            [],
            [],
            [
                self::OPTION_IS_OPERATOR => true
            ]
        );

        return $response->body->objects ?? [];
    }

    public function fetchAttachmentUrl(string $attachmentId): ?string
    {

        $response = $this->sendApiRequest(
            self::HTTP_GET_OPERATION,
            'message-attachment/?id=' . $attachmentId,
            [],
            [],
            [
                self::OPTION_IS_OPERATOR => true
            ]
        );
        $attachments = $response->body->objects ?? [];
        if (count($attachments)) {
            $attachment = array_shift($attachments);

            return $attachment->file_content;
        }

        return null;
    }

    public function getDisputesByMerchantOrderId($merchantOrderId)
    {
        $response = $this->sendApiRequest(
            self::HTTP_GET_OPERATION,
            'message/?parent_msg=none&limit=100&merchant_order='.$merchantOrderId
        );

        return $response->body;
    }
}
