<?php
/**
 * Created by PhpStorm.
 * User: CMA15450
 * Date: 24/03/2017
 * Time: 16:31
 */

namespace Open\IzbergBundle\Model;

use Symfony\Component\Validator\Constraints as Assert;
use <PERSON><PERSON>\Serializer\Annotation\Type;
use <PERSON><PERSON>\Serializer\Annotation\Exclude;
use <PERSON><PERSON>\Serializer\Annotation as JSM;

/**
 * Entity to map a cart item object
 * @JSM\ExclusionPolicy("none")
 */
class CartItem
{

    /**
     * @Type("integer")
     * @Assert\NotBlank()
     */
    private $id = 0;

    /**
     * @Type("string")
     * @Assert\NotBlank()
     */
    private $name;

    /**
     * @Type("integer")
     * @Assert\NotBlank()
     */
    private $quantity = 1;

    /**
     * @Type("integer")
     * @Assert\NotBlank()
     */
    private $sku;

    /**
     * @Type("double")
     * @Assert\NotBlank()
     */
    private $unit_price = 0;

    /**
     * @Type("double")
     * @Assert\NotBlank()
     */
    private $unit_price_vat_included = 0;

    /**
     * @Type("double")
     * @Assert\NotBlank()
     */
    private $unit_shipping = 0;

    /**
     * @Type("double")
     * @Assert\NotBlank()
     */
    private $unit_vat = 0;

    /**
     * @Type("Open\IzbergBundle\Model\Merchant")
     * @Assert\NotBlank()
     */
    private $merchant = null;

    /**
     * @Type("Open\IzbergBundle\Model\Cart")
     */
    private $cart = null;

    /**
     * @Type("double")
     * @Assert\NotBlank()
     */
    private $tax_rate = 0;

    /**
     * @Type("string")
     * @Assert\NotBlank()
     */
    private $image_url;

    /**
     * @Type("Open\IzbergBundle\Model\Offer")
     * @Assert\NotBlank()
     */
    private $offer;

    /**
     * @var string
     * @Exclude()
     */
    private $currency;

    /**
     * @Type("Open\IzbergBundle\Model\CartItemExtraInfo")
     *
     * @var CartItemExtraInfo|null
     */
    private $extra_info;

    /**
     * Get cart item Id
     * @return integer
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * Set cart item Id
     * @param $id
     * @return integer
     */
    public function setId($id)
    {
        return $this->id = intval($id);
    }

    /**
     * Get cart item name
     * @return mixed
     */
    public function getName()
    {
        return $this->name;
    }

    /**
     * Set cart item name
     * @param $name
     * @return mixed
     */
    public function setName($name)
    {
        return $this->name = $name;
    }

    /**
     * @return mixed
     */
    public function getTaxRate()
    {
        return $this->tax_rate;
    }

    /**
     * @param mixed $tax_rate
     */
    public function setTaxRate($tax_rate): void
    {
        $this->tax_rate = floatval($tax_rate);
    }

    /**
     * Get item quantity
     * @return integer
     */
    public function getQuantity()
    {
        return $this->quantity;
    }

    /**
     * @return mixed
     */
    public function getMerchant()
    {
        return $this->merchant;
    }

    /**
     * @param mixed $merchant
     */
    public function setMerchant($merchant): void
    {
        $this->merchant = $merchant;
    }

    /**
     * Set item quantity
     * @param $qty
     * @return int
     */
    public function setQuantity($qty)
    {
        return $this->quantity = intval($qty);
    }

    /**
     * Get price per unit (no vat)
     * @return float
     */
    public function getUnitPrice()
    {
        return $this->unit_price;
    }

    /**
     * Set price per unit (no vat)
     * @param $unit_price
     * @return float
     */
    public function setUnitPrice($unit_price)
    {
        return $this->unit_price = floatval($unit_price);
    }

    /**
     * get vat per unit
     * @return float
     */
    public function getUnitVat()
    {
        return $this->unit_vat;
    }

    /**
     * set vat per unit
     * @param $vat
     * @return float
     */
    public function setUnitVat($vat)
    {
        return $this->unit_vat = floatval($vat);
    }

    /**
     * Get price per unit (vat included)
     * @return float
     */
    public function getUnitPriceVatIncluded()
    {
        return floatval($this->unit_vat + $this->unit_price);
    }

    /**
     * Set price per unit (vat include)
     * @param $unit_price_vat_included
     * @return mixed
     */
    public function setUnitPriceVatIncluded($unit_price_vat_included)
    {
        return $this->unit_price_vat_included = $unit_price_vat_included;
    }

    /**
     * @return mixed
     */
    public function getCart()
    {
        return $this->cart;
    }

    /**
     * @param mixed $cart
     */
    public function setCart($cart): void
    {
        $this->cart = $cart;
    }

    /**
     * @return mixed
     */
    public function getUnitShipping()
    {
        return $this->unit_shipping;
    }

    /**
     * @param mixed $unit_shipping
     */
    public function setUnitShipping($unit_shipping): void
    {
        $this->unit_shipping = $unit_shipping;
    }

    /**
     * @return mixed
     */
    public function getImageUrl()
    {
        return $this->image_url;
    }

    /**
     * @param mixed $image_url
     */
    public function setImageUrl($image_url): void
    {
        $this->image_url = $image_url;
    }

    /**
     * @return Offer|null
     */
    public function getOffer(): ?Offer
    {
        return $this->offer;
    }

    /**
     * @param mixed $offer
     */
    public function setOffer($offer): void
    {
        $this->offer = $offer;
    }

    /**
     * @return mixed
     */
    public function getSku()
    {
        return $this->sku;
    }

    /**
     * @param mixed $sku
     */
    public function setSku($sku): void
    {
        $this->sku = $sku;
    }

    public function getCurrency(): ?string
    {
        return $this->currency;
    }

    public function setCurrency(?string $currency): self
    {
        $this->currency = $currency;
        return $this;
    }

    /**
     * @return CartItemExtraInfo|null
     */
    public function getExtraInfo(): ?CartItemExtraInfo
    {
        return $this->extra_info;
    }

    /**
     * @param CartItemExtraInfo $extra_info
     * @return $this
     */
    public function setExtraInfo(CartItemExtraInfo $extra_info): self
    {
        $this->extra_info = $extra_info;
        return $this;
    }
}