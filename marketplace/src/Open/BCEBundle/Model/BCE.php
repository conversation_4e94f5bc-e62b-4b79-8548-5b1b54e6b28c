<?php

namespace Open\BCEBundle\Model;


class BCE implements \JsonSerializable
{

    protected $derniereMAJ;

    protected $rateDate;

    protected $rates;

    public function getRateDate()
    {
        return $this->rateDate;
    }

    public function setRateDate($rateDate)
    {
        if (is_string($rateDate)){
            $rateDate = \DateTime::createFromFormat('Y-m-d', $rateDate);
        }
        $this->rateDate = $rateDate;
    }

    public function setDerniereMaj(\DateTime $uneDate = null)
    {
        if($uneDate === null){
            $this->derniereMAJ = new \DateTime();
        }else{
            $this->derniereMAJ = $uneDate;
        }
    }

    public function getDerniereMaj()
    {
        return $this->derniereMAJ;
    }

    public function __construct()
    {
        $this->setDerniereMaj();
    }

    public function getRates()
    {
        return json_decode($this->rates, true);
    }

    public function setRates($rates)
    {
        $this->rates = json_encode($rates);
    }

    public function jsonSerialize() {
        $ret = [
            'date'  => $this->getRateDate(),
            'rates' => json_decode($this->getRates()),
            ];
        return json_encode($ret);
    }
}

