import React, {Component} from "react";
import axios from "axios";
import {uri} from "../../config/services";
import {Trans} from "react-i18next";
import Paper from "@material-ui/core/Paper";
import Tabs from "@material-ui/core/Tabs";
import Tab from "@material-ui/core/Tab";
import Badge from "@material-ui/core/Badge";
import {withStyles} from "@material-ui/core/styles";
import Table from "@material-ui/core/Table";
import TableBody from "@material-ui/core/TableBody";
import TableCell from "@material-ui/core/TableCell";
import TableHead from "@material-ui/core/TableHead";
import TableRow from "@material-ui/core/TableRow";
import TablePagination from "@material-ui/core/TablePagination";
import MyTableRow from "./tablerow";

const StyledBadge = withStyles((theme) => ({
    badge: {
        right: 0,
        top: 0,
    },
}))(Badge);

export default class SimpleTable extends Component {
    selected = 0;

    send = false;

    history;

    filtersGroups = [
        {
            label: <Trans>table_tab_header_new</Trans>,
            value: "new",
            status: ["new"],
            columns: [
                {
                    name: "buyer",
                    label: <Trans>table_header_buyer</Trans>,
                },
                {
                    name: "creation_date",
                    label: <Trans>table_header_date</Trans>,
                },
                {
                    name: "subject",
                    label: <Trans>table_header_subject</Trans>,
                },
            ],
        },
        {
            label: <Trans>table_tab_header_draft</Trans>,
            value: "vendor_draft",
            status: ["draft", "redraft"],
            columns: [
                {
                    name: "quote_number",
                    label: <Trans>table_header_quote_number</Trans>,
                },
                {
                    name: "buyer",
                    label: <Trans>table_header_buyer</Trans>,
                },
                {
                    name: "creation_date",
                    label: <Trans>table_header_date</Trans>,
                },
                {
                    name: "subject",
                    label: <Trans>table_header_subject</Trans>,
                },
            ],
        },
        {
            label: <Trans>table_tab_header_send</Trans>,
            value: "send",
            status: ["send"],
            columns: [
                {
                    name: "quote_number",
                    label: <Trans>table_header_quote_number</Trans>,
                },
                {
                    name: "buyer",
                    label: <Trans>table_header_buyer</Trans>,
                },
                {
                    name: "creation_date",
                    label: <Trans>table_header_send_date</Trans>,
                },
                {
                    name: "subject",
                    label: <Trans>table_header_subject</Trans>,
                },
            ],
        },
        {
            label: <Trans>table_tab_header_validated</Trans>,
            value: "validated",
            status: ["validated"],
            columns: [
                {
                    name: "quote_number",
                    label: <Trans>table_header_quote_number</Trans>,
                },
                {
                    name: "buyer",
                    label: <Trans>table_header_buyer</Trans>,
                },
                {
                    name: "validation_date",
                    label: <Trans>table_header_validation_date</Trans>,
                },
                {
                    name: "subject",
                    label: <Trans>table_header_subject</Trans>,
                },
            ],
        },
        {
            label: <Trans>table_tab_header_refused</Trans>,
            value: "vendor_cancelled",
            status: ["cancelled", "refused"],
            columns: [
                {
                    name: "quote_number",
                    label: <Trans>table_header_quote_number</Trans>,
                },
                {
                    name: "buyer",
                    label: <Trans>table_header_buyer</Trans>,
                },
                {
                    name: "cancel_date",
                    label: <Trans>table_header_cancel_date</Trans>,
                },
                {
                    name: "subject",
                    label: <Trans>table_header_subject</Trans>,
                },
            ],
        },
    ];

    constructor() {
        super();
        this.state = {
            items: [],
            columns: [],
            unread: [],
            page_count: 1,
            items_per_page: 10,
            total_item_count: 0,
            current_page: 1,
        };
        this.tabChange = this.tabChange.bind(this);
        this.getTableData = this.getTableData.bind(this);
        this.changeTable = this.changeTable.bind(this);
    }

    changeTable(event, page) {
        this.setState({
            columns: this.filtersGroups[this.selected].columns,
            items: [],
        });
        this.getTableData(page + 1, this.filtersGroups[this.selected].value);
    }

    componentDidMount() {
        if (this.props.status) {
            this.filtersGroups.forEach((f, k) => {
                if (f.status.includes(this.props.status)) {
                    this.selected = k;
                }
            });
        }
        this.changeTable(null, 0);
        axios({
            method: "get",
            url: uri("QUOTELIST") + window.merchantId + "/unread_count",
            responseType: "json",
        }).then((response) => {
            this.setState({unread: response.data});
        });
    }

    getTableData(page, status) {
        if (this.send === false) {
            this.send = true;
            axios({
                method: "get",
                url:
                    uri("QUOTELIST") +
                    window.merchantId +
                    "/status/" +
                    status +
                    "?page=" +
                    page +
                    "&limit=" +
                    this.state.items_per_page,
                responseType: "json",
            })
                .then((response) => {
                    const newState = Object.assign({}, this.state, response.data);
                    this.setState(newState);
                })
                .finally(() => {
                    this.send = false;
                });
        }
    }

    tabChange(e, v) {
        this.selected = v;
        this.setState({columns: this.filtersGroups[v].columns, items: []});
        this.getTableData(1, this.filtersGroups[v].value);
    }

    render() {
        return (
            <div>
                <h2>
          <span className="titleTable">
            <Trans>title</Trans>
          </span>
                </h2>
                <Paper square>
                    <Tabs
                        indicatorColor="primary"
                        textColor="primary"
                        aria-label="disabled tabs example"
                        value={this.selected}
                        onChange={this.tabChange}
                    >
                        {this.filtersGroups.map((filter, i) => {
                            return (
                                <Tab
                                    key={i}
                                    label={
                                        <TabLabel
                                            label={filter.label}
                                            count={this.state.unread[filter.value]}
                                        />
                                    }
                                />
                            );
                        })}
                    </Tabs>
                    <Table>
                        <TableHead>
                            <TableRow>
                                {this.state.columns.map((col, i) => (
                                    <TableCell key={i}>{col.label}</TableCell>
                                ))}
                            </TableRow>
                        </TableHead>
                        <TableBody>
                            {this.state.items.map((row) => (
                                <MyTableRow row={row} columns={this.state.columns}/>
                            ))}
                        </TableBody>
                    </Table>
                    <TablePagination
                        component="div"
                        rowsPerPageOptions={[]}
                        count={this.state.total_item_count}
                        rowsPerPage={this.state.items_per_page}
                        page={this.state.current_page - 1}
                        onChangePage={this.changeTable}
                    />
                </Paper>
            </div>
        );
    }
}

function TabLabel(props) {
    return (
        <div>
            <span style={{paddingRight: 25}}>{props.label}</span>
            {props.count > 0 && (
                <StyledBadge badgeContent={props.count} color="primary">
                    &nbsp;
                </StyledBadge>
            )}
        </div>
    );
}
