<?php
/**
 * Created by PhpStorm.
 * User: PPH04712
 * Date: 30/01/2018
 * Time: 11:58
 */

namespace AppBundle\Entity;
use Doctrine\ORM\Mapping as ORM;


/**
 * @ORM\Table(name="action_historization")
 * @ORM\Entity()
 */
class ActionHistorization
{

    /**
     * @var int
     *
     * @ORM\Column(name="id", type="integer")
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="AUTO")
     */
    private $id;


    /**
     * @var
     * @ORM\Column(name="ref_entity_id", type="string", length=50, nullable=true)
     */
    private $refEntityId;



    /**
     * @var string $type type of the history: 'delete', 'create', 'update'
     * @ORM\Column(name="type", type="string", length=10)
     */
    private $type;


    /**
     * @var string $className
     * @ORM\Column(name="class_name", type="string", length=100)
     */
    private $className;

    /**
     * created Time/Date
     *
     * @var \DateTime
     *
     * @ORM\Column(name="created_at", type="datetime", nullable=true)
     */
    private $createdAt;


    /**
     * Simulate a one too many unidirectional: We don't want a bidirectional to avoid performance issues (don't load all history of a user
     * when getting it from database)
     * So users is an array, but can only contain one element (that why a unique=true is defined on the inverJoinColumns)
     * @ORM\ManyToMany(targetEntity="AppBundle\Entity\User")
     * @ORM\JoinTable(name="user_history",
     *      joinColumns={@ORM\JoinColumn(name="history_id", referencedColumnName="id", unique=true)},
     *      inverseJoinColumns={@ORM\JoinColumn(name="user_id", referencedColumnName="id")}
     *      )
     */
    private $users = [];

    /**
     * @ORM\Column(name="change_set", type="array")
     */
    private $changeSet;

    /**
     * @return int
     */
    public function getId(): int
    {
        return $this->id;
    }

    /**
     * @param int $id
     */
    public function setId(int $id): void
    {
        $this->id = $id;
    }

    /**
     * @return \DateTime
     */
    public function getCreatedAt(): \DateTime
    {
        return $this->createdAt;
    }

    /**
     * @param \DateTime $createdAt
     */
    public function setCreatedAt(\DateTime $createdAt): void
    {
        $this->createdAt = $createdAt;
    }

    /**
     * @return mixed
     */
    public function getUsers()
    {
        return $this->users;
    }

    /**
     * add a user !!Only one user can be add here!!!
     * @param $user
     */
    public function addUser($user){
        $this->users[] = $user;
    }

    /**
     * @return mixed
     */
    public function getChangeSet()
    {
        return $this->changeSet;
    }

    /**
     * @param mixed $changeSet
     */
    public function setChangeSet($changeSet): void
    {
        $this->changeSet = $changeSet;
    }

    /**
     * @return string
     */
    public function getType(): string
    {
        return $this->type;
    }

    /**
     * @param string $type
     */
    public function setType(string $type): void
    {
        $this->type = $type;
    }

    /**
     * @return string
     */
    public function getClassName(): string
    {
        return $this->className;
    }

    /**
     * @param string $className
     */
    public function setClassName(string $className): void
    {
        $this->className = $className;
    }

    /**
     * @return mixed
     */
    public function getRefEntityId()
    {
        return $this->refEntityId;
    }

    /**
     * @param mixed $refEntityId
     */
    public function setRefEntityId($refEntityId): void
    {
        $this->refEntityId = $refEntityId;
    }








}