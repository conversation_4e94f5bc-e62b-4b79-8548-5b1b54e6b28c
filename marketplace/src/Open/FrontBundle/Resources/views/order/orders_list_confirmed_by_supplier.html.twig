{% extends '@OpenFront/order/orders_list.html.twig' %}
{% trans_default_domain 'AppBundle' %}

{% block orderlist %}
    {% if orders|length > 0 %}
        <table class="table desktop-only orders-table">
            <thead>
            <tr>
                <th style="width: 120px;">{{ 'orders.list.block.order_title' | trans }}</th>
                <th>{{ 'orders.list.block.supplier' | trans }}</th>
                <th>{{ 'orders.list.block.order_date' | trans }}</th>
                <th>{{ 'orders.list.block.validation_date' | trans }}</th>
                <th>{{ 'orders.list.block.cart_number' | trans }}</th>
                <th>{{ 'orders.list.block.address_title' | trans }}</th>
                <th>{{ 'orders.list.block.recipient' | trans }}</th>
                <th class="talign-r min-w-90">{{ 'orders.list.block.total_title' | trans }}</th>
                <th></th>
            </tr>
            </thead>
            <tbody>
            {% for order in orders %}
                <tr onclick="window.location ='{{ path('front.order.detail', {'id': order.id}) }}';">
                    <td>{{ order.id }} {% if order.gift %}
                            <img src="{{ asset('images/gift.svg') }}" alt="" height="16">
                        {% endif %} {% if order.risk %}
                            <img class="pl-3" src="{{ asset('images/ico-chimie3.svg') }}" title="{{ 'cart.risk_info' | trans }}" alt="" height="16">
                        {% endif %}
                    </td>
                    <td>{{ order.merchantName }}</td>
                    <td>{{ order.createdAt | localizeddate('short', 'none', locale) }}</td>

                    <td>
                        {% if order.supplierConfirmAt is not null %}{{ order.supplierConfirmAt | localizeddate('short', 'none', locale) }}{% endif %}
                    </td>

                    <td>{{ order.metaCart.id }}</td>
                    <td>
                        {% set shippingAddress = order.metaCart.buyerShippingAddress %}

                        {% if shippingAddress is not null %}
                            {{ shippingAddress.address }}
                            {% if shippingAddress.address2 is not null %}{{ shippingAddress.address2 }}{% endif %}
                            ,
                            {{ shippingAddress.zipcode }}
                            {{ shippingAddress.city }}
                            {% set country = 'country.'~shippingAddress.country.code %}
                            {{ country | trans }}
                        {% endif %}
                    </td>
                    <td>
                        {% if shippingAddress is not null %}
                            {% if shippingAddress.contact is not null %}{{ shippingAddress.contact }}{% endif %}
                        {% endif %}
                    </td>
                    <td class="bd-none talign-r min-w-90">{{ order.amountCurrencyDelivery | number_format(2, '.') }} {{ order.buyer.countryOfDelivery.currency }}</td>
                    {# Reorder #}
                    <td data-reorder="{{ path('front.order.reorder', {'merchantOrderId': order.id}) }}"  data-checkReOrder="{{ path('front.order.reorder.check', {'merchantOrderId': order.id}) }}">
                        <div class="tooltip-total tooltip-buy-again">
                            <img src="{{ asset('images/icone-panier-black.svg') }}">
                            <div class="tooltiptext">
                                {{ 'orders.list.link.buy_again' | trans }}
                            </div>
                        </div>
                    </td>
                </tr>
            {% endfor %}
            </tbody>
        </table>
    {% else %}
        <div class="orders-list-empty">
            <h6 style="text-align: center">{{ 'orders.empty.validated' | trans }}</h6>
        </div>
    {% endif %}
{% endblock %}