{% trans_default_domain 'AppBundle' %}

{% set suffix = (suffix is defined and suffix is not empty) ? suffix : 'desktop' %}
    <div class="treads-list-container">
        <table class="table threads-table treads-list"
               data-link="{{ path('thread.details', {'threadId': "threadId"}) }}">
            <thead>
            <tr>
                <th>{{ 'message.labels.id' | trans }}</th>
                <th>{{ 'message.labels.creation_date' | trans }}</th>
                <th>{{ 'message.labels.last_thread' | trans }}</th>
                <th>{{ 'message.labels.supplier_name' | trans }}</th>
                <th>{{ 'message.labels.subject' | trans }}</th>
                <th>{{ 'message.labels.threads_count' | trans }}</th>
            </tr>
            </thead>
            <tbody>
            {# Datatable ajax data #}
            </tbody>
        </table>
    </div>
    <div class="orders-list-empty" style="display: none;">
        <h6 style="text-align: center">{{ 'message.empty_list' | trans }}</h6>
    </div>