<?php

namespace Open\IdealBundle\Model;

class EmployeeType
{
    private ?string $Id = null;
    private ?string $Name_fr = null;

    /**
     * @return string|null
     */
    public function getId(): ?string
    {
        return $this->Id;
    }

    /**
     * @param string|null $Id
     * @return EmployeeType
     */
    public function setId(?string $Id): EmployeeType
    {
        $this->Id = $Id;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getNameFr(): ?string
    {
        return $this->Name_fr;
    }

    /**
     * @param string|null $Name_fr
     * @return EmployeeType
     */
    public function setNameFr(?string $Name_fr): EmployeeType
    {
        $this->Name_fr = $Name_fr;
        return $this;
    }


}