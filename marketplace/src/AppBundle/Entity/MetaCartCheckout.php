<?php

namespace AppBundle\Entity;

use Doctrine\ORM\Mapping as ORM;

/**
 * MetaCart
 *
 * @ORM\Table(name="meta_cart_checkout")
 * @ORM\Entity(repositoryClass="AppBundle\Repository\MetaCartCheckoutRepository")
 */
class MetaCartCheckout
{
    public const STATE_PENDING = 'pending';

    public const STATE_RUNNING = 'running';

    public const STATE_FINISHED = 'finished';

    public const STATE_FAILED = 'failed';

    /**
     * @var int
     *
     * @ORM\Column(name="id", type="integer")
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="AUTO")
     */
    private $id;

    /**
     * @var MetaCart
     *
     * @ORM\ManyToOne(targetEntity="AppBundle\Entity\MetaCart")
     */
    private $metaCart;

    /**
     * @var User
     *
     * @ORM\ManyToOne(targetEntity="AppBundle\Entity\User")
     */
    private $manager;

    /**
     * @var string
     *
     * @ORM\Column(name="state", type="string", length=20)
     */
    private $state;

    /**
     * @var \DateTimeImmutable
     *
     * @ORM\Column(type = "datetime", name="createdAt")
     */
    private $createdAt;

    /**
     * @var \DateTimeImmutable
     * @ORM\Column(type = "datetime", name="updatedAt")
     */
    private $updatedAt;

    public function __construct(MetaCart $metaCart, User $manager)
    {
        $this->metaCart = $metaCart;
        $this->createdAt = new \DateTimeImmutable();
        $this->updatedAt = $this->createdAt;
        $this->state = self::STATE_PENDING;
        $this->manager = $manager;
    }

    public function getMetaCart(): MetaCart
    {
        return $this->metaCart;
    }

    public function getState(): string
    {
        return $this->state;
    }

    public function setState(string $newState): self
    {
        $this->state = $newState;
        $this->updatedAt = new \DateTimeImmutable();
        return $this;
    }

    public function getManager(): User
    {
        return $this->manager;
    }

    public function getCreatedAt(): \DateTimeImmutable
    {
        return $this->createdAt;
    }

    public function getUpdatedAt(): \DateTimeImmutable
    {
        return $this->updatedAt;
    }
}
