<?php

namespace AppBundle\Entity;

use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use Doctrine\ORM\PersistentCollection;

/**
 * MetaCart
 *
 * @ORM\Table(name="meta_cart")
 * @ORM\Entity(repositoryClass="AppBundle\Repository\MetaCartRepository")
 */
class MetaCart
{
    // 1- the cart has been created: It is not assigned to any user
    public const STATUS_CREATED = "CREATED";

    // 2- the cart has been checkout: the process of order creation has started
    public const STATUS_ORDERING = "ORDERING";

    // 3- the cart has been assigned for validaton to an other user
    public const STATUS_ASSIGNED = "ASSIGNED";

    // 4A- the cart has been rejected: the assignation should be removed
    public const STATUS_REJECTED = "REJECTED";

    // 4B- The cart has been accepted and ordered by a buyer
    public const STATUS_ORDERED = "ORDERED";

    // 5- the cart has been flagged as empty
    public const STATUS_EMPTIED = "EMPTIED";

    // 6- the cart has been cancel
    public const STATUS_CANCELLED = "CANCELLED";

    // 7- The cancelRaison message is added to the metacart.
    public const STATUS_REASON = "Le changement de nom TOTAL en TotalEnergies impacte les commandes Click & Buy non validées avant le 28/05/21. Ces commandes seront annulées automatiquement. Vous les retrouverez dans Mes commandes > Annulées. Veuillez contacter le support si besoin.";


    /**
     * @var int
     *
     * @ORM\Column(name="id", type="integer")
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="AUTO")
     */
    private $id;

    /**
     * @var string
     *
     * @ORM\Column(name="country_of_delivery", type="string", length=20)
     */
    private $countryOfDelivery;

    /**
     * @var string
     *
     * @ORM\Column(name="currency", type="string", length=20)
     */
    private $currency;

    /**
     * One meta cart have multiple cart entity (one for each currency)
     * @var Collection
     *
     * @ORM\OneToMany(targetEntity="AppBundle\Entity\Cart", mappedBy="metaCart")
     */
    private Collection $carts;

    /**
     * One meta cart have multiple merchants
     * @var Collection
     *
     * @ORM\OneToMany(targetEntity="AppBundle\Entity\MetaCartMerchant", mappedBy="metaCart")
     */
    private Collection $merchants;

    /**
     * @var  User
     *
     * @ORM\ManyToOne(targetEntity="AppBundle\Entity\User")
     */
    private $buyer;

    /**
     * @var User
     *
     * @ORM\ManyToOne(targetEntity="AppBundle\Entity\User")
     */
    private $buyerManager;

    /**
     * @var string
     *
     * @ORM\Column(name="status", type="string", length=20)
     */
    private $status;

    /**
     * @var bool
     *
     * @ORM\Column(type="boolean", options={"default":1})
     */
    private $isCurrent;

    /**
     * @var int
     *
     * @ORM\Column(type="integer")
     */
    private $itemsCount;

    /**
     * @var string|null
     * @ORM\Column(name="reject_reason", type="text", nullable=true)
     */
    private $rejectReason;

    /**
     * @var string
     * @ORM\Column(name="cancel_reason", type="text", nullable=true)
     */
    private $cancelReason;

    /**
     * @var BuyerAddress
     * @ORM\ManyToOne(targetEntity="AppBundle\Entity\BuyerAddress", inversedBy="metaCart")
     */
    private $buyerShippingAddress;

    /**
     * @var BuyerAddress
     * @ORM\ManyToOne(targetEntity="AppBundle\Entity\BuyerAddress")
     */
    private $buyerBillingAddress;

    /**
     * One buyer have recipient linked to his address
     * @var ShippingRecipient
     *
     * @ORM\ManyToOne(targetEntity="ShippingRecipient")
     * @ORM\JoinColumn(name="recipient_id", referencedColumnName="id")
     */
    private $shippingRecipient;

    /**
     * @var \DateTimeImmutable
     *
     * @ORM\Column(type="datetime_immutable", name="order_created_at", nullable=true)
     */
    private $orderCreatedAt;

    /**
     * @var \DateTimeImmutable
     *
     * @ORM\Column(type="datetime_immutable", name="validated_at", nullable=true)
     */
    private $validatedAt;

    /**
     * @var float
     * @ORM\Column(name="amount", type="float", nullable=true)
     */
    private $amount;

    /**
     * @var User
     *
     * @ORM\ManyToOne(targetEntity="AppBundle\Entity\User")
     */
    private ?User $validatedBy;

    /**
     * @var Collection
     *
     * @ORM\OneToMany(targetEntity="AppBundle\Entity\MerchantOrder", mappedBy="metaCart")
     */
    private $merchantOrders;

    /**
     * @var bool|null
     *
     * @ORM\Column(name="gift", type="boolean", nullable=true, options={"default":null})
     */
    private $gift;

    /**
     * @var bool
     *
     * @ORM\Column(name="auto_validation", type="boolean", options={"default": false})
     */
    private $autoValidation = false;

    /**
     * @var bool|null
     *
     * @ORM\Column(name="risk", type="boolean", nullable=true, options={"default":null})
     */
    private $risk;

    public function __construct()
    {
        $this->carts = new ArrayCollection();
        $this->merchants = new ArrayCollection();
        $this->merchantOrders = new ArrayCollection();
        $this->gift = null; // gift default to null, the user needs to select yes or no manually
        $this->risk = null;
    }

    public function getId(): int
    {
        return $this->id;
    }

    public function setId(int $id): self
    {
        $this->id = $id;
        return $this;
    }

    public function getCarts(): PersistentCollection
    {
        return $this->carts;
    }

    public function addCart(Cart $cart): self
    {
        if(!$this->carts->contains($cart)) {
            $this->carts->add($cart);
        }
        return $this;
    }

    public function removeCart(Cart $cart): self
    {
        if($this->carts->contains($cart)) {
            $this->carts->removeElement($cart);
        }
        return $this;
    }

    public function getMerchants(): PersistentCollection
    {
        return $this->merchants;
    }

    public function addMerchant(MetaCartMerchant $merchant): self
    {
        if(!$this->merchants->contains($merchant)) {
            $this->merchants->add($merchant);
        }

        return $this;
    }

    public function removeMerchant(MetaCartMerchant $merchant): self
    {
        if($this->merchants->contains($merchant)) {
            $this->merchants->removeElement($merchant);
        }

        return $this;
    }

    public function getCountryOfDelivery(): string
    {
        return $this->countryOfDelivery;
    }

    public function setCountryOfDelivery(string $countryOfDelivery): self
    {
        $this->countryOfDelivery = $countryOfDelivery;
        return $this;
    }

    public function getCurrency(): string
    {
        return $this->currency;
    }

    public function setCurrency(string $currency): self
    {
        $this->currency = $currency;
        return $this;
    }

    public function getBuyer(): User
    {
        return $this->buyer;
    }

    public function setBuyer(User $buyer): self
    {
        $this->buyer = $buyer;
        return $this;
    }

    public function isCurrent(): bool
    {
        return $this->isCurrent;
    }

    public function setIsCurrent(bool $isCurrent): self
    {
        $this->isCurrent = $isCurrent;
        return $this;
    }

    public function getItemsCount(): int
    {
        return $this->itemsCount;
    }

    public function setItemsCount(int $itemsCount): self
    {
        $this->itemsCount = $itemsCount;
        return $this;
    }

    public function getBuyerManager(): ?User
    {
        return $this->buyerManager;
    }

    public function setBuyerManager(User $buyerManager): self
    {
        $this->buyerManager = $buyerManager;
        return $this;
    }

    public function getStatus(): string
    {
        return $this->status;
    }

    public function setStatus(string $status): self
    {
        $this->status = $status;
        return $this;
    }

    public function getRejectReason(): string
    {
            return $this->rejectReason ?? '';
    }

    public function setRejectReason(?string $rejectReason): self
    {
        $this->rejectReason = $rejectReason;
        return $this;
    }

    public function getBuyerShippingAddress(): ?BuyerAddress
    {
        return $this->buyerShippingAddress;
    }

    public function setBuyerShippingAddress(?BuyerAddress $buyerShippingAddress): self
    {
        $this->buyerShippingAddress = $buyerShippingAddress;
        return $this;
    }

    public function getBuyerBillingAddress(): ?BuyerAddress
    {
        return $this->buyerBillingAddress;
    }

    public function setBuyerBillingAddress(?BuyerAddress $buyerBillingAddress): self
    {
        $this->buyerBillingAddress = $buyerBillingAddress;
        return $this;
    }

    public function getOrderCreatedAt(): ?\DateTimeImmutable
    {
        return $this->orderCreatedAt;
    }

    public function setOrderCreatedAt(?\DateTimeImmutable $orderCreatedAt): self
    {
        $this->orderCreatedAt = $orderCreatedAt;
        return $this;
    }

    public function isOrdered(): bool
    {
        return ($this->status === self::STATUS_ORDERED);
    }

    public function isRejected(): bool
    {
        return ($this->status === self::STATUS_REJECTED);
    }

    public function getValidatedAt(): ?\DateTimeImmutable
    {
        return $this->validatedAt;
    }

    public function setValidatedAt(?\DateTimeImmutable $validatedAt): self
    {
        $this->validatedAt = $validatedAt;
        return $this;
    }

    public function getAmount(): float
    {
        return $this->amount;
    }

    public function setAmount(float $amount): self
    {
        $this->amount = $amount;
        return $this;
    }

    public function getValidatedBy(): ?User
    {
        return $this->validatedBy;
    }

    public function setValidatedBy(?User $validatedBy): self
    {
        $this->validatedBy = $validatedBy;
        return $this;
    }

    public function getMerchantOrders(): Collection
    {
        return $this->merchantOrders;
    }

    /**
     * @return string
     */
    public function getCancelReason(): string
    {
        return $this->cancelReason;
    }

    /**
     * @param string $cancelReason
     * @return MetaCart
     */
    public function setCancelReason(string $cancelReason): self
    {
        $this->cancelReason = $cancelReason;
        return $this;
    }

    /**
     * @return bool
     */
    public function isGift(): ?bool
    {
        return $this->gift;
    }

    /**
     * @param bool $gift
     * @return $this
     */
    public function setGift(?bool $gift): self
    {
        $this->gift = $gift;
        return $this;
    }

    /**
     * @return bool
     */
    public function isAutoValidation(): bool
    {
        return $this->autoValidation;
    }

    /**
     * @param bool $autoValidation
     * @return $this
     */
    public function setAutoValidation(bool $autoValidation): self
    {
        $this->autoValidation = $autoValidation;
        return $this;
    }

    /**
     * @return bool|null
     */
    public function isRisk(): ?bool
    {
        return $this->risk;
    }

    /**
     * @param bool|null $risk
     * @return MetaCart
     */
    public function setRisk(?bool $risk): MetaCart
    {
        $this->risk = $risk;
        return $this;
    }

}
