{% if pageCount > 1 %}
    <nav>
        <ul class="pagination{{ (align is not defined) ? '' : align=='center' ? ' justify-content-center' : (align=='right' ? ' justify-content-end' : '') }}">
            <div class="block-previous">
                {% if previous is defined %}
                    <li class="page-item previous-item">
                        <a class="page-link desktop-only" rel="prev" href="{{ path(route, query|merge({(pageParameterName): previous})) }}"><i class="arrow left"></i>{{ 'ticket.list.knp_previous'|trans({}, 'AppBundle') }}</a>
                        <a class="page-link mobile-only" rel="prev" href="{{ path(route, query|merge({(pageParameterName): previous})) }}"><i class="arrow left"></i></a>
                    </li>
                {% else %}
                    <li class="page-item disabled">
                        <a class="page-link mobile-only disabled" rel="prev"><i class="arrow left"></i></a>
                    </li>
                {% endif %}
            </div>
            <div class="pagination-mobile-view mobile-only">
                <div class="page-numbers active">{{ current }}</div>
                sur {{ pageCount }}
            </div>
            <div class="block-number desktop-only">
                {% if startPage > 1 %}
                    <li class="page-item">
                        <a class="page-link page-numbers" href="{{ path(route, query|merge({(pageParameterName): 1})) }}">1</a>
                    </li>
                    {% if startPage == 3 %}
                        <li class="page-item">
                            <a class="page-link page-numbers" href="{{ path(route, query|merge({(pageParameterName): 2})) }}">2</a>
                        </li>
                    {% elseif startPage != 2 %}
                        <li class="page-item disabled">
                            <span class="page-link page-numbers">&hellip;</span>
                        </li>
                    {% endif %}
                {% endif %}

                {% for page in pagesInRange %}
                    {% if page != current %}
                        <li class="page-item">
                            <a class="page-link page-numbers" href="{{ path(route, query|merge({(pageParameterName): page})) }}">{{ page }}</a>
                        </li>
                    {% else %}
                        <li class="page-item active">
                            <span class="page-link page-numbers">{{ page }}</span>
                        </li>
                    {% endif %}

                {% endfor %}

                {% if pageCount > endPage %}
                    {% if pageCount > (endPage + 1) %}
                        {% if pageCount > (endPage + 2) %}
                            <li class="page-item disabled">
                                <span class="page-link page-numbers">&hellip;</span>
                            </li>
                        {% else %}
                            <li class="page-item">
                                <a class="page-link page-numbers" href="{{ path(route, query|merge({(pageParameterName): (pageCount - 1)})) }}">{{ pageCount -1 }}</a>
                            </li>
                        {% endif %}
                    {% endif %}
                    <li class="page-item">
                        <a class="page-link page-numbers" href="{{ path(route, query|merge({(pageParameterName): pageCount})) }}">{{ pageCount }}</a>
                    </li>
                {% endif %}
            </div>

            <div class="block-next">
                {% if next is defined %}
                    <li class="page-item next-item">
                        <a class="page-link desktop-only" rel="next" href="{{ path(route, query|merge({(pageParameterName): next})) }}"> {{ 'ticket.list.knp_next'|trans({}, 'AppBundle') }}<i class="arrow right"></i></a>
                        <a class="page-link mobile-only" rel="next" href="{{ path(route, query|merge({(pageParameterName): next})) }}"><i class="arrow right"></i></a>
                    </li>
                {% else %}
                    <li  class="page-item disabled">
                        <a class="page-link mobile-only disabled" rel="next"><i class="arrow right"></i></a>
                    </li>
                {% endif %}
            </div>
        </ul>
    </nav>
{% endif %}