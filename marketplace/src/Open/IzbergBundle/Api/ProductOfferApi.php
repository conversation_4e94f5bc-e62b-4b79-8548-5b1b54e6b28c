<?php

namespace Open\IzbergBundle\Api;

use AppBundle\Model\DetailedPrice;
use AppBundle\Model\Merchant;
use AppBundle\Model\TechnicalDetails;
use AppBundle\Model\TechnicalProperty;
use Open\IzbergBundle\Api;
use Open\IzbergBundle\Dto\AttributeDTO;
use Open\IzbergBundle\Model\Offer;
use Open\IzbergBundle\Model\ProductOffer;
use Open\IzbergBundle\Model\Product;
use Unirest;

/**
 * Provide Wrapper for the Product and Product AlgoliaOffer API
 * Class ProductOfferApi
 * @package Open\IzbergBundle\Api
 */
class ProductOfferApi extends Api
{
    private const PRODUCT_OFFER_PATH = 'productoffer';

    public function  getUri(){
        return 'productoffer';
    }

    public function getItemClass(){
        return ProductOffer::class;
    }

    /**
     * @param int $productId
     * @param int $merchantId
     * @param string $name
     * @param float $price
     * @param string $language
     * @return ProductOffer
     */
    public function createProductOffer(int $productId, int $merchantId, string $name, float $price, $description,$defaultImage, $customAttributes, $language): ProductOffer
    {
        $data = [
            'product' => '/v1/product/'. $productId . '/',
            'merchant' => '/v1/merchant/' . $merchantId . '/',
            'name' => $name,
            'price' => $price,
            'description' => $description,
            'stock' => '1',
            'attributes' => $customAttributes,
            'default_image' => $defaultImage
        ];

        $response = $this->sendApiRequest(
            self::HTTP_POST_OPERATION,
            self::PRODUCT_OFFER_PATH . '/',
            $data,
            [
                Api::HEADER_ACCEPT_LANGUAGE => $language,
            ],
            [
                Api::OPTION_IS_OPERATOR => true,
            ]
        );

        return $this->serializer->deserialize($response->raw_body, ProductOffer::class, 'json');
    }


    /**
     * Get a product offer by its Id
     * @param $id
     * @param $language (without it custom attribute might be null)
     * @return
     */
    public function getProductOffer($id, $language)
    {
        $response = $this->sendApiRequest(
            self::HTTP_GET_OPERATION,
            self::PRODUCT_OFFER_PATH . '/' . intval($id) . '/',
            [],
            [self::HEADER_ACCEPT_LANGUAGE=>$language]
        );

        return json_decode($response->raw_body, true);
        //return $this->serializer->deserialize($response->raw_body, ProductOffer::class, 'json');
    }

    /**
     * delete a product offer by its Id
     * @param $id
     * @param bool $operator whether this is a operator (true) call or a user call (false)
     * @return
     */
    public function deleteProductOffer($id)
    {
        $response = $this->sendApiRequest(
            self::HTTP_DELETE_OPERATION,
            self::PRODUCT_OFFER_PATH . '/' . intval($id) . '/',
            null,
            [],
            [
                self::OPTION_NO_DATA_RETURN => true,
                self::OPTION_IS_OPERATOR => true
            ]
        );

    }

    /**
     * Get a product offer by its Id
     * @param $id
     * @param bool $operator whether this is a operator (true) call or a user call (false)
     * @return
     */
    public function getProductOfferOffer($id)
    {
        $response = $this->sendApiRequest(
            self::HTTP_GET_OPERATION,
            self::PRODUCT_OFFER_PATH . '/' . intval($id) . '/'
        );

        // return json_decode($response->raw_body);
        return $this->serializer->deserialize($response->raw_body, ProductOffer::class, 'json');
    }

    /**
     * Get product by Id
     * @param $id
     * @param bool $operator whether this is a operator (true) call or a user call (false)
     * @return Product
     */
    public function getProduct($id, $language = 'fr')
    {


        $response = $this->sendApiRequest(
            self::HTTP_GET_OPERATION,
            'product/' . intval($id) . '/',
            null,
            [
                Api::HEADER_ACCEPT_LANGUAGE => $language,
            ],
        );


        return $this->serializer->deserialize($response->raw_body, Product::class, 'json');
    }

    /**
     * Get all product offers for the given category
     * @param $category_id
     * @return mixed the ProductOffer object
     */
    public function getProducOffersByCategory($category_id)
    {
        $response = $this->sendApiRequest(
            self::HTTP_GET_OPERATION,
            'product/?where=application_categories=' . intval($category_id)
        );

        return $this->serializer->deserialize($response->raw_body, ProductOffer::class, 'json');
    }


    /**
     * @param $id
     * @param $data
     */
    public function patchProductOffer($id, string $language, $data)
    {

        $this->sendApiRequest(
            self::HTTP_PATCH_OPERATION,
            self::PRODUCT_OFFER_PATH . '/' . intval($id) . '/',
            $data,
            ["Accept-Language"=>$language],
            [
                self::OPTION_NO_DATA_RETURN => true
            ]
        );

    }

    /**
     * @param $id
     * @param $imageId
     */
    public function assignImage($id, $imageId)
    {
        $this->sendApiRequest(
            self::HTTP_POST_OPERATION,
            self::PRODUCT_OFFER_PATH . '/' . intval($id) . '/assign_image/',
            ["image_id"=>$imageId, "order"=>0],
            [],
            [
                self::OPTION_NO_DATA_RETURN => true

            ]
        );
    }

    /**
     * Set product Departement
     * @param $id
     * @param string $language
     * @param $departement
     */
    public function setProductDepartement($id, string $language, $departement)
    {
        $this->patchProductOffer($id, $language, array("attributes" => array("departement-depart" => $departement)));
    }


    /**
     * Deactivate a product OFFER via izberg API
     */
    public function deactivateProduct($id)
    {
        $this->sendApiRequest(
            self::HTTP_POST_OPERATION,
            self::PRODUCT_OFFER_PATH . '/' . intval($id) . '/deactivate/',
            null,
            [],
            [
                self::OPTION_NO_DATA_RETURN => true,
            ]
        );

        return true;
    }

    /**
     * activate a product OFFER via izberg API
     * @param $id
     * @return bool
     */
    public function activateProduct($id)
    {
        $this->sendApiRequest(
            self::HTTP_POST_OPERATION,
            self::PRODUCT_OFFER_PATH . '/' . intval($id) . '/activate/',
            null,
            [],
            [
                self::OPTION_NO_DATA_RETURN => true,
            ]
        );

        return true;
    }

    public function getImageId($productOffer) {
       $images =  $productOffer['assigned_images'];
       if($images && is_array($images) && count($images)>0){
            return $images[0]['id'];
       }
    }


}

