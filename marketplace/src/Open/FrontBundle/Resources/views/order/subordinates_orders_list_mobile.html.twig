{% extends '@OpenFront/base.html.twig' %}
{% trans_default_domain 'AppBundle' %}
{% set defaultParams =  app.request.query.all() | merge({'page':pagination.current_page}) %}

{% block title %}
    {{ 'cart.detail' | trans }}
{% endblock %}

{% block body %}
    {% block searchbar %}
        {% if formSearchBar is defined %}
            {% include('@OpenFront/component/search-bar.html.twig') %}
        {% else %}
            {{ render(controller("Open\\FrontBundle\\Controller\\SearchBarController::formAction")) }}
        {% endif %}
    {% endblock %}

    <div class="Page-inner">
        <div class="Page-content">
            <div style="text-align:center">
                <section class="previous-page-img">
                    <img alt="go back" src="{{ asset('images/red-arrow.svg') }}">
                    <img alt="go back" src="{{ asset('images/red-arrow.svg') }}">
                    <a class="previous-page" href="{{ path('front.ordersToValidate.categories') }}">
                        {{ 'orders.list.back' | trans | upper }}
                    </a>
                </section>
                <section class="account-header">{{ ('orders.status.'~status) | trans | upper }}</section>
            </div>

            <div class="orders-types-list ">
                {% if metaCarts|length > 0 %}
                    {% include('@OpenFront/order/sub_orders_list_mobile_partial.html.twig') %}
                {% else %}
                    <section class="order-summary">
                        {{ 'orders.list.tab.empty' | trans  }}
                    </section>
                {% endif %}
            </div>
        </div>

    </div>
{% endblock %}
{% block javascripts %}

    <script type="text/javascript">

        var currentPage = {{ page }};
        var nbPages = {{ nbPages }};
        var pSize = {{ pageSize }};

        document.addEventListener('DOMContentLoaded', function() {
            displayShowMore();
        });

        var ShowMoreSubOrders = function() {
            currentPage = currentPage + 1;
            {% set params = defaultParams | merge({'status': status, 'page':'_page_'}) %}
            loadingModal = window.UI.Modal.showLoading();
            var url = '{{ path("front.ordersToValidateList.status", params)|raw }}';
            url = url.replace("_page_", currentPage);
            $.ajax({
                url: url,
                success: function(htmlResult) {
                    $('.show-more-container').remove();
                    $('.orders-types-list').append(htmlResult);
                    displayShowMore();
                },
                complete: function() {
                    closeLoading();
                }
            });
        }

        var closeLoading = function() {
            if(loadingModal !== null) {
                loadingModal.close();
            }
        };

        let displayShowMore = function() {
            let totalOrders = '{{ totalOrders }}';
            let totalOrdersDisplayed = $('.orders-types-list').children().length;
            if((parseInt(totalOrdersDisplayed) - 1) >= totalOrders) {
                $('.show-more-container').remove();
            }
        }

    </script>

{% endblock %}
