<?php

namespace AppBundle\Entity;

use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Validator\Constraints as Assert;

/**
 * Cart
 *
 * @ORM\Table(name="cart")
 * @ORM\Entity(repositoryClass="AppBundle\Repository\CartRepository")
 * @ORM\HasLifecycleCallbacks()
 *
 */
class Cart
{
    //the cart has been assigned to an other user
    public const STATUS_ASSIGN = "ASSIGN";

    //the cart has been created: It is not assigned to any user
    public const STATUS_CREATE = "CREATE";

    //the cart has been rejected: it has been reassigned to the cart creator
    public const STATUS_REJECTED = "REJECTED";

    //The cart has been accepted and ordered by a buyer
    public const STATUS_ORDER = "ORDER";

    //All the process for this cart is DONE => this cart can't be updated
    public const STATUS_DONE = "DONE";

    //the cart should not appear anymore in the application
    public const STATUS_REMOVED = "REMOVED";

    use TimestampedTrait;
    use TechnicalIdentifierTrait;

    /**
     * @var int
     *
     * @ORM\Id
     * @ORM\Column(name="id", type="integer")
     */
    private $id;

    /**
     * One cart have ont user that created it
     * @ORM\ManyToOne(targetEntity="User")
     * @ORM\JoinColumn(name="created_user_id", referencedColumnName="id", nullable=true)
     *
     * @var User
     */
    private $createdUser;

    /**
     * One cart have a current user that is assign to.
     * @ORM\ManyToOne(targetEntity="User")
     * @ORM\JoinColumn(name="current_user_id", referencedColumnName="id", nullable=true)
     *
     * @var User
     */
    private $currentUser;

    /**
     * One cart have a site that is assign to.
     * @ORM\ManyToOne(targetEntity="Site")
     * @ORM\JoinColumn(name="site_id", referencedColumnName="id", nullable=true)
     */
    private $site;

    /**
     * @ORM\Column(name="status", type="string", length=20, nullable=false)
     * @Assert\Length(max=20)
     */
    private $status;

    /**
     * @ORM\Column(name="payment_mode", type="string", length=50, nullable=true)
     */
    private $paymentMode;

    /**
     * @ORM\Column(name="payment_term", type="string", length=50, nullable=true)
     */
    private $paymentTerm;

    /**
     * @ORM\Column(name="payment_method", type="string", length=50, nullable=true)
     */
    private $paymentMethod;

    /**
     * @ORM\Column(name="payment_type", type="string", length=50, nullable=true)
     */
    private $paymentType;

    /**
     * @ORM\Column(name="payment_term_izberg_id", type="string", length=50, nullable=true)
     */
    private $paymentTermIzbergId;

    /**
     * One cart have a shipping address.
     * @ORM\ManyToOne(targetEntity="AppBundle\Entity\Address")
     * @ORM\JoinColumn(name="address_id", referencedColumnName="id", nullable=true)
     */
    private $address;

    /**
     * @ORM\ManyToOne(targetEntity="AppBundle\Entity\Address")
     * @ORM\JoinColumn(name="billing_address_id", referencedColumnName="id", nullable=true)
     */
    private $billingAddress;

    /**
     * @ORM\Column(name="order_id", type="integer", nullable=true)
     */
    private $orderId;


    /**
     * @ORM\Column(name="wps_transaction_id", type="string", length=50, nullable=true)
     */
    private $wpsTransactionId;

    /**
     * @ORM\Column(name="wps_reconciliation_key", type="string", nullable=true)
     */
    private $wpsReconciliationKey;

    /**
     * @ORM\Column(name="validation_number", type="string", nullable=true)
     */
    private $validationNumber;

    /**
     * @var string
     *
     * @ORM\Column(name="cart_currency", type="string", length=20)
     */
    private $cartCurrency;

    /**
     * @var MetaCart
     *
     * @ORM\ManyToOne(targetEntity="AppBundle\Entity\MetaCart", inversedBy="carts")
     * @ORM\JoinColumn(name="meta_cart_id", referencedColumnName="id")
     */
    private $metaCart;

    /**
     * @var int
     *
     * @ORM\Column(type="integer")
     */
    private $itemsCount;

    /**
     * Cart constructor.
     */
    public function __construct()
    {

    }

    /**
     * @return int
     */
    public function getCartId(): int
    {
        return $this->id;
    }

    /**
     * @param int $id
     */
    public function setCartId(int $id): void
    {
        $this->id = $id;
    }

    public function getCreatedUser(): User
    {
        return $this->createdUser;
    }

    public function setCreatedUser(User $createdUser): void
    {
        $this->createdUser = $createdUser;
    }

    public function getCurrentUser(): User
    {
        return $this->currentUser;
    }

    /**
     * @param User $currentUser
     */
    public function setCurrentUser(User $currentUser): void
    {
        $this->currentUser = $currentUser;
    }

    /**
     * @return mixed
     */
    public function getStatus()
    {
        return $this->status;
    }

    /**
     * @param mixed $status
     */
    public function setStatus($status): void
    {
        $this->status = $status;
    }

    /**
     * @return Site
     */
    public function getSite(): ?Site
    {
        return $this->site;
    }

    /**
     * @param Site $site
     */
    public function setSite(?Site $site): void
    {
        $this->site = $site;
    }

    /**
     * @return int
     */
    public function getId(): int
    {
        return $this->id;
    }

    /**
     * @param int $id
     */
    public function setId(int $id): void
    {
        $this->id = $id;
    }

    /**
     * @return mixed
     */
    public function getAddress()
    {
        return $this->address;
    }

    /**
     * @param mixed $address
     */
    public function setAddress($address): void
    {
        $this->address = $address;
    }

    public function getBillingAddress()
    {
        return $this->billingAddress;
    }

    public function setBillingAddress($billingAddress): void
    {
        $this->billingAddress = $billingAddress;
    }

    /**
     * @return mixed
     */
    public function getOrderId()
    {
        return $this->orderId;
    }

    /**
     * @param mixed $orderId
     */
    public function setOrderId($orderId): void
    {
        $this->orderId = $orderId;
    }

    /**
     * @return mixed
     */
    public function getWpsTransactionId()
    {
        return $this->wpsTransactionId;
    }

    /**
     * @param mixed $wpsTransactionId
     */
    public function setWpsTransactionId($wpsTransactionId): void
    {
        $this->wpsTransactionId = $wpsTransactionId;
    }

    public function getPaymentMode()
    {
        return $this->paymentMode;
    }

    public function setPaymentMode($paymentMode): void
    {
        $this->paymentMode = $paymentMode;
    }

    public function getPaymentTerm()
    {
        return $this->paymentTerm;
    }

    public function setPaymentTerm($paymentTerm): void
    {
        $this->paymentTerm = $paymentTerm;
    }

    public function getPaymentMethod()
    {
        return $this->paymentMethod;
    }

    public function setPaymentMethod($paymentMethod): void
    {
        $this->paymentMethod = $paymentMethod;
    }

    public function getPaymentType()
    {
        return $this->paymentType;
    }

    public function setPaymentType($paymentType): void
    {
        $this->paymentType = $paymentType;
    }

    public function getPaymentTermIzbergId()
    {
        return $this->paymentTermIzbergId;
    }

    public function setPaymentTermIzbergId($paymentTermIzbergId): void
    {
        $this->paymentTermIzbergId = $paymentTermIzbergId;
    }

    /**
     * @return mixed
     */
    public function getWpsReconciliationKey()
    {
        return $this->wpsReconciliationKey;
    }

    /**
     * @param mixed $wpsReconciliationKey
     */
    public function setWpsReconciliationKey($wpsReconciliationKey): void
    {
        $this->wpsReconciliationKey = $wpsReconciliationKey;
    }

    /**
     * @return mixed
     */
    public function getValidationNumber()
    {
        return $this->validationNumber;
    }

    /**
     * @param mixed $validationNumber
     */
    public function setValidationNumber($validationNumber)
    {
        $this->validationNumber = $validationNumber;
    }

    public function setCartCurrency($cartCurrency): self
    {
        $this->cartCurrency = $cartCurrency;

        return $this;
    }

    public function getCartCurrency(): string
    {
        return $this->cartCurrency;
    }

    public function getMetaCart(): MetaCart
    {
        return $this->metaCart;
    }

    public function setMetaCart(MetaCart $metaCart): self
    {
        $this->metaCart = $metaCart;
        return $this;
    }

    public function getItemsCount(): int
    {
        return $this->itemsCount;
    }

    public function setItemsCount(int $itemsCount): self
    {
        $this->itemsCount = $itemsCount;
        return $this;
    }
}
