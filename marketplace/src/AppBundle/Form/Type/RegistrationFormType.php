<?php

namespace AppBundle\Form\Type;

use AppBundle\Entity\Country;
use AppBundle\Entity\User;
use AppBundle\Services\CompanyService;
use AppBundle\Services\CountryService;
use AppBundle\Services\CurrencyService;
use AppBundle\Services\LanguageService;
use AppBundle\Services\MarketPlaceService;
use AppBundle\Validator\Constraints\CompanyIdentification;
use AppBundle\Validator\Constraints\ReCaptcha;
use Ddeboer\Vatin\Validator;
use Doctrine\ORM\EntityRepository;
use Exception;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\EmailType;
use Symfony\Component\Form\Extension\Core\Type\HiddenType;
use Symfony\Component\Form\Extension\Core\Type\PasswordType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\Form\FormError;
use Symfony\Component\Form\FormEvent;
use Symfony\Component\Form\FormEvents;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\HttpFoundation\Session\Session;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Validator\Constraints\Callback;
use Symfony\Component\Validator\Constraints\Length;
use Symfony\Component\Validator\Constraints\NotBlank;
use Symfony\Component\Validator\ConstraintViolationListInterface;
use Symfony\Component\Validator\Validator\ValidatorInterface;
use Symfony\Contracts\Translation\TranslatorInterface;

class RegistrationFormType extends AbstractType
{
    private TranslatorInterface $translator;
    private RequestStack $requestStack;
    private CompanyService $companyService;
    private CountryService $countryService;
    private LanguageService $languageService;
    private CurrencyService $currencyService;
    private MarketPlaceService $marketPlaceService;
    private array $merchantLanguage;
    private string $captchaSecret;
    private bool $captchaEnabled;
    private Validator $vatValidator;
		private ValidatorInterface $validator;

    public function __construct(
        TranslatorInterface $translator,
        RequestStack        $requestStack,
        CompanyService      $companyService,
        CountryService      $countryService,
        LanguageService     $languageService,
        CurrencyService     $currencyService,
        MarketPlaceService  $marketPlaceService,
        Validator           $vatValidator,
        ValidatorInterface  $validator,
        array               $merchantLanguage,
        string              $captchaSecret,
        bool                $captchaEnabled,
    ) {
        $this->translator = $translator;
        $this->requestStack = $requestStack;
        $this->companyService = $companyService;
        $this->countryService = $countryService;
        $this->languageService = $languageService;
        $this->currencyService = $currencyService;
        $this->marketPlaceService = $marketPlaceService;
        $this->merchantLanguage = $merchantLanguage;
        $this->captchaSecret = $captchaSecret;
        $this->captchaEnabled = $captchaEnabled;
        $this->vatValidator = $vatValidator;
	      $this->validator = $validator;
    }


    public function buildForm(FormBuilderInterface $builder, array $options)
    {
        $defaultMkp = $this->marketPlaceService->getMarketPlaceById($options['marketplace']);

        // callback de validation du champs 'identification'
        $identificationCallback = function ($object, $context) use ($options) {

            // On ne fait cette vérif que pour les buyers
            if (array_key_exists('merchant', $options) && !$options['merchant']) {
                $formValues = $this->requestStack->getCurrentRequest()->request->get('registration_form');
                $country = is_array($formValues)?$formValues['country']:null;
                $identification = is_array($formValues)?$formValues['identification']:null;

                if ($this->companyService->company_identification_already_exists($country, $identification)) {
                    $msg = $this->translator->trans('registration.error.identification_already_used', [], 'AppBundle');
                    $context->addViolation($msg, [], null);
                }

                //also add a check of the vat number if user is in UE
                try {
                    $buyerCountryObj = $this->countryService->getCountryById($country);
                    if ($buyerCountryObj->isInEU() && !$this->vatValidator->isValid($identification, true)) {
                        $context->addViolation($this->translator->trans('form.company.ident_number.unknown', [], 'validators'), [], null);
                    }
                } catch (Exception $e) {
                    //do nothing, we can't validate
                }
            }
        };

        $builder
            ->remove('username')
            ->remove('plainPassword')
            ->remove('email');

        $builder
            ->add('categories', ChoiceType::class, [
                'translation_domain' => 'AppBundle',
                'label' => 'user.form.activities',
                'multiple' => true,
                'mapped' => false,
                'expanded' => true,
                'choices' => $this->buildCategoriesChoices()
            ])
            ->add('marketplace', ChoiceType::class, [
                'translation_domain' => 'AppBundle',
                'mapped' => false,
                'choice_translation_domain' => 'AppBundle',
                'label' => 'address.form.marketplace',
                'constraints' => new NotBlank(),
                'attr' => [
                    'required' => 'required' // needed because mapped = false
                ],
                'choices' => $this->buildMarketplaceChoices(),
                'data' => $defaultMkp->getId()
            ])
            ->add('country', EntityType::class, [
                'translation_domain' => 'AppBundle',
                'class' => Country::class,
                'label' => 'address.form.country',
                'placeholder' => 'address.form.country_placeholder',
                'mapped' => false,
                'constraints' => new NotBlank(),
                'attr' => [
                    'required' => 'required' // needed because mapped = false
                ],
                'query_builder' => function (EntityRepository $er) {
                    // Show only vendor country
                    $where = 'c.vendor = 1';
                    // Show only enabled countries
                    $where .= " and c.enabled = 1 ";

                    return $er
                        ->createQueryBuilder('c')
                        ->where($where)
                        ->orderBy('c.code', 'ASC');
                },
                'choice_translation_domain' => 'AppBundle',
                'choice_attr' => function ($country) {
                    $label = $this->translator->trans('country.ident.' . $country->getCode(), [], 'AppBundle');
                    $help = $this->translator->trans('country.ident_helper.' . $country->getCode(), [], 'AppBundle');

                    return [
                        'data-regexp' => $country->getCompanyIdentRegex(),
                        'data-country-code' => $country->getCode(),
                        'data-label' => $label,
                        'data-help' => $help,
                    ];
                }
            ])
            ->add('language', ChoiceType::class, [
                'label' => 'buyer.registration.language.title',
                'placeholder' => 'buyer.registration.language.placeholder',
                'mapped' => false,
                'choice_translation_domain' => null,
                'choices' => $this->getMerchantLanguage(),
                'constraints' => [
                    new NotBlank()
                ]
            ]);

        if (!array_key_exists('merchant', $options) || !$options['merchant']) {
            $builder->add('locale', ChoiceType::class, [
                'label' => 'back.user.form.language',
                'placeholder' => false,
                'attr' => [
                    'required' => 'required',
                ],
                'constraints' => [
                    new NotBlank()
                ],
                'data' => 'en',
                'choice_translation_domain' => 'AppBundle',
                'choices' => $this->languageService->getLanguageCodes(),
                'choice_label' => function ($choiceValue, $key, $value) {
                    return 'node.form.lang.' . $value;
                }
            ]);
        }

        $builder
            ->add('raisonSociale', null, [
                'label' => 'buyer.registration.raison_sociale',
                'mapped' => false,
                'attr' => [
                    'required' => 'required', // needed because mapped = false
                ],
                'constraints' => [
                    new NotBlank(),
                    new Length([
                        'max' => 35
                    ])
                ]
            ])
            ->add('identification', null, [
                'label' => 'buyer.registration.identification',
                'mapped' => false,
                'attr' => [
                    'required' => 'required', // needed because mapped = false
                ],
                'constraints' => [
                    new CompanyIdentification(),
                    new NotBlank(),
                    new Callback(['callback' => $identificationCallback]),
                ]
            ])
            ->add('lastname', null, [
                'label' => 'buyer.registration.lastname',
                'constraints' => [
                    new NotBlank(),
                    new Length([
                        'max' => 50
                    ])
                ]
            ])
            ->add('firstname', null, [
                'label' => 'buyer.registration.firstname',
                'constraints' => [
                    new NotBlank(),
                    new Length([
                        'max' => 50
                    ])
                ]
            ])
            ->add('email', EmailType::class, [
                'label' => 'buyer.registration.email',
            ])
            ->add('mainPhoneNumber', PhoneType::class, [
                'label' => 'buyer.registration.main_phone',
                'attr' => [
                    'pattern' => '^[\+]?[0-9]+$',
                ],
                'constraints' => [
                    new NotBlank(),
                ]
            ])
            ->add('plainPassword', PasswordType::class, [
                'label' => 'buyer.registration.password',
            ])
            ->add('cgu', ChoiceType::class, [
                'label' => '',
                'mapped' => false,
                'multiple' => true,
                'expanded' => true,
                'required' => true,
                'attr' => [
                    'required' => 'required',
                ],
                'choice_translation_domain' => 'AppBundle',
                'choices' => [
                    '' => 'cgu_accepted',
                ],
                'constraints' => [
                    new NotBlank()
                ]
            ])
            ->add('function', TextType::class, [
                'label' => 'buyer.registration.function'
            ]);

        if (array_key_exists('merchant', $options) && $options['merchant']) {
            $builder->add('currency', ChoiceType::class, [
                'label' => 'buyer.registration.currency.title',
                'placeholder' => 'buyer.registration.currency.placeholder',
                'choice_translation_domain' => null,
                'choices' => $this->currencyService->getCurrencyCodes(),
                'constraints' => [
                    new NotBlank()
                ]
            ]);
        }

        if ($this->captchaEnabled) {
            $builder->add('hiddenRecaptcha', HiddenType::class, [
                'label' => false,
                'mapped' => false,
                'constraints' => [
                    new NotBlank(),
                    new ReCaptcha([
                        'captcha_secret' => $this->captchaSecret
                    ]),
                ],
		            'attr' => [
			            'data-sitekey' => $options['recaptcha_site_key']
		            ]
            ]);

		        $builder->addEventListener(FormEvents::POST_SUBMIT, function (FormEvent $event) {
				        $form = $event->getForm();
				        $recaptchaResponse = $form->get('hiddenRecaptcha')->getData();

				        $constraint = new ReCaptcha(['captcha_secret' => $this->captchaSecret]);

				        $violations = $this->validator->validate($recaptchaResponse, [
					        new NotBlank(),
					        $constraint,
				        ]);

				        if (count($violations) > 0) {

                            /** @var Session $session*/
					        $session = $this->requestStack->getSession();

                            /** @var ConstraintViolationListInterface $violations */
					        foreach ($violations as $violation) {
						        $form->addError(new FormError((string)$violation->getMessage()));
						        $session->getFlashBag()->add('error', $violation->getMessage());
					        }
				        }
		        });
        }
    }

    public function configureOptions(OptionsResolver $resolver)
    {
        $resolver->setDefaults([
            'translation_domain' => 'messages',
            'csrf_token_id' => 'registration',
            'data_class' => User::class,
            'marketplace' => false,
            'merchant' => false,
	          'recaptcha_site_key' => null,
        ]);
    }

    private function buildCategoriesChoices(): array
    {
        return [
            'category.office_supplies' => $this->translator->trans('category.office_supplies', [], 'AppBundle', 'en'),
            'category.hardware' => $this->translator->trans('category.hardware', [], 'AppBundle', 'en'),
            'category.catering' => $this->translator->trans('category.catering', [], 'AppBundle', 'en'),
            'category.event' => $this->translator->trans('category.event', [], 'AppBundle', 'en'),
            'category.intellectual_benefits' => $this->translator->trans('category.intellectual_benefits', [], 'AppBundle', 'en'),
            'category.communication_marketing' => $this->translator->trans('category.communication_marketing', [], 'AppBundle', 'en'),
            'category.epi' => $this->translator->trans('category.epi', [], 'AppBundle', 'en'),
            'category.lab_consumable' => $this->translator->trans('category.lab_consumable', [], 'AppBundle', 'en'),
            'category.industrial_supply' => $this->translator->trans('category.industrial_supply', [], 'AppBundle', 'en'),
            'category.flower_services' => $this->translator->trans('category.flower_services', [], 'AppBundle', 'en'),
            'category.promotional_items' => $this->translator->trans('category.promotional_items', [], 'AppBundle', 'en'),
            'category.other' => $this->translator->trans('category.other', [], 'AppBundle', 'en'),
        ];
    }

    private function buildMarketplaceChoices(): array
    {
        $marketplaces = $this->marketPlaceService->getAllMarketPlace();
        $marketplaceChoices = [];
        foreach ($marketplaces as $mkp) {
            $marketplaceChoices[sprintf('marketplace.%s', $mkp->getName())] = $mkp->getId();
        }

        return $marketplaceChoices;
    }

    private function getMerchantLanguage(): array
    {
        $languages = [];
        foreach ($this->merchantLanguage as $language) {
            $languages['buyer.registration.language.' . strtolower($language)] = $language;

        }
        return $languages;
    }
}
