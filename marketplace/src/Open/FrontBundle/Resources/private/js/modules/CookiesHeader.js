let Cookies = require('js-cookie');

(function () {
    'use strict';

    let header = null;
    let $w = null;

    /**
     * Set the cookie
     * @private
     */
    let _setCookie = function () {
        Cookies.set('cgu_accepted', true, { expires: 365 });
    };

    /**
     * Close header and set the cookie
     * @private
     */
    let _closeHeader = function() {
        header.style.display = 'none';
        _setCookie();
    };

    /**
     * OnScroll handler
     * @private
     */
    let _checkScroll = function () {
        // If user scrolled a bit then stop listening and hide the arrow
        if ($w.scrollTop() > 100) {
            $w.off('scroll', _checkScroll);
            _closeHeader();
        }
    };

    /**
     * Initialize
     */
    let init = function () {
        let c = Cookies.get('cgu_accepted');
        header = document.getElementById('js-cookies-header');
        $w = $(window);

        // Show bar if cookie not found
        if (c === undefined) {
            header.classList.add('is-visible');

            document.getElementById('js-close-cookies-header').addEventListener('click', _closeHeader);

            $w.on('scroll', _checkScroll);

            setTimeout(15000, _setCookie);

            // otherwise hide the bar totaly so it doesnt block clicks
        } else {
            header.style.display = 'none';
        }

    };


    module.exports = {
        init: init,
    };

})();