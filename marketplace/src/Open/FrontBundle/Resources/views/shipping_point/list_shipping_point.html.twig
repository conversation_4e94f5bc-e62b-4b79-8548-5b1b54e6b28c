{% extends '@OpenFront/base.html.twig' %}
{% trans_default_domain 'AppBundle' %}

{% block title %}{{ 'shipping_point.list.title' | trans }}{% endblock %}

{% block meta_description %}{{ 'home.description'|trans }}{% endblock %}

{% block body %}

    {% block searchbar %}
        {% if formSearchBar is defined %}
            {% include('@OpenFront/component/search-bar.html.twig') %}
        {% else %}
            {{ render(controller("Open\\FrontBundle\\Controller\\SearchBarController::formAction")) }}
        {% endif %}
    {% endblock %}

    <div class="Page-inner">
    <div class="Page-content">
        <div style="text-align:center">
            <section class="shipping-header shipping-title">{{ 'shipping_point.list.title' | trans }}</section>
        </div>
        {% if addresses is not empty%}
            <div class="shipping-header-intro">
                <section>{{ 'shipping_point.list.intro1' | trans }}</section>
            </div>
            <div class="shipping-new-address-link">
                <a href="{{ path ('new.shipping.address') }}" >{{ 'shipping_point.list.add_new_address' | trans }}</a>
            </div>

            <table class="table orders-table desktop-only">
                <thead>
                <tr>
                    <th>{{ ('shipping_point.detail.name' | trans) }}</th>
                    <th>{{ ('shipping_point.detail.address' | trans) }}</th>
                    <th>{{ ('shipping_point.detail.zip_code' | trans) }}</th>
                    <th>{{ ('shipping_point.detail.city' | trans) }}</th>
                    <th>{{ ('shipping_point.detail.country' | trans) }}</th>

                    <th>{{ ('shipping_point.detail.contact' | trans) }}</th>
                    <th>{{ ('shipping_point.detail.phone' | trans) }}</th>
                    <th class="no-right-bordered">{{ ('shipping_point.detail.comments' | trans) }}</th>
                    <th></th>
                </tr>
                </thead>
                <tbody>
                {% for personalAddress in addresses %}
                    {% include('@OpenFront/shipping_point/list_shipping_point_row.html.twig') %}
                {% endfor %}
                </tbody>
            </table>
        {% else %}
            <span>{{ 'cart.shipping.empty_list' | trans }}</span><br><br>
            <span style="text-align: right" class="shipping-new-address-link">
                <a href="{{ path ('new.shipping.address') }}" >{{ 'shipping_point.list.add_new_address' | trans }}</a>
            </span>
            <p class="my-4">{{ 'cart.shipping.sentence_empty_list' | trans }}</p><br><br>
        {% endif %}
        <div class="shipping-outro">
            <section>{{ 'shipping_point.list.outro1' | trans }}</section>
        </div>
    </div>
    </div>
{% endblock %}
