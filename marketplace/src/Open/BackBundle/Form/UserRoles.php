<?php


namespace Open\BackBundle\Form;


use AppBundle\Entity\User;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\HiddenType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

class UserRoles extends AbstractType
{
    private const USER_ROLES = [
        'user.roles.choices.buyer' => User::ROLE_BUYER,
        'user.roles.choices.manager' => User::ROLE_MANAGER,
        'user.roles.choices.entityReporting' => User::ROLE_ENTITY_REPORTING,
        'user.roles.choices.marketplaceReporting' => User::ROLE_MARKETPLACE_REPORTING
    ];

    public function buildForm(FormBuilderInterface $builder, array $options)
    {
        $currentUserRoles = key_exists('userRoles', $options['data']) ? $options['data']['userRoles'] : [];
        $roles = [];
        foreach (self::USER_ROLES as $key => $value) {
            if(in_array($value, $currentUserRoles)) {
                $roles[] = [$key => $value];
            }
        }

        $builder
            ->add('userRoles', ChoiceType::class, [
                'attr' => [
                    'class' => 'custom-select'
                ],
                'label' => 'user.roles.popup.label',
                'multiple' => true,
                'choices' => self::USER_ROLES,
                'choice_attr' => $roles
            ])
        ;
    }

    public function configureOptions(OptionsResolver $resolver)
    {
        $resolver->setDefaults([
            'translation_domain' => 'AppBundle',
        ]);
    }

}
