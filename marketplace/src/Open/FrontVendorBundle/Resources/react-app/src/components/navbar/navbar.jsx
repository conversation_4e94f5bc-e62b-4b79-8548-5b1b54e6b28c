import React, {Component} from "react";
import AppBar from "@material-ui/core/AppBar";
import Toolbar from "@material-ui/core/Toolbar";
import Dropdown from "@izberg/izberg-ui-beta/Dropdown";
import {Button} from "@material-ui/core";
import {Trans} from "react-i18next";
import Menu from "../../config/menu";
import AccountCircleIcon from "@material-ui/icons/AccountCircle";
import {Link} from "react-router-dom";
import * as logo from "../../assets/img/logo.png";

import "./navbar.css";

export class Navbar extends Component {
    state = {
        anchorEl: null,
        menu: null
    };

    openMenu(e, menu) {
        this.setState({
            anchorEl: e.currentTarget,
            menu: menu
        });
    }

    render() {
        const {anchorEl, menu} = this.state;
        return (
            <AppBar position="sticky" color="inherit">
                <Toolbar style={{padding: 0}}>
                    <div
                        style={{display: "flex", flex: 1, alignItems: "stretch"}}
                        className="navbar"
                    >
                        <Link
                            to="/front/supplier"
                            style={{display: "flex", marginRight: 80, padding: "18px 15px"}}
                        >
                            <img src={logo.default} style={{height: 34}} alt="Total"/>
                        </Link>
                        <div style={{display: "flex", flex: 1}}>
                            <Button onClick={e => this.openMenu(e, Menu.home)}>
                                <Trans>home</Trans>
                            </Button>
                            <Button onClick={e => this.openMenu(e, Menu.catalog)}>
                                <Trans>catalog</Trans>
                            </Button>
                            <Button onClick={e => this.openMenu(e, Menu.orders)}>
                                <Trans>orders</Trans>
                            </Button>
                            <Button href="/front/supplier" className="active">
                                <Trans>Devis</Trans>
                            </Button>
                            <Button onClick={e => this.openMenu(e, Menu.messages)}>
                                <Trans>messages</Trans>
                            </Button>
                            <Button onClick={e => this.openMenu(e, Menu.settings)}>
                                <Trans>settings</Trans>
                            </Button>
                        </div>
                        <div style={{display: "flex", alignItems: "stretch"}}>
                            <Button onClick={e => this.openMenu(e, Menu.user)}>
                                <AccountCircleIcon/>
                                &nbsp; {window.userName}
                            </Button>
                        </div>
                        <Dropdown
                            actions={menu}
                            classes={{}}
                            anchorEl={anchorEl}
                            onClose={() => this.setState({anchorEl: null})}
                            position={"bottom"}
                        />
                    </div>
                </Toolbar>
            </AppBar>
        );
    }
}
