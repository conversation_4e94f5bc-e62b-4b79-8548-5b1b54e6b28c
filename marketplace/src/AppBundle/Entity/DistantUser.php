<?php

namespace AppBundle\Entity;

use Doctrine\ORM\Mapping as ORM;

/**
 * Country
 *
 * @ORM\Table(name="distant_user")
 * @ORM\Entity(repositoryClass="AppBundle\Repository\DistantUserRepository")
 */
class DistantUser
{
    /**
     * @var int
     *
     * @ORM\Column(name="id", type="integer")
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="AUTO")
     */
    private int $id;

    /**
     * @ORM\Column(name="igg", type="string", length=180)
     */
    private ?string $igg = null;
    /**
     * @ORM\Column(name="first_name", type="string", length=180, nullable=true)
     */
    private ?string $firstName = null;
    /**
     * @ORM\Column(name="last_name", type="string", length=180, nullable=true)
     */
    private ?string $lastName = null;
    /**
     * @ORM\Column(name="personal_title", type="string", length=10, nullable=true)
     */
    private ?string $personalTitle = null;
    /**
     * @ORM\Column(name="preferred_language", type="string", length=50, nullable=true)
     */
    private ?string $preferredLanguage = null;
    /**
     * @ORM\Column(name="email", type="string", length=180, nullable=true)
     */
    private ?string $email = null;
    /**
     * @ORM\Column(name="telephone", type="string", length=180, nullable=true)
     */
    private ?string $telephone = null;
    /**
     * @ORM\Column(name="organization", type="string", length=180, nullable=true)
     */
    private ?string $organization = null;
    /**
     * @ORM\Column(name="manager_igg", type="string", length=180, nullable=true)
     */
    private ?string $managerIgg = null;
    /**
     * @ORM\Column(name="cost_center", type="string", length=180, nullable=true)
     */
    private ?string $costCenter = null;
    /**
     * @ORM\Column(name="site_name", type="string", length=180, nullable=true)
     */
    private ?string $siteName = null;
    /**
     * @ORM\Column(name="site_street", type="string", length=180, nullable=true)
     */
    private ?string $siteStreet = null;
    /**
     * @ORM\Column(name="site_street2", type="string", length=180, nullable=true)
     */
    private ?string $siteStreet2 = null;
    /**
     * @ORM\Column(name="site_zipcode", type="string", length=180, nullable=true)
     */
    private ?string $siteZipcode = null;
    /**
     * @ORM\Column(name="site_locality", type="string", length=180, nullable=true)
     */
    private ?string $siteLocality = null;
    /**
     * @ORM\Column(name="site_country", type="string", length=180, nullable=true)
     */
    private ?string $siteCountry = null;
    /**
     * @ORM\Column(name="site_country_code", type="string", length=180, nullable=true)
     */
    private ?string $siteCountryCode = null;
    /**
     * @ORM\Column(name="invoicing_entity_name", type="string", length=180, nullable=true)
     */
    private ?string $invoicingEntityName = null;
    /**
     * @ORM\Column(name="invoicing_entity_short_name", type="string", length=180, nullable=true)
     */
    private ?string $invoicingEntityShortName = null;
    /**
     * @ORM\Column(name="category", type="string", length=180, nullable=true)
     */
    private ?string $category = null;
    /**
     * @ORM\Column(name="invoicing_entity_street", type="string", length=180, nullable=true)
     */
    private ?string $invoicingEntityStreet = null;
    /**
     * @ORM\Column(name="invoicing_entity_street2", type="string", length=180, nullable=true)
     */
    private ?string $invoicingEntityStreet2 = null;
    /**
     * @ORM\Column(name="invoicing_entity_zipcode", type="string", length=180, nullable=true)
     */
    private ?string $invoicingEntityZipcode = null;
    /**
     * @ORM\Column(name="invoicing_entity_locality", type="string", length=180, nullable=true)
     */
    private ?string $invoicingEntityLocality = null;
    /**
     * @ORM\Column(name="invoicing_entity_country", type="string", length=180, nullable=true)
     */
    private ?string $invoicingEntityCountry = null;
    /**
     * @ORM\Column(name="invoicing_entity_country_code", type="string", length=180, nullable=true)
     */
    private ?string $invoicingEntityCountryCode = null;
    /**
     * @ORM\Column(name="start_date", type="string", length=180, nullable=true)
     */
    private ?string $startDate = null;
    /**
     * @ORM\Column(name="end_date", type="string", length=180, nullable=true)
     */
    private ?string $endDate = null;
    /**
     * @ORM\Column(name="update_date", type="string", length=180, nullable=true)
     */
    private ?string $updateDate = null;
    /**
     * @ORM\Column(name="contrat", type="string", length=180, nullable=true)
     */
    private ?string $contrat = null;

    /**
     * @return int
     */
    public function getId(): int
    {
        return $this->id;
    }

    /**
     * @param int $id
     * @return DistantUser
     */
    public function setId(int $id): DistantUser
    {
        $this->id = $id;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getIgg(): ?string
    {
        return $this->igg;
    }

    /**
     * @param string|null $igg
     * @return DistantUser
     */
    public function setIgg(?string $igg): DistantUser
    {
        $this->igg = $igg;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getFirstName(): ?string
    {
        return $this->firstName;
    }

    /**
     * @param string|null $firstName
     * @return DistantUser
     */
    public function setFirstName(?string $firstName): DistantUser
    {
        $this->firstName = $firstName;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getLastName(): ?string
    {
        return $this->lastName;
    }

    /**
     * @param string|null $lastName
     * @return DistantUser
     */
    public function setLastName(?string $lastName): DistantUser
    {
        $this->lastName = $lastName;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getPersonalTitle(): ?string
    {
        return $this->personalTitle;
    }

    /**
     * @param string|null $personalTitle
     * @return DistantUser
     */
    public function setPersonalTitle(?string $personalTitle): DistantUser
    {
        $this->personalTitle = $personalTitle;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getPreferredLanguage(): ?string
    {
        return $this->preferredLanguage;
    }

    /**
     * @param string|null $preferredLanguage
     * @return DistantUser
     */
    public function setPreferredLanguage(?string $preferredLanguage): DistantUser
    {
        $this->preferredLanguage = $preferredLanguage;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getEmail(): ?string
    {
        return $this->email;
    }

    /**
     * @param string|null $email
     * @return DistantUser
     */
    public function setEmail(?string $email): DistantUser
    {
        $this->email = $email;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getTelephone(): ?string
    {
        return $this->telephone;
    }

    /**
     * @param string|null $telephone
     * @return DistantUser
     */
    public function setTelephone(?string $telephone): DistantUser
    {
        $this->telephone = $telephone;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getOrganization(): ?string
    {
        return $this->organization;
    }

    /**
     * @param string|null $organization
     * @return DistantUser
     */
    public function setOrganization(?string $organization): DistantUser
    {
        $this->organization = $organization;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getManagerIgg(): ?string
    {
        return $this->managerIgg;
    }

    /**
     * @param string|null $managerIgg
     * @return DistantUser
     */
    public function setManagerIgg(?string $managerIgg): DistantUser
    {
        $this->managerIgg = $managerIgg;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getCostCenter(): ?string
    {
        return $this->costCenter;
    }

    /**
     * @param string|null $costCenter
     * @return DistantUser
     */
    public function setCostCenter(?string $costCenter): DistantUser
    {
        $this->costCenter = $costCenter;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getSiteName(): ?string
    {
        return $this->siteName;
    }

    /**
     * @param string|null $siteName
     * @return DistantUser
     */
    public function setSiteName(?string $siteName): DistantUser
    {
        $this->siteName = $siteName;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getSiteStreet(): ?string
    {
        return $this->siteStreet;
    }

    /**
     * @param string|null $siteStreet
     * @return DistantUser
     */
    public function setSiteStreet(?string $siteStreet): DistantUser
    {
        $this->siteStreet = $siteStreet;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getSiteStreet2(): ?string
    {
        return $this->siteStreet2;
    }

    /**
     * @param string|null $siteStreet2
     * @return DistantUser
     */
    public function setSiteStreet2(?string $siteStreet2): DistantUser
    {
        $this->siteStreet2 = $siteStreet2;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getSiteZipcode(): ?string
    {
        return $this->siteZipcode;
    }

    /**
     * @param string|null $siteZipcode
     * @return DistantUser
     */
    public function setSiteZipcode(?string $siteZipcode): DistantUser
    {
        $this->siteZipcode = $siteZipcode;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getSiteLocality(): ?string
    {
        return $this->siteLocality;
    }

    /**
     * @param string|null $siteLocality
     * @return DistantUser
     */
    public function setSiteLocality(?string $siteLocality): DistantUser
    {
        $this->siteLocality = $siteLocality;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getSiteCountry(): ?string
    {
        return $this->siteCountry;
    }

    /**
     * @param string|null $siteCountry
     * @return DistantUser
     */
    public function setSiteCountry(?string $siteCountry): DistantUser
    {
        $this->siteCountry = $siteCountry;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getSiteCountryCode(): ?string
    {
        return $this->siteCountryCode;
    }

    /**
     * @param string|null $siteCountryCode
     * @return DistantUser
     */
    public function setSiteCountryCode(?string $siteCountryCode): DistantUser
    {
        $this->siteCountryCode = $siteCountryCode;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getInvoicingEntityName(): ?string
    {
        return $this->invoicingEntityName;
    }

    /**
     * @param string|null $invoicingEntityName
     * @return DistantUser
     */
    public function setInvoicingEntityName(?string $invoicingEntityName): DistantUser
    {
        $this->invoicingEntityName = $invoicingEntityName;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getInvoicingEntityShortName(): ?string
    {
        return $this->invoicingEntityShortName;
    }

    /**
     * @param string|null $invoicingEntityShortName
     * @return DistantUser
     */
    public function setInvoicingEntityShortName(?string $invoicingEntityShortName): DistantUser
    {
        $this->invoicingEntityShortName = $invoicingEntityShortName;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getCategory(): ?string
    {
        return $this->category;
    }

    /**
     * @param string|null $category
     * @return DistantUser
     */
    public function setCategory(?string $category): DistantUser
    {
        $this->category = $category;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getInvoicingEntityStreet(): ?string
    {
        return $this->invoicingEntityStreet;
    }

    /**
     * @param string|null $invoicingEntityStreet
     * @return DistantUser
     */
    public function setInvoicingEntityStreet(?string $invoicingEntityStreet): DistantUser
    {
        $this->invoicingEntityStreet = $invoicingEntityStreet;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getInvoicingEntityStreet2(): ?string
    {
        return $this->invoicingEntityStreet2;
    }

    /**
     * @param string|null $invoicingEntityStreet2
     * @return DistantUser
     */
    public function setInvoicingEntityStreet2(?string $invoicingEntityStreet2): DistantUser
    {
        $this->invoicingEntityStreet2 = $invoicingEntityStreet2;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getInvoicingEntityZipcode(): ?string
    {
        return $this->invoicingEntityZipcode;
    }

    /**
     * @param string|null $invoicingEntityZipcode
     * @return DistantUser
     */
    public function setInvoicingEntityZipcode(?string $invoicingEntityZipcode): DistantUser
    {
        $this->invoicingEntityZipcode = $invoicingEntityZipcode;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getInvoicingEntityLocality(): ?string
    {
        return $this->invoicingEntityLocality;
    }

    /**
     * @param string|null $invoicingEntityLocality
     * @return DistantUser
     */
    public function setInvoicingEntityLocality(?string $invoicingEntityLocality): DistantUser
    {
        $this->invoicingEntityLocality = $invoicingEntityLocality;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getInvoicingEntityCountry(): ?string
    {
        return $this->invoicingEntityCountry;
    }

    /**
     * @param string|null $invoicingEntityCountry
     * @return DistantUser
     */
    public function setInvoicingEntityCountry(?string $invoicingEntityCountry): DistantUser
    {
        $this->invoicingEntityCountry = $invoicingEntityCountry;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getInvoicingEntityCountryCode(): ?string
    {
        return $this->invoicingEntityCountryCode;
    }

    /**
     * @param string|null $invoicingEntityCountryCode
     * @return DistantUser
     */
    public function setInvoicingEntityCountryCode(?string $invoicingEntityCountryCode): DistantUser
    {
        $this->invoicingEntityCountryCode = $invoicingEntityCountryCode;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getStartDate(): ?string
    {
        return $this->startDate;
    }

    /**
     * @param string|null $startDate
     * @return DistantUser
     */
    public function setStartDate(?string $startDate): DistantUser
    {
        $this->startDate = $startDate;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getEndDate(): ?string
    {
        return $this->endDate;
    }

    /**
     * @param string|null $endDate
     * @return DistantUser
     */
    public function setEndDate(?string $endDate): DistantUser
    {
        $this->endDate = $endDate;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getUpdateDate(): ?string
    {
        return $this->updateDate;
    }

    /**
     * @param string|null $updateDate
     * @return DistantUser
     */
    public function setUpdateDate(?string $updateDate): DistantUser
    {
        $this->updateDate = $updateDate;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getContrat(): ?string
    {
        return $this->contrat;
    }

    /**
     * @param string|null $contrat
     * @return DistantUser
     */
    public function setContrat(?string $contrat): DistantUser
    {
        $this->contrat = $contrat;
        return $this;
    }

}