# Processus d'inscription des utilisateurs iDEAL

Le système d'inscription iDEAL est un processus automatisé exécuté par cron en deux étapes :

1. **Récupération des données** : La commande `open:buyer:retrieve` récupère les données utilisateurs depuis l'API iDEAL et les stocke dans l'entité DistantUser

2. **Import des utilisateurs** : La commande `open:buyer:import` crée/met à jour les utilisateurs dans le système avec leurs hiérarchies managériales

**Note** : Aucune action manuelle n'est requise, tout est synchronisé automatiquement avec le système d'identité externe.
