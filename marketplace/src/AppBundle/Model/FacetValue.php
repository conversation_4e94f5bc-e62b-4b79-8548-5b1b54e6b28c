<?php

namespace AppBundle\Model;


/**
 * Class FacetValue represents a facet value.
 * @package AppBundle\Model
 */
class FacetValue
{
    /**
     * @var int|null $parent
     */
    private $parent;

    /**
     * @var int $level
     */
    private $level;

    /**
     * @var string value of the facet.
     */
    private $value;

    /**
     * @var string $label optional label for this value
     */
    private $label;

    /**
     * @var int $hits number of hits for this value
     */
    private $hits;

    public function __construct()
    {
        $this->level = 0;
    }

    /**
     * @return int|null
     */
    public function getParent(): ?int
    {
        return $this->parent;
    }

    /**
     * @param int|null $parent
     * @return FacetValue
     */
    public function setParent(?int $parent): self
    {
        $this->parent = $parent;
        return $this;
    }

    /**
     * @return int
     */
    public function getLevel(): int
    {
        return $this->level;
    }

    /**
     * @param int $level
     * @return FacetValue
     */
    public function setLevel(int $level): self
    {
        $this->level = $level;
        return $this;
    }


    /**
     * @return string
     */
    public function getValue(): string
    {
        return $this->value;
    }

    /**
     * @param string $value
     * @return FacetValue
     */
    public function setValue(string $value): FacetValue
    {
        $this->value = $value;
        return $this;
    }

    /**
     * @return string
     */
    public function getLabel(): ?string
    {
        return $this->label;
    }

    /**
     * @param string $label
     * @return FacetValue
     */
    public function setLabel(?string $label): FacetValue
    {
        $this->label = $label;
        return $this;
    }

    /**
     * @return int
     */
    public function getHits(): int
    {
        return $this->hits;
    }

    /**
     * @param int $hits
     * @return FacetValue
     */
    public function setHits(int $hits): FacetValue
    {
        $this->hits = $hits;
        return $this;
    }



}
