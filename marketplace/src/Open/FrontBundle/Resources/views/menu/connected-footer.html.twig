{% trans_default_domain 'AppBundle' %}

{% set countryOfDeliveryIzbergCode = (app.user.countryOfDelivery.izbergCode) | lower %}

<footer class="Footer connected">
    <section class="footer-border"></section>
    <section class="footer-connected">
        <div class="footer-links-connected">
            <div class="footer-logo--home">
                <a href="{{ path('homepage') }}">
                    <img src="{{ asset('images/logo-mp.svg') }}">
                </a>
            </div>
            <div>
                <a href="/{{ locale }}/need_help{{ '-' ~ app.user.marketplace.totalDiscriminator }}">{{ 'footer.help.title'|trans }}</a>
            </div>
            <div class="v-line desktop-only">
            </div>
            <div>

                <a href="/{{ locale }}/cgu">{{ 'footer.cgu_connected'|trans }}</a>
            </div>
            <div class="v-line desktop-only">
            </div>
            <div>
                <a href="#" onclick="
                        window.UI.Modal.reportContent(
                        '{{ path('message.report.illicit_content') }}',
                        '{{ 'message.modal.empty'|trans({}, 'AppBundle')|escape('js') }}',
                        '{{ 'message.add.success'|trans({}, 'AppBundle')|escape('js') }}',
                        '{{ 'message.add.error'|trans({}, 'AppBundle')|escape('js') }}'
                    )">
                    {{ 'footer.report_content'|trans }}
                </a>
            </div>
            <div class="v-line desktop-only">
            </div>
            <div>
                <a href="{{ path('front.supplier.invite') }}">{{ 'footer.invite_suppplier'|trans }}</a>
            </div>
        </div>
    </section>
    <div class="h-line"></div>
    <div class="additional-content">
        {{ 'footer.additional.copyright'|trans }} - {{ 'footer.all_right_reserved'|trans }}
    </div>

</footer>
