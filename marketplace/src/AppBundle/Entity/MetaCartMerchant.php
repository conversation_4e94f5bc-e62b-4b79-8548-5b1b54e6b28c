<?php


namespace AppBundle\Entity;

use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\ORM\Mapping as ORM;
use Doctrine\ORM\PersistentCollection;

/**
 * MetaCartMerchant
 *
 * @ORM\Table(name="meta_cart_merchant")
 * @ORM\Entity(repositoryClass="AppBundle\Repository\MetaCartMerchantRepository")
 */
class MetaCartMerchant
{
    /**
     * @var int
     *
     * @ORM\Column(name="id", type="integer")
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="AUTO")
     */
    private $id;

    /**
     * @var int
     *
     * @ORM\Column(name="izberg_id", type="integer")
     */
    private $izbergId;

    /**
     * @var MetaCart
     *
     * @ORM\ManyToOne(targetEntity="AppBundle\Entity\MetaCart")
     */
    private $metaCart;

    /**
     * @var string|null
     *
     * @ORM\Column(name="comment", type="text", nullable=true)
     */
    private $comment;

    /**
     * @var string|null
     *
     * @ORM\Column(name="comment_placeholder", type="text", nullable=true)
     */
    private $commentPlaceholder;

    /**
     * One meta cart merchant have multiple cart item
     * @var ArrayCollection
     *
     * @ORM\OneToMany(targetEntity="AppBundle\Entity\MetaCartMerchantItem", mappedBy="metaCartMerchant")
     */
    private $items;

    public function __construct()
    {
        $this->items = new ArrayCollection();
    }

    public function getId(): int
    {
        return $this->id;
    }

    public function getIzbergId(): int
    {
        return $this->izbergId;
    }

    public function setIzbergId(int $izbergId): self
    {
        $this->izbergId = $izbergId;
        return $this;
    }

    public function getMetaCart(): MetaCart
    {
        return $this->metaCart;
    }

    public function setMetaCart(MetaCart $metaCart): self
    {
        $this->metaCart = $metaCart;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getComment(): ?string
    {
        return $this->comment;
    }

    /**
     * @param string|null $comment
     * @return $this
     */
    public function setComment(?string $comment): self
    {
        $this->comment = $comment;
        return $this;
    }

    public function getCommentPlaceholder(): ?string
    {
        return $this->commentPlaceholder;
    }

    public function setCommentPlaceholder(?string $commentPlaceholder): self
    {
        $this->commentPlaceholder = $commentPlaceholder;
        return $this;
    }

    public function getItems(): ArrayCollection|PersistentCollection
    {
        return $this->items;
    }

    public function addItem(MetaCartMerchantItem $item): self
    {
        if (!$this->items->contains($item)) {
            $this->items->add($item);
        }

        return $this;
    }

    public function removeItem(MetaCartMerchantItem $item): self
    {
        if ($this->items->contains($item)) {
            $this->items->removeElement($item);
        }

        return $this;
    }
}
