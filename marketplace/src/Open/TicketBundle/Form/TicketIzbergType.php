<?php
/**
 * Created by PhpStorm.
 * User: QAR14123
 * Date: 05/06/2018
 * Time: 15:25
 */

namespace Open\TicketBundle\Form;


use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\SubmitType;
use Symfony\Component\Form\Extension\Core\Type\TextareaType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

class TicketIzbergType extends AbstractType
{
    /**
     * @param FormBuilderInterface $builder
     * @param array                $options
     */
    public function buildForm(FormBuilderInterface $builder, array $options)
    {
        $builder
            ->add('message',
                TextareaType::class,
                array(
                    'label' => 'contactMerchant.form.message',
                    "attr" => array (
                        "class" => "full_width"
                    ),
                    'translation_domain' => 'AppBundle'
                )
            )
            ->add(
                'save',
                SubmitType::class,
                array(
                    'label' => 'contactMerchant.form.save',
                    "attr" => array (
                        "value" => "save",
                        "class" => "Button button_margin"
                    ),
                    'translation_domain' => 'AppBundle'
                )
            )
            ->setMethod('POST');
    }

    public function configureOptions(OptionsResolver $resolver)
    {
        $resolver->setDefaults([
            'translation_domain' => 'AppBundle',
            'is_response' => false,
            'validation_groups' => array('Default'),
            'allow_extra_fields' => true
        ]);
    }
}