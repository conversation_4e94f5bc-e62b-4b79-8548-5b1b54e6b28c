<?php

namespace AppBundle\Entity;

use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use Doctrine\ORM\PersistentCollection;
use Symfony\Component\Validator\Constraints as Assert;
use Symfony\Bridge\Doctrine\Validator\Constraints\UniqueEntity;

/**
 * UserEnabler, give matrix to enabled user by entity/site/user type
 * @ORM\Table(name="user_enabler")
 * @ORM\Entity(repositoryClass="AppBundle\Repository\UserEnablerRepository")
 * @UniqueEntity(fields="id")
 * @ORM\HasLifecycleCallbacks()
 * */
class UserEnabler implements \JsonSerializable
{

    use TimestampedTrait;
    use ExportableEntityTrait;
    use TechnicalIdentifierTrait;

    /**
     * @var int
     * @ORM\Column(name="id", type="integer")
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="AUTO")
     */
    private $id;

    /**
     *
     * @ORM\ManyToOne(targetEntity="AppBundle\Entity\InvoiceEntity",inversedBy="userEnablers",cascade={"persist"})
     * @ORM\JoinColumn(name="invoice_entity_id", referencedColumnName="id", nullable=true)
     */
    private $entity;

    /**
     * @var Site
     *
     * @ORM\ManyToOne(targetEntity="AppBundle\Entity\Site",inversedBy="userEnablers",cascade={"persist"})
     * @ORM\JoinColumn(name="site_id", referencedColumnName="id", nullable=true)
     */
    private Site $site;

    /**
     *
     * @var string
     * @ORM\Column(name="user_type", type="string", length=2, nullable=true)
     */
    private string $userType;

    /**
     * @var PersistentCollection $users
     *
     * @ORM\OneToMany(targetEntity="AppBundle\Entity\User", mappedBy="userEnabler")
     */
    private PersistentCollection  $users;


    /**
     * @ORM\Column(name="enabled", type="boolean")
     */
    private $enabled = false;



    public function jsonSerialize()
    {
        return json_encode(array());
    }

    /**
     * @return mixed
     */
    public function getEntity()
    {
        return $this->entity;
    }

    /**
     * @param mixed $entity
     */
    public function setEntity($entity): void
    {
        $this->entity = $entity;
    }

    /**
     * @return mixed
     */
    public function getSite()
    {
        return $this->site;
    }

    /**
     * @param mixed $site
     */
    public function setSite($site): void
    {
        $this->site = $site;
    }

    /**
     * @return mixed
     */
    public function getUserType()
    {
        return $this->userType;
    }

    /**
     * @param mixed $userType
     */
    public function setUserType($userType): void
    {
        $this->userType = $userType;
    }



    /**
     * @return int
     */
    public function getId(): int
    {
        return $this->id;
    }

    /**
     * @param int $id
     */
    public function setId(int $id): void
    {
        $this->id = $id;
    }

    /**
     * @return PersistentCollection
     */
    public function getUsers(): Collection
    {
        return $this->users;
    }

    /**
     * @param PersistentCollection $users
     */
    public function setUsers(PersistentCollection $users): void
    {
        $this->users = $users;
    }

    /**
     * @return mixed
     */
    public function getEnabled()
    {
        return $this->enabled;
    }

    /**
     * @param mixed $enabled
     */
    public function setEnabled($enabled): void
    {
        $this->enabled = $enabled;
    }




}