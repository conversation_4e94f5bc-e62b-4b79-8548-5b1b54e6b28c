{% extends '@OpenBack/base.html.twig' %}

{% block body %}
    <div class="Page-outer">

        <div class="Page-inner">

            {% if error %}
                <ul class="Messages">
                    <li class="Messages-item Message--error">
                        {{ error.messageKey|trans(error.messageData, 'security') }}
                    </li>
                </ul>
            {% endif %}

            <div class="LoginForm">

                <form class="form-signin" action="{{ path('admin.login') }}" method="post" id="js-form-login"
                      autocomplete="off">
                    <input type="hidden" name="_csrf_token" value="{{ csrf_token('authenticate') }}">

                    <h2 class="form-signin-heading">{{ 'operator.security.login.signin'|trans }}</h2>

                    <div class="form-group">
                        <label for="inputEmail">{{ 'operator.security.login.email'|trans }}</label>
                        <input type="email" class="form-control" id="inputEmail" name="_username"
                               value="{{ last_username }}" required="" autofocus=""
                               placeholder="{{ 'operator.security.login.email'|trans }}">
                    </div>

                    <div class="form-group">
                        <label for="inputPassword">{{ 'operator.security.login.password'|trans }}</label>
                        <input id="inputPassword" name="_password" class="form-control"
                               placeholder="{{ 'operator.security.login.password'|trans }}" required="" type="password">
                    </div>

                    <button id="_submit" name="_submit" class="btn btn-lg btn-primary btn-block" type="submit"
                            value="{{ 'operator.security.login.submit'|trans }}">{{ 'operator.security.login.submit'|trans }}</button>

                    <a href="{{ path('send.user.password.reset') }}" class="btn btn-sm btn-link">{{ 'operator.security.login.forgot'|trans }}</a>
                </form>
            </div>
        </div>
    </div>
{% endblock %}