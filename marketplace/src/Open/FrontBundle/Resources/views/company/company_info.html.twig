{% extends '@OpenFront/menu/buyer_menu.html.twig' %}
{% form_theme form 'Form/appli_layout.html.twig' %}
{% trans_default_domain 'AppBundle' %}

{% block title %}
    {{ 'menu.desktop.company' | trans }}
{% endblock %}

{% block body %}
<div class="Page-inner">

    <div class="Form CompanyForm">

        {{ form_start(form, {'attr':{'id':'js-company-form'}}) }}

        <div class="form-title"><h1>{{ 'company.form.social'|trans }}</h1></div>

        <div class="form-title">
            <h4>{{ 'company.form.company_info'|trans }}</h4>
        </div>

        {% do form.mainAddress.check.setRendered %}
        <div class="flex-container">

            <div class="form-row">
                <div class="js-select-wrapper select-wrapper has-text">
                    {{ form_widget(form.mainAddress.country, {'attr':{'id':'company_form_country', 'data-sort': 'true'}}) }}
                    {{ form_label(form.mainAddress.country) }}
                </div>
                <small class="error">{{ form_errors(form.mainAddress.country) }}</small>
            </div>
        </div>
        <div class="flex-container">

            {{ form_row(form.name) }}

            {{ form_row(form.identification, {'attr':{'id':'fos_user_registration_form_identification', 'readonly':'readonly'}}) }}

            <div class="DocumentForm {% if isDisabled %}is-disabled{% endif %}">
                {% if form.businessRegistration is defined %}
                    <div id="businessRegistrationDiv ">
                        <div id="js-upload-bizreg" class="DocumentForm-uploadField" data-target-list-id="js-businessRegistration-docs">
                            {{ form_row(form.businessRegistration) }}
                        </div>

                        <div id="js-businessRegistration-nodocs" class="Messages DocumentForm-nodocs"  {% if (company.documents|length) > 0 %} style="display:none"  {% endif %}>
                            <div class="Message-item Message--error">
                                {{ 'document.noDoc'|trans }}
                            </div>
                        </div>

                        <div id="js-businessRegistration-upload-message" class="Messages DocumentForm-nodocs" style="display: none;">
                            <div class="Message-item Message--error">
                            </div>
                        </div>

                        <ul id="js-businessRegistration-docs" class="DocumentForm-docs">
                            {% for document in company.documents %}
                                <li>
                                    <a href="{{ path('front.document.view',{id:document.id}) }}" target="_blank">{{ document.filename }}</a>

                                    {% if not isDisabled %}
                                        <svg class="IconMax js-doc-remove" data-doc-id="{{ document.id }}" data-company-id="{{ company.id }}" data-doc-type="businessRegistration">
                                            <use xlink:href="#icon-cancel"></use>
                                        </svg>
                                    {% endif %}
                                </li>
                            {% endfor %}
                        </ul>

                        <div id="progressBusinessRegistration" class = "Message--progress"></div>
                        <button type="button" class="js-upload-file-mobile Button Button--upload Button--uploadMobile" data-target="js-upload-bizreg">
                            <svg class="Icon">
                                <use xlink:href="#icon-upload"></use>
                            </svg>
                            {{ 'document.upload.title'|trans }}
                        </button>
                    </div>
                {% endif %}

                {% if not isDisabled %}
                    {% if not cgu %}
                        <div class="DocumentForm-cgu">
                            <a href="{{ path('front.company.cgu') }}">
                                <i class="arrow right"></i>
                                {{ 'cgu.read' | trans }}
                            </a>
                        </div>
                        <small id="cguError" class="cgu-error error" style="display: none">
                            {{ 'cgu.errorMessage'| trans }}
                        </small>
                    {% else %}
                        <div class="Messages messages-cgu">
                            <svg class="Icon">
                                <use xlink:href="#icon-buyer-tick-green"></use>
                            </svg>
                            {{ 'cgu.validated' | trans({'%link%' : path('front.company.cgu')})|raw }}
                        </div>
                    {% endif %}
                {% else %}
                    <div class="Messages messages-cgu">
                        <svg class="Icon">
                            <use xlink:href="#icon-buyer-tick-green"></use>
                        </svg>
                        {{ 'cgu.validated' | trans({'%link%' : path('front.company.cgu')})|raw }}
                    </div>
                {% endif %}
            </div>
        </div>

        <div class="form-title">
            <h4>{{ 'company.form.main_contact.title'|trans }}</h4>
        </div>
        <div class="flex-container">
            <div class="row-2-1">
                {{ form_row(form.mainAddress.address) }}
            </div>
            <div class="row-2-1">
                {{ form_row(form.mainAddress.address2) }}
            </div>
            <div class="row-1-1-1">
                {{ form_row(form.mainAddress.zipCode) }}
                {{ form_row(form.mainAddress.city) }}

                {% if form.mainAddress.region is defined %}
                    <div class="form-row">
                        <div class="js-select-wrapper select-wrapper has-text">
                            {{ form_widget(form.mainAddress.region) }}
                            {{ form_label(form.mainAddress.region) }}
                        </div>
                        <small class="error">{{ form_errors(form.mainAddress.region) }}</small>
                    </div>
                {% elseif form.mainAddress.regionText is defined %}
                    {{ form_row(form.mainAddress.regionText) }}
                {% endif %}
            </div>

        </div>
        <div class="form-title"><h4>{{ 'company.form.billing_address.address'|trans }}</h4></div>

        <div class="form-check-row">
            {{ form_row(form.billingAddress.check,{ 'label' : 'company.form.check' }) }}
        </div>

        <div id="js-billing-address">
            {% do form.billingAddress.country.setRendered %}
            <div class="flex-container">
                <div class="row-2-1">
                    {{ form_row(form.billingService) }}
                </div>
            </div>
            <div class="flex-container">
                <div class="row-2-1">
                    {{ form_row(form.billingAddress.address) }}
                </div>
                <div class="row-2-1">
                    {{ form_row(form.billingAddress.address2) }}
                </div>
            </div>
            <div class="flex-container">
                <div class="row-1-1-1">
                    {{ form_row(form.billingAddress.zipCode) }}
                    {{ form_row(form.billingAddress.city) }}

                    {% if form.billingAddress.region is defined %}
                    <div class="form-row">
                        <div class="js-select-wrapper select-wrapper has-text">
                            {{ form_widget(form.billingAddress.region) }}
                            {{ form_label(form.billingAddress.region) }}
                        </div>
                        <small class="error">{{ form_errors(form.billingAddress.region) }}</small>
                    </div>
                    {% elseif form.billingAddress.regionText is defined %}
                        {{ form_row(form.billingAddress.regionText) }}
                    {% endif %}
                </div>
            </div>
        </div>

        <div class="Form-group Buttons-group">
            {% if showBuyerMenu is defined and showBuyerMenu == true %}
                <a href="{{ path('front.company.profile') }}" class="button grey-button">{{ 'company.form.back'|trans }}</a>
                {% if not isDisabled %}
                    {{ form_row(form.save) }}
                {% endif %}
            {% else %}
                <div>
                    {% if not isDisabled %}
                        <div style="display:none">{{ form_row(form.save) }}</div>
                        <button type="submit" id="company_info_form_save" name="company_info_form[save]" value="save" class="Button button_margin">{{ 'company.form.save'|trans }}</button>
                    {% endif %}
                </div>
            {% endif %}
        </div>

        {{ form_end(form) }}

        <script type="text/javascript">
            'use strict';

            document.addEventListener('DOMContentLoaded', function() {

              var $form = $('#js-company-form');

              var $businessRegistration = $('#js-businessRegistration-docs');
              var $businessRegistrationUploadButton = $('#js-button-company_info_form_businessRegistration');
              var $businessRegistrationAuth = $('#company_info_form_businessRegistration');

              // Bind click events on remove buttons
              $form.on('click', '.js-doc-remove', function (ev) {
                var $target = $(ev.currentTarget);
                var docType = $target.data('docType');
                var docId = parseInt($target.data('docId'), 0);
                var companyId = parseInt($target.data('companyId'), 0);

                UI.Modal.confirm('', '{{ 'document.upload.delete'|trans({}, 'AppBundle') }}', function () {
                      var $parent = $target.parent();
                      var $cont = $parent.parent();

                      $.ajax(
                          Routing.generate('front.document.remove'),
                          {
                            type: 'POST',
                            dataType: 'json',
                            data: {
                              docId: docId,
                              companyId: companyId,
                              docType: docType
                            },
                            success: function (data) {
                              $parent.fadeOut('fast', function () {
                                $parent.remove();

                                $('#' + 'js-' + docType + '-upload-message').css('display', 'none');

                                if ($cont.find('li').length === 0) {
                                  $('#' + 'js-' + docType + '-nodocs').css('display', 'block');

                                  $cont.hide();

                                  // Reset file counter for the validation
                                  $cont.parent().find('input[type=file]').data('count', 0);
                                }
                              });
                            },
                            error: function (response, textStatus, errorThrown) {
                              alert('{{ 'document.upload.deleteError'|trans }}');
                              console.log('Error [' + errorThrown + '] : ' + response.responseText); //Todo: flash user about the error
                            }
                          }
                      );
                    },
                    function () {
                      console.log('Confirmation canceled');
                    });
              });

              $businessRegistrationUploadButton.click(function () {
                $(this).parent().find('label').click();
              });

              // Initialize file upload
              $businessRegistrationAuth.fileupload(
                  UI.UploadDocument.manageUpload(
                      '{{ path(  'company.upload.businessRegistration'  , {id:company.id}) }}',
                      $('#progressBusinessRegistration'),
                      $('#js-businessRegistration-upload-message'),
                      $businessRegistration,
                      $('#js-businessRegistration-nodocs'),
                      'businessRegistration',
                      $businessRegistrationUploadButton,
                      '{{ company.id }}',
                      '{{ typeError }}',
                      '{{ sizeMax }}',
                      '{{ sizeError }}'
                  )
              );

              // Initialize the custom company identification validation
              window.UI.CompanyIdentification.init(
                '#company_info_form_mainAddress_country'
              );

              // Initialize country selection
              window.UI.CountrySelector.init(
                '#company_info_form_mainAddress_country',
                '#company_vform_identification'
              );

                UI.OpenDiv.createCheckAction({
                    "divId": "#js-billing-address",
                    "checkId": "#company_info_form_billingAddress_check"
                });

                window.UI.Select.init();

                {{ form_jquery_validation(form) }}

              // Tweak validation of the sites checkbox (1 site checked required unless role is admin)
              $('input[name="company_info_form[identification]"]').rules(
                'add',
                {
                  'company-identification' : true,
                  'messages' : {
                    'company-identification': '{{ 'form.company.ident_number.invalid'|trans({}, 'validators') }}'
                  }
                }
              );

                // Auto complete on city for main address
                UI.AutoComplete.createAutoComplete({
                    "path": "{{ path('city_autocomplete') }}",
                    "countryId": "#company_info_form_mainAddress_country",
                    "cityId": "#company_info_form_mainAddress_city",
                    "zipcodeId": "#company_info_form_mainAddress_zipCode",
                    "cityField": true
                });

                // Auto complete on city for billing address
                UI.AutoComplete.createAutoComplete({
                    "path": "{{ path('city_autocomplete') }}",
                    "countryId": "#company_info_form_mainAddress_country",
                    "cityId": "#company_info_form_billingAddress_city",
                    "zipcodeId": "#company_info_form_billingAddress_zipCode",
                    "cityField": true
                });

                // Auto complete on zipcode for main address
                UI.AutoComplete.createAutoComplete({
                    "path": "{{ path('city_autocomplete') }}",
                    "countryId": "#company_info_form_mainAddress_country",
                    "cityId": "#company_info_form_mainAddress_zipCode",
                    "zipcodeId": "#company_info_form_mainAddress_city",
                    "cityField": false
                });

                // Auto complete on zipcode for billing address
                UI.AutoComplete.createAutoComplete({
                    "path": "{{ path('city_autocomplete') }}",
                    "countryId": "#company_info_form_mainAddress_country",
                    "cityId": "#company_info_form_billingAddress_zipCode",
                    "zipcodeId": "#company_info_form_billingAddress_city",
                    "cityField": false
                });

                UI.CountrySelector.filterRegion({
                    "countrySelector": "#company_info_form_mainAddress_country",
                    "regionSelector": "#company_info_form_mainAddress_region"
                });
                UI.CountrySelector.filterRegion({
                    "countrySelector": "#company_info_form_mainAddress_country",
                    "regionSelector": "#company_vform_billingAddress_region"
                });

                $("#company_info_form_billingAddress_check").click(function(){
                   if($(this).is(":checked")){
                       $("#js-billing-address").find("div.select-wrapper").removeClass('disabled');
                   }
                });

              // Handle clicks on fake upload buttons
              $('.js-upload-file-mobile').on('click', function (ev) {
                var $target = $(ev.currentTarget);
                $('#' + $target.data('target')).find('.js-button-upload').click();
              });


              // Check if count of doc is greater than 1
              $.validator.addMethod('uploadRequired', function (value, element) {
                return (parseInt($(element).data('count'), 10) > 0);
              });

              $('input[type=file]').each(function (index, el) {
                var $el = $(el)
                var $parent = $el.parent().parent();
                var $list = $('#' + $parent.data('targetListId'));

                var nbFiles = $list.find('li').length;

                $el.data('count', nbFiles);

                if (nbFiles <= 0) {
                  $list.hide();
                }
              });

              var uploadRequiredRules = {
                required: false,
                uploadRequired: true,
                'messages': {
                  'uploadRequired': '{{ 'form.company.fileupload.invalid'| trans({}, 'validators') }}'
                }
              };

            {% if form.businessRegistration is defined %}
              $('input[name="company_document_form[businessRegistration][]"]').rules('add', uploadRequiredRules);
            {% endif %}

                $(":input").attr("autocomplete", "new-password");
            });

        </script>
    </div>
</div>
{% endblock %}