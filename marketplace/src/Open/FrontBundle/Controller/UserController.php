<?php

namespace Open\FrontBundle\Controller;

use AppB<PERSON>le\Controller\MkoController;
use AppBundle\Entity\User;
use AppBundle\Repository\UserToUserRelationshipRepository;
use AppBundle\Services\BuyerService;
use AppBundle\Services\MailService;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Security;
use Symfony\Component\Form\Extension\Core\Type\FormType;
use Symfony\Component\Form\Extension\Core\Type\HiddenType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormFactory;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Validator\Constraints\NotBlank;

class UserController extends MkoController
{
    /**
     * @Route("/user/profile", name="front.user.profile")
     * @Security("is_granted('ROLE_BUYER')")
     * @param BuyerService $buyerService
     *
     * @return Response
     */
    public function buyerProfileAction(BuyerService $buyerService): Response
    {
        /** @var User $user */
        $user = $this->getUser();

        $superior = $buyerService->getBuyerSuperior($user);
        $delegatesAsString = "";
        if ($superior) {
            $delegates = $buyerService->getBuyerDelegates($superior);


            /** @var User $delegate */
            foreach ($delegates as $delegate) {
                $delegatesAsString .= sprintf("%s %s, ", $delegate->getFirstname(), $delegate->getLastname());
            }
            if (strlen($delegatesAsString) > 2) {
                $delegatesAsString = substr($delegatesAsString, 0, strlen($delegatesAsString) - 2);
            }
        }


        return $this->render(
            '@OpenFront/user/user_profile.html.twig',
            [
                'user' => $user,
                'manager' => $superior,
                'delegates' => $delegatesAsString,
            ]
        );
    }

    /**
     * @Route("/user/country-of-delivery", name="front.user.country_of_delivery", methods={"POST"})
     * @Security("is_granted('ROLE_BUYER')")
     * @param Request      $request
     * @param FormFactory  $formFactory
     * @param BuyerService $buyerService
     *
     * @return Response
     */
    public function countryOfDeliveryAction(Request $request, FormFactory $formFactory, BuyerService $buyerService): Response
    {
        $buyer = $this->getUser();

        $form = $formFactory->createNamedBuilder('', FormType::class, null, ['csrf_token_id' => 'country_of_delivery'])
            ->add(
                'country',
                HiddenType::class,
                [
                    'constraints' => new NotBlank()
                ]
            )
            ->setMethod('POST')
            ->getForm();

        $form->handleRequest($request);
        if ($form->isSubmitted() && $form->isValid()) {
            $countryCode = $form->getData()['country'];
            if ($buyerService->changeBuyerCountryOfDelivery($buyer, $countryCode)) {
                return new Response('');
            }
        }

        return new Response('', Response::HTTP_BAD_REQUEST);
    }

    /**
     * @Route("/user/delegations", name="front.user.delegation")
     * @Security("is_granted('ROLE_BUYER')")
     * @param Request      $request
     * @param BuyerService $buyerService
     *
     * @return Response
     */
    public function delegationAction(Request $request, BuyerService $buyerService): Response
    {
        $isMobile = (boolean)$request->query->get('isMobile', '0');
        // List all delegates for this user
        /** @var User $buyer */
        $buyer = $this->getUser();
        $delegates = $buyerService->getBuyerDelegates($buyer);

        $template = '@OpenFront/user/user_delegate.html.twig';

        if ($isMobile) {
            $template = '@OpenFront/user/user_delegate_mobile.html.twig';
        }

        return $this->render(
            $template,
            [
                'user' => $this->getUser(),
                'delegates' => $delegates,
            ]
        );
    }

    /**
     * @Route("/user/delegations/autocomplete", name="front.user.delegation.autocomplete", methods={"POST"})
     * @Security("is_granted('ROLE_BUYER')")
     * @param Request      $request
     * @param BuyerService $buyerService
     *
     * @return JsonResponse
     */
    public function autocompleteDelegationAction(Request $request, BuyerService $buyerService): JsonResponse
    {
        $term = preg_quote(strtolower((string) $request->request->get('term')), '/');
        /** @var User $buyer */
        $buyer = $this->getUser();

        $filteredLinkableBuyers = $buyerService->getPotentialDelegates($term, $buyer);
        $response = array_map(
            static function (User $buyer) {
                return [
                    'id' => $buyer->getUsername(),
                    'label' => $buyer->getUsername() . ' - ' . $buyer->getFirstname() . ' ' . $buyer->getLastname(),
                ];
            },
            $filteredLinkableBuyers
        );

        return new JsonResponse($response);
    }

    /**
     * @Route("/user/delegations/new", name="front.user.delegation.new", methods={"POST"})
     * @Security("is_granted('ROLE_BUYER')")
     * @param Request      $request
     * @param BuyerService $buyerService
     *
     * @return Response
     */
    public function newDelegationAction(Request $request, BuyerService $buyerService, MailService $emailService): Response
    {
        $isAjax = $request->isXmlHttpRequest();
        $isMobile = (boolean)$request->query->get('isMobile', '0');
        if (!$isAjax) {
            return new Response('Forbidden', Response::HTTP_FORBIDDEN);
        }

        $form = $this->createFormBuilder(null, ['csrf_protection' => true, 'csrf_field_name' => '_token', 'csrf_token_id' => 'add'])
            ->add('username', TextType::class, ['required' => true, 'constraints' => new NotBlank()])
            ->setMethod('POST')
            ->getForm();

        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $username = $form->getData()['username'];

            $buyerWhoDelegating = $this->getUser();
            $delegationToBuyer = $buyerService->getBuyerFromUsername($username);

            if ($delegationToBuyer instanceof User) {
                $buyerService->attachBuyerToDelegate($buyerWhoDelegating, $delegationToBuyer, $this->getUser(), false);
                $delegates = $buyerService->getBuyerDelegates($buyerWhoDelegating);
                if ($buyerWhoDelegating instanceof User) {
                    $emailService->sendEmailMessage(
                        MailService::DELEGATION_ADDED_BY_MANAGER_TO_MANAGER,
                        $buyerWhoDelegating->getLocale(),
                        $buyerWhoDelegating->getEmail(),
                        [
                            'firstName' => $buyerWhoDelegating->getFirstname(),
                            'lastName' => $buyerWhoDelegating->getLastname(),
                            'firstNameDelegate' => $delegationToBuyer->getFirstname(),
                            'lastNameDelegate' => $delegationToBuyer->getLastname(),
                        ]
                    );
                    $emailService->sendEmailMessage(
                        MailService::DELEGATION_ADDED_BY_MANAGER_TO_DELEGATE,
                        $delegationToBuyer->getLocale(),
                        $delegationToBuyer->getEmail(),
                        [
                            'firstName' => $delegationToBuyer->getFirstname(),
                            'lastName' => $delegationToBuyer->getLastname(),
                            'firstNameManager' => $buyerWhoDelegating->getFirstname(),
                            'lastNameManager' => $buyerWhoDelegating->getLastname(),
                        ]
                    );
                }

                $template = '@OpenFront/shared/delegation_table_row.html.twig';
                if ($isMobile) {
                    $template = '@OpenFront/shared/delegation_row_mobile.html.twig';
                }

                return $this->render(
                    $template,
                    [
                        'delegate' => $delegationToBuyer,
                        'delegates' => $delegates
                    ]
                );

            }

            return new Response('No content', Response::HTTP_NO_CONTENT);
        }

        return new Response('Form Error', Response::HTTP_BAD_REQUEST);
    }

    /**
     * @Route("/user/delegations/delete", name="front.user.delegation.delete", methods={"DELETE"})
     * @Security("is_granted('ROLE_BUYER')")
     * @param Request                          $request
     * @param BuyerService                     $buyerService
     * @param UserToUserRelationshipRepository $relationshipRepository
     *
     * @return Response
     */
    public function deleteDelegationAction(Request $request, BuyerService $buyerService, MailService $emailService): Response
    {
        $isAjax = $request->isXmlHttpRequest();
        if (!$isAjax) {
            return new Response('Forbidden', Response::HTTP_FORBIDDEN);
        }

        $form = $this->createFormBuilder(null, ['csrf_protection' => true, 'csrf_field_name' => '_token', 'csrf_token_id' => 'delete'])
            ->add('username', TextType::class, ['required' => true, 'constraints' => new NotBlank()])
            ->setMethod('DELETE')
            ->getForm();

        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $username = $form->getData()['username'];

            $buyerWhoDelegating = $this->getUser();
            $delegationToBuyer = $buyerService->getBuyerFromUsername($username);

            if ($delegationToBuyer instanceof User) {
                $buyerService->deleteDelegate($buyerWhoDelegating, $delegationToBuyer, $this->getUser(), false);

                $delegates = $buyerService->getBuyerDelegates($buyerWhoDelegating);


                if ($buyerWhoDelegating instanceof User) {
                    $emailService->sendEmailMessage(
                        MailService::DELEGATION_REMOVE_BY_MANAGER_TO_MANAGER,
                        $buyerWhoDelegating->getLocale(),
                        $buyerWhoDelegating->getEmail(),
                        [
                            'firstName' => $buyerWhoDelegating->getFirstname(),
                            'lastName' => $buyerWhoDelegating->getLastname(),
                            'firstNameDelegate' => $delegationToBuyer->getFirstname(),
                            'lastNameDelegate' => $delegationToBuyer->getLastname(),
                        ]
                    );
                    $emailService->sendEmailMessage(
                        MailService::DELEGATION_REMOVED_BY_MANAGER_TO_DELEGATE,
                        $delegationToBuyer->getLocale(),
                        $delegationToBuyer->getEmail(),
                        [
                            'firstName' => $delegationToBuyer->getFirstname(),
                            'lastName' => $delegationToBuyer->getLastname(),
                            'firstNameManager' => $buyerWhoDelegating->getFirstname(),
                            'lastNameManager' => $buyerWhoDelegating->getLastname(),
                        ]
                    );
                }

                if (empty($delegates)) {
                    return $this->render(
                        '@OpenFront/shared/delegation_empty.html.twig'
                    );

                }
                return new Response('Found', Response::HTTP_ACCEPTED);
            }

            return new Response('No content', Response::HTTP_NO_CONTENT);
        }

        return new Response('Form Error', Response::HTTP_BAD_REQUEST);
    }
}
