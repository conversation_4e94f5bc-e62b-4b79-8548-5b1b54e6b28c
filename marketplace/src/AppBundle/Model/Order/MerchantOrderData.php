<?php

namespace AppBundle\Model\Order;

use DateTimeImmutable;

final class MerchantOrderData
{
    /**
     * @var string|null
     */
    private $orderNumber;

    /**
     * @var string
     */
    private $supplier;

    /**
     * @var string
     */
    private $orderDate;

    /**
     * @var string
     */
    private $cartReference;

    /**
     * @var string
     */
    private $categories;

    /**
     * @var string
     */
    private $beneficiary;

    /**
     * @var string
     */
    private $amount;

    /**
     * @var string $organisation
     */
    private $organisation;

    /**
     * @var string $siteName
     */
    private $siteName;


    public function getOrderNumber(): ?string
    {
        return $this->orderNumber;
    }

    public function setOrderNumber(?string $orderNumber): self
    {
        $this->orderNumber = $orderNumber;
        return $this;
    }

    public function getSupplier(): string
    {
        return $this->supplier;
    }

    public function setSupplier(string $supplier): self
    {
        $this->supplier = $supplier;
        return $this;
    }

    public function getOrderDate(): string
    {
        return $this->orderDate;
    }

    public function setOrderDate(string $orderDate): self
    {
        $this->orderDate = $orderDate;
        return $this;
    }

    public function getCartReference(): string
    {
        return $this->cartReference;
    }

    public function setCartReference(string $cartReference): self
    {
        $this->cartReference = $cartReference;
        return $this;
    }

    public function getCategories(): string
    {
        return $this->categories;
    }

    public function setCategories(string $categories): self
    {
        $this->categories = $categories;
        return $this;
    }

    public function getBeneficiary(): string
    {
        return $this->beneficiary;
    }

    public function setBeneficiary(string $beneficiary): self
    {
        $this->beneficiary = $beneficiary;
        return $this;
    }

    public function getAmount(): string
    {
        return $this->amount;
    }

    public function setAmount(string $amount): self
    {
        $this->amount = $amount;
        return $this;
    }

    public function getOrganisation(): string
    {
        return $this->organisation;
    }

    public function setOrganisation($organisation): self
    {
        is_null($organisation) ? $organisation = '' : null;
        $this->organisation = $organisation;
        return $this;
    }

    /**
     * @param string $siteName
     */
    public function setSiteName(string $siteName): self
    {
        $this->siteName = $siteName;
        return $this;
    }

    public function getSiteName(): string
    {
        return $this->siteName;
    }
}
