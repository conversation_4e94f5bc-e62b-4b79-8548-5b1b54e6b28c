<?php

namespace Open\BackBundle\Form;



use Doctrine\ORM\EntityRepository;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\CheckboxType;
use Symfony\Component\Form\Extension\Core\Type\DateType;
use Symfony\Component\Form\Extension\Core\Type\IntegerType;
use Symfony\Component\Form\Extension\Core\Type\SubmitType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Translation\TranslatorInterface;


class FilterInvitationType extends AbstractType
{

    public function buildForm(FormBuilderInterface $builder, array $options)
    {
        $builder
            ->add(
                'id',
                TextType::class
            )
            ->add(
                'companyName',
                TextType::class
            )
            ->add(
                'firstname',
                TextType::class
            )
            ->add(
                'lastname',
                TextType::class
            )
            ->add(
                'email',
                TextType::class
            )
            ->add(
                'createdDate',
                TextType::class
            )
            ->add(
                'status',
                ChoiceType::class,
                array(
                    'choices' => array(
                        'back.invitation.status.all' => 'all',
                        'back.invitation.status.invitation' => 'invitation',
                        'back.invitation.status.registration' => 'registration',
                        'back.invitation.status.validation' => 'validation',
                        'back.invitation.status.account_creation' => 'account_creation',
                        'back.invitation.status.account_validation' => 'account_validation',

                    ),
                    'choice_translation_domain' => 'AppBundle',
                )
            )->setMethod('GET');
    }

    public function configureOptions(OptionsResolver $resolver)
    {
        $resolver->setDefaults(
            [
                'csrf_protection' => false
            ]
        );
    }
}
