<?php


namespace Open\IzbergBundle\Model;

use JMS\Serializer\Annotation as JSM;

class ApplicationCategory
{
    /**
     * @JSM\Type("integer")
     */
    private $id = 0;

    /**
     * @return int
     */
    public function getId(): int
    {
        return $this->id;
    }

    /**
     * @param int $id
     */
    public function setId(int $id): void
    {
        $this->id = $id;
    }



}