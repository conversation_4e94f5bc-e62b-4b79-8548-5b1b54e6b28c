<?php
namespace AppBundle\Menu;

use Knp\Menu\FactoryInterface;
use Symfony\Component\DependencyInjection\ContainerAwareInterface;
use Symfony\Component\DependencyInjection\ContainerAwareTrait;

class ProfileMenu implements ContainerAwareInterface
{
    use ContainerAwareTrait;

    public function mainMenu(FactoryInterface $factory, array $options)
    {
        $menu = $factory->createItem('root');

        $menu->addChild('profile.account', array('route' => 'fos_user_profile_edit'));


        // create another menu item
        //$menu->addChild('About Me');

        //$menu['About Me']->setAttribute('dropdown', true);

        // you can also add sub level's to your menu's as follows
        //$menu['About Me']->addChild('Edit profile', array('route' => 'admin.user.list'));



        return $menu;
    }

}