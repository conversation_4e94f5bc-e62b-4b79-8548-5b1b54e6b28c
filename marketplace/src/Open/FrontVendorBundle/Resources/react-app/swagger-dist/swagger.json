{"swagger": "2.0", "info": {"description": "Quote Api Documentation", "version": "1.0.0", "title": ""}, "host": "localhost:8080", "basePath": "/quote-api", "tags": [{"name": "Quote", "description": "Quote API"}], "paths": {"/quote": {"post": {"tags": ["Quote"], "summary": "Create a quote", "description": "Create a quote", "operationId": "createQuote", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "quote", "description": "quote to create ", "required": true, "schema": {"$ref": "#/definitions/Quote"}}], "responses": {"201": {"description": "Created", "schema": {"$ref": "#/definitions/Quote"}}, "400": {"description": "Bad request", "schema": {"$ref": "#/definitions/ApiError"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/ApiError"}}, "503": {"description": "Service Unavailable"}}}}, "/quote/{quoteId}": {"get": {"tags": ["Quote"], "summary": "Find a quote", "description": "Find a quote", "operationId": "findQuote", "produces": ["application/json"], "parameters": [{"in": "path", "name": "quoteId", "description": "Quote identifier to associate", "required": true, "type": "integer", "format": "int64"}], "responses": {"200": {"description": "Success", "schema": {"$ref": "#/definitions/Quote"}}, "400": {"description": "Bad request", "schema": {"$ref": "#/definitions/ApiError"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/ApiError"}}, "503": {"description": "Service Unavailable"}}}}, "/quote/{quoteId}/update": {"put": {"tags": ["Quote"], "summary": "Update a draft or redraft quote", "description": "Update a draft or redraft quote", "operationId": "updateQuote", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "path", "name": "quoteId", "description": "Quote identifier to associate", "required": true, "type": "integer", "format": "int64"}, {"in": "body", "name": "quote", "description": "Quote to update ", "required": true, "schema": {"$ref": "#/definitions/Quote"}}], "responses": {"200": {"description": "Success", "schema": {"$ref": "#/definitions/Quote"}}, "400": {"description": "Bad request", "schema": {"$ref": "#/definitions/ApiError"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/ApiError"}}, "503": {"description": "Service Unavailable"}}}}, "/quote/{quoteId}/send": {"put": {"tags": ["Quote"], "summary": "update the quote and send it to buyer", "description": "Update a quote", "operationId": "sendAction", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "path", "name": "quoteId", "description": "Quote identifier to associate", "required": true, "type": "integer", "format": "int64"}, {"in": "body", "name": "quote", "description": "Quote to update ", "required": true, "schema": {"$ref": "#/definitions/Quote"}}], "responses": {"200": {"description": "Success", "schema": {"$ref": "#/definitions/Quote"}}, "400": {"description": "Bad request", "schema": {"$ref": "#/definitions/ApiError"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/ApiError"}}, "503": {"description": "Service Unavailable"}}}}, "/quote/{quoteId}/refuse": {"put": {"tags": ["Quote"], "summary": "refuse a new or draft quote", "description": "Update a quote", "operationId": "refuseAction", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "path", "name": "quoteId", "description": "Quote identifier to associate", "required": true, "type": "integer", "format": "int64"}, {"in": "body", "name": "quote", "description": "Quote to update ", "required": true, "schema": {"$ref": "#/definitions/RefuseRequest"}}], "responses": {"200": {"description": "Success", "schema": {"$ref": "#/definitions/Quote"}}, "400": {"description": "Bad request", "schema": {"$ref": "#/definitions/ApiError"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/ApiError"}}, "503": {"description": "Service Unavailable"}}}}, "/quote/vendor/{vendorId}": {"get": {"tags": ["Quote"], "summary": "get list of quote for vendor", "description": "get list of quote for vendor", "operationId": "getVendorQuotes", "parameters": [{"in": "path", "name": "vendorId", "description": "Quote identifier to associate", "required": true, "type": "integer", "format": "int64"}], "produces": ["application/json"], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/PaginatedQuote"}}, "400": {"description": "Bad request", "schema": {"$ref": "#/definitions/ApiError"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/ApiError"}}, "503": {"description": "Service Unavailable"}}}}, "/quote/vendor/{vendorId}/unread_count": {"get": {"tags": ["Quote"], "summary": "get array of unread quote count", "description": "get array of unread quote count", "operationId": "getVendorUnread", "parameters": [{"in": "path", "name": "vendorId", "description": "vendor identifier", "required": true, "type": "integer", "format": "int64"}], "produces": ["application/json"], "responses": {"200": {"description": "Read", "schema": {"type": "array", "items": {"type": "integer"}}}, "400": {"description": "Bad request", "schema": {"$ref": "#/definitions/ApiError"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/ApiError"}}, "503": {"description": "Service Unavailable"}}}}}, "definitions": {"User": {"type": "object", "properties": {"id": {"description": "id of the user", "type": "integer", "format": "int64"}, "firstname": {"description": "first name of the user", "type": "integer", "format": "int64"}, "lastname": {"description": "last name of the user", "type": "integer", "format": "int64"}}}, "Merchant": {"type": "object", "properties": {"id": {"description": "id of the Merchant", "type": "integer", "format": "int64"}, "name": {"description": " name of the merchant", "type": "integer", "format": "int64"}}}, "Quote": {"type": "object", "properties": {"buyer": {"$ref": "#/definitions/User"}, "date": {"description": "date of the quote", "type": "string", "format": "date-time"}, "initial_offer_url": {"description": "url of the image", "type": "string"}, "initial_offer_reference": {"description": "reference of the offer", "type": "string"}, "initial_offer_title": {"description": "title of the offer", "type": "string"}, "initial_offer_description": {"description": "description of the image", "type": "string"}, "quote_id": {"description": "Id of the quote", "type": "integer", "format": "int64"}, "quote_items": {"description": "quote item list details", "type": "array", "items": {"$ref": "#/definitions/QuoteItem"}}, "quote_number": {"description": "number of the quote", "type": "string"}, "status": {"description": "status of the quote <p> See enumeration @QuoteStatusType </p>", "type": "string", "enum": ["DRAFT", "VALIDATED"]}, "subject": {"description": "subject", "type": "string"}, "title": {"description": "title", "type": "string"}, "vendor": {"$ref": "#/definitions/Merchant"}}}, "QuoteItem": {"description": "Quote item object", "type": "object", "properties": {"id": {"description": "technical id of quote item", "type": "integer", "format": "int64"}, "reference": {"description": "offer reference", "type": "string"}, "quantity": {"description": "quantity", "type": "integer"}, "total_price": {"description": "total price", "type": "number"}, "unit_price": {"description": "unit price", "type": "number"}, "vat": {"description": "vat for the quote item <p> TO_DO </p>", "type": "string"}}}, "PaginatedQuote": {"description": "Pagination system", "type": "object", "properties": {"current_page": {"description": "current page", "type": "integer"}, "items": {"type": "array", "items": {"$ref": "#/definitions/Quote"}}, "page_count": {"description": " number of page", "type": "integer"}, "items_per_page": {"description": "item per page", "type": "integer"}, "total_item_count": {"description": "total number of item", "type": "integer"}}}, "QuoteStatusType": {"description": "Quote status coding", "type": "string", "enum": ["new", "draft", "redraft", "send", "validated", "cancelled", "refused"]}, "RefuseRequest": {"description": "Refuse Request", "type": "object", "properties": {"quote_id": {"description": "quote id", "type": "integer"}, "reason": {"type": "string", "description": "vendor reason"}}}, "ApiError": {"type": "object", "properties": {"code": {"description": "Error code", "type": "string"}, "label": {"description": "Error label", "type": "string"}}}}}