{% trans_default_domain 'AppBundle' %}
{% for key,review in reviews %}
<div class="merchant-review">
    <div class="font-weight-bold">{{ review.displayName }}</div>
    <div class="flex merchant-review-date">{{ 'review.createdAt'|trans }}  {{ review.createdOn| localizeddate('short', 'none', locale) }} <div class='stars ml-10' data-rating="{{ review.score| default(0) }}"></div></div>
    <div class="merchant-review-content">{{ review.body }}</div>
</div>
{% endfor %}
