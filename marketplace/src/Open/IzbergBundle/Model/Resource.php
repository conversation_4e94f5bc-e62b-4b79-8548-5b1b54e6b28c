<?php

namespace Open\IzbergBundle\Model;

use Symfony\Component\Validator\Constraints as Assert;
use <PERSON><PERSON>\Serializer\Annotation\Type;
use <PERSON><PERSON>\Serializer\Annotation as JSM;

/**
 * This entity can be used where API return a short reference to a resource instead of a full object
 * Class Resource
 * @package Open\IzbergBundle\Entity
 */
class Resource
{
    /**
     * @Type("integer")
     */
    private $id;

    /**
     * @Type("integer")
     */
    private $pk;

    /**
     * @Type("string")
     * @Assert\NotBlank()
     */
    private $resource_uri;

    /**
     * Get resource Id
     * @return integer
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * Set resource Id
     * @param $id
     * @return integer
     */
    public function setId($id)
    {
        return $this->id = $id;
    }

    /**
     * Get ??? (Seems the same as id in most cases)
     * @return integer
     */
    public function getPk()
    {
        return $this->pk;
    }

    /**
     * Get ??? (Seems the same as id in most cases)
     * @param $pk
     * @return integer
     */
    public function setPk($pk)
    {
        return $this->pk = $pk;
    }

    /**
     * Get resource URI
     * @return string
     */
    public function getResourceUri(): ?string
    {
        return $this->resource_uri;
    }

    /**
     * Set resource URI
     * Todo: Should be build only from path and id probably
     * @param $resource_uri
     * @return string
     */
    public function setResourceUri($resource_uri)
    {
        return $this->resource_uri = $resource_uri;
    }
}
