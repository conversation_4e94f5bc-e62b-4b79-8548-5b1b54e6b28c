<?php
namespace Open\IzbergBundle\Api;

use Open\IzbergBundle\Api;
use Open\IzbergBundle\Model\IzbergUser;
use Unirest;

/**
 * Wrapper for Izber User API
 * Class UserApi
 * @package Open\IzbergBundle\Api
 */
class UserApi extends Api
{
    public function  getUri(){
        return 'user';
    }

    public function getItemClass(){
        return IzbergUser::class;
    }

    /**
     * Get user by Id
     * @param $id
     * @return IzbergUser
     */
    public function getUser($id)
    {
        $response = $this->sendApiRequest(
            self::HTTP_GET_OPERATION,
            'user/' . $id . '/'
        );

        return $this->serializer->deserialize($response->raw_body,IzbergUser::class, 'json');
    }
}
