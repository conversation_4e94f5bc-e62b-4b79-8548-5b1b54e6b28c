<?php

namespace Open\IzbergBundle\DependencyInjection;

use Open\IzbergBundle\Api;
use Open\IzbergBundle\Api\ApiClientManager;
use Open\IzbergBundle\Api\ApiConfigurator;
use Symfony\Component\DependencyInjection\ContainerBuilder;
use Symfony\Component\Config\FileLocator;
use Symfony\Component\DependencyInjection\Reference;
use Symfony\Component\HttpKernel\DependencyInjection\Extension;
use Symfony\Component\DependencyInjection\Loader;

/**
 * This is the class that loads and manages your bundle configuration.
 *
 * @link http://symfony.com/doc/current/cookbook/bundles/extension.html
 */
class OpenIzbergExtension extends Extension
{
    /**
     * {@inheritdoc}
     */
    public function load(array $configs, ContainerBuilder $container)
    {
        $configuration = new Configuration();
        $config = $this->processConfiguration($configuration, $configs);

        $loader = new Loader\YamlFileLoader($container, new FileLocator(__DIR__ . '/../Resources/config'));
        $loader->load('services.yaml');

        $container->register(ApiConfigurator::class, ApiConfigurator::class)
            ->addArgument($config)
            ->setFactory([Api\ApiConfiguratorFactory::class, 'createApiConfigurator']);

        $container->register(ApiClientManager::class, ApiClientManager::class)
            ->setConfigurator([new Reference(ApiConfigurator::class), 'configure']);


        $taggedServices = $container->findTaggedServiceIds('izberg.api');

        foreach($taggedServices as $id => $tags) {
            $definition = $container->findDefinition($id);
            $definition
                ->setArguments([
                    new Reference(ApiClientManager::class),
                    new Reference(ApiConfigurator::class),
                    new Reference('logger.service'),
                    new Reference('security.token_storage'),
                    new Reference('session'),
                    new Reference('jms_serializer'),
                    new Reference('AppBundle\Services\SecurityService'),
                    new Reference('AppBundle\Services\IzbergCustomAttributes'),
                ]);
        }
    }
}
