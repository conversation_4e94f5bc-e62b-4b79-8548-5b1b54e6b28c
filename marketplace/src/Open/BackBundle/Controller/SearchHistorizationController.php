<?php

namespace Open\BackBundle\Controller;

use A<PERSON><PERSON><PERSON><PERSON>\Controller\MkoController;
use AppB<PERSON>le\Entity\SearchHistorization;
use AppBundle\Services\CSVService;
use AppBundle\Services\SearchHistorizationService;
use Open\BackBundle\Form\FilterSearchHistorizationType;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Route;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Security;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

class SearchHistorizationController extends MkoController
{
    const TRANSLATION_DOMAIN = 'AppBundle';
    const PAGINATION = 'pagination';
    const LOCALE = 'locale';

    const DATEMIN = 'datemin';
    const DATE = 'date';
    const SEARCHED_TERM = 'searched_term';
    const IS_ANONYMOUS = 'is_anonymous';
    const FILTER = 'filter';
    const NB_HITS = 'nb_hits';

    /**
     * @Route("/search_historization/list", name="admin.search_historization.list")
     * @Security("is_granted('ROLE_ADMIN') or is_granted('ROLE_OPERATOR')")
     * @param Request $request
     *
     * @return Response
     */
    public function list(Request $request, SearchHistorizationService $searchHistorizationService): Response
    {

        $filter = (array) $request->request->get('filter_search_historization');
        if (array_key_exists("datemin", $filter)) {
            $filter["datemin"] = \DateTime::createFromFormat('d-m-Y', $filter["datemin"]);
        }
        if (array_key_exists("datemax", $filter)) {
            $filter["datemax"] = \DateTime::createFromFormat('d-m-Y', $filter["datemax"]);
        }
        $request->setLocale('en');
        $form = $this->createForm(FilterSearchHistorizationType::class, $filter);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $filter = $form->getData();
        }

        return $this->render(
            '@OpenBack/search/search_historization_list.html.twig',
            [
                self::PAGINATION => $searchHistorizationService->getAll(25, $request, $filter),
                'form' => $form->createView(),
                self::LOCALE => 'en',
                'query' => $filter,
            ]
        );
    }

    /**
     * @Route("/search_historization/export", name="search_historization.export")
     * @param Request $request
     *
     * @return Response
     */

    public function exportCSV(Request $request, SearchHistorizationService $service, CSVService $csvService)
    {
        $dateFormat = "d/m/Y H:i:s";
        $form = $this->createForm(FilterSearchHistorizationType::class);
        $form->handleRequest($request);
        if ($form->isSubmitted() && $form->isValid()) {
            $filter = $form->getData();
        } else {
            $filter = [];
            $filter['status'] = 'all';
        }

        $listOfSearch = $service->getAllByFilter($filter);

        /*
        * STEP 1: add custom columns for export
        */
        /** @var SearchHistorization $search */
        foreach ($listOfSearch as &$search) {
            $search->addProperty(self::DATE, $search->getCreatedAt()->format($dateFormat));
            $search->addProperty(self::IS_ANONYMOUS, $search->getIsAnonymous());
            $search->addProperty(self::SEARCHED_TERM, $search->getSearchTerm());
            $search->addProperty(self::FILTER, $search->getFilter());
            $search->addProperty(self::NB_HITS, $search->getNbHits());
        }

        $headers = [
            "back.search_historization.id",
            "back.search_historization.date",
            "back.search_historization.is_anonymous",
            "back.search_historization.searched_term",
            "back.search_historization.filter_label",
            "back.search_historization.nb_hits",
        ];

        $properties = [
            "id",
            self::DATE,
            self::IS_ANONYMOUS,
            self::SEARCHED_TERM,
            self::FILTER,
            self::NB_HITS,
        ];

        /*
         * STEP 3: run the export
         */
        $content = $csvService->objArrayToCSV($listOfSearch, $headers, $properties);

        return new Response($content, 200, [
            'Content-Encoding' => 'UTF-16',
            'Content-Type' => 'application/CSV; charset=UTF-16',
            'Content-Disposition' => 'attachment; filename="search.csv"']);

    }
}
