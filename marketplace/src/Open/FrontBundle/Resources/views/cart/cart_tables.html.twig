{% trans_default_domain 'AppBundle' %}
{% set isCartCreator = true %}
{% set locale = app.session.get("_locale") %}

<div class="cart-buyer-header">
    <section class="cart-buyer-header-label">{{ 'cart.my_cart' | trans | upper }}</section>
    <section class="cart-buyer-header-empty">
        <form id="empty-cart-form" method="post" name="form" action="{{ path('front.cart.empty') }}">
            <input type="hidden" name="form[_token]" value="{{ csrf_token('form') }}"/>
            <input type="hidden" name="form[cartId]" value="{{ cart.id }}"/>
            <button class="" type="submit" name="form[emptyCart]">{{ 'cart.empty.cart'|trans }}</button>
        </form>
    </section>
</div>
{% if multiMerchants %}
    <p class="red bold">
        {{ 'cart.multi_merchant_message'|trans }}
    </p>
{% endif %}
<div data-itemincart="{{ cart.itemsCount }}">
    {% for merchant in cart.merchants %}
        {% include('@OpenFront/cart/cart_seller_table.html.twig') with {'merchant': merchant, 'index': loop.index} %}
    {% endfor %}

    {# ------ TOTAL SECTION ------ #}
    <div class="total-container">

        <div id="table-merchant-total" class="seller-table table-total">
            <section class="label total-label highlighted-solid-red">{{ 'cart.table_label.total'|trans }}</section>
            <section
                    class="total-without-vat highlighted-solid-red">{{ cart.total | price(locale) }} {{ cart.currency }}</section>
        </div>

        {% if risk %}
            <div class="d-flex justify-content-end">
                <div class="cart-gift-container">
                    <div class="cart-gift-background">
                        {% set cartRisk = (cart.risk is null) ? 'null' : (cart.risk) ? 'true' : 'false' %}
                        {% set riskEnabledChecked = (cartRisk is same as ('true')) ? 'checked' : '' %}
                        {% set riskDisabledChecked = (cartRisk is same as ('false')) ? 'checked' : '' %}

                        <p class="bold"> {{ 'cart.table_label.risk.main_comment'|trans}} <span class="cart-required-field">*</span></p>
                        <p><img class="pr-3" src="{{ asset('images/ico-chimie3.svg') }}" alt="" height="27">
                            {{ 'cart.table_label.risk.question'|trans}}
                            <span class="pl-5">
                            <input class="d-none" id="cart-risk-enable" type="radio" name="cartRisk"
                                   value="yes" {{ riskEnabledChecked }} />
                            <span class="mr-3">
                                <label for="cart-risk-enable" class="d-inline-block">{{ 'yes'|trans }}</label>
                            </span>
                            <input class="d-none" id="cart-risk-disable" type="radio" name="cartRisk"
                                   value="no" {{ riskDisabledChecked }} />
                            <span>
                                <label for="cart-risk-disable" class="d-inline-block">{{ 'no'|trans }}</label>
                            </span>
                        </span>
                        </p>
                    </div>
                    <div class="cart-gift-padding"></div>
                </div>
            </div>
        {% endif %}

        <div class="d-flex justify-content-end">
            <div class="cart-gift-container">
                <div class="cart-gift-background">
                    {% set cartGift = (cart.gift is null) ? 'null' : (cart.gift) ? 'true' : 'false' %}
                    {% set giftEnabledChecked = (cartGift is same as ('true')) ? 'checked' : '' %}
                    {% set giftDisabledChecked = (cartGift is same as ('false')) ? 'checked' : '' %}

                    <p class="bold"> {{ 'cart.table_label.gift.main_comment'|trans}} <span class="cart-required-field">*</span></p>
                    <p>
                        <img class="pr-3" src="{{ asset('images/gift.svg') }}" alt="" height="27">
                        {{ 'cart.table_label.gift.question'|trans}}
                        <span class="pl-5">
                            <input class="d-none" id="cart-gift-enable" type="radio" name="cartGift"
                                   value="yes" {{ giftEnabledChecked }} />
                            <span class="mr-3">
                                <label for="cart-gift-enable" class="d-inline-block">{{ 'yes'|trans }}</label>
                            </span>
                            <input class="d-none" id="cart-gift-disable" type="radio" name="cartGift"
                                   value="no" {{ giftDisabledChecked }} />
                            <span>
                                <label for="cart-gift-disable" class="d-inline-block">{{ 'no'|trans }}</label>
                            </span>
                        </span>
                    </p>

                </div>
                <div class="cart-gif-warning">
                    {{ 'cart.table_label.gift.warning'|trans }}
                </div>
                <div class="cart-gift-padding">
                    <p>{{ 'cart.table_label.gift.rule_1'|trans({'%link%' : asset('docs/eRegister_grouprules_20200504_0257.pdf')})|raw}}</p>
                    <p>{{ 'cart.table_label.gift.rule_2'|trans({'%link%' : 'https://e-registercompliance-cid.ep.corp.local/Home/FirstPage'})|raw}}</p>
                </div>
            </div>
        </div>

        <div class="cart-actions">
            <div class="d-flex justify-content-between align-items-end">
                <div class="back-shopping"><a href="{{ path("homepage") }}"><i
                                class="arrow left"></i>{{ 'cart.buttons.continue_shopping'|trans }}</a></div>
                <div class="cart-validation-btn">
                    {% if hasEnabledManager %}
                        {% if cart.canCheckout %}
                            <form id="cart-submit-form" method="post" action="{{ path('front.cart.checkout') }}" name="form">
                                <input type="hidden" name="form[cartId]" value="{{ cart.id }}"/>
                                <input type="hidden" name="form[_token]" value="{{ csrf_token('form') }}"/>

                                <div class="tooltip-total" style="white-space: nowrap">
                                    <a href="#anchorCheckout">
                                        <button class="return btn-red" type="submit" title="" name="form[checkoutCart]">
                                            {{ 'cart.table_label.validate'|trans|upper }}
                                        </button>
                                    </a>
                                    <div class="tooltiptext">
                                        {{ 'cart.buttons.checkout_tooltip' | trans }}
                                    </div>
                                </div>
                            </form>
                        {% else %}
                            {% if cart.totalLimitOver %}
                                <p class="red">
                                    {{ 'cart.total_is_over'|trans({'%amount%': 2000, '%currency%': cart.currency}) }}
                                </p>
                            {% endif %}
                        {% endif %}
                    {% else %}
                        <p class="red">
                            {{ 'cart.no_manager_active'|trans }}
                        </p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

{% block javascripts %}

    <script src="{{ asset('js/rater.min.js') }}"></script>
    <script type="text/javascript">
        $('.starrr').each(function () {
            var rating = $(this).data('rating');
            $(this).rate({
                rating: rating,
                max_value: 5,
                step_size: 0.5,
                readonly: true,
                initial_value: rating,
            });
        });
        var total = $('#total-price').text();

        $('#delivery-type').on('focusin', function () {
            total = $('#total-price').text();
        });

    </script>
    <script type="text/javascript">
        var cartEmpty = {
            init: function () {
                cartEmpty.vars = {
                    form: document.getElementById("empty-cart-form"),
                    cartQuoteMessage: '{{ 'cart.remove.modal.message' | trans }}',
                    cartContainsQuotedElements: '{{ cartContainsQuotedElements }}'
                };

                cartEmpty.funcs = {
                    onConfirm : function () {
                        cartEmpty.vars.form.submit();
                    },
                    onCancel: function () {

                    }
                }

                cartEmpty.vars.form.onsubmit = function (event) {
                    event.preventDefault();
                    if (cartEmpty.vars.cartContainsQuotedElements) {
                        window.UI.Modal.confirm('', cartEmpty.vars.cartQuoteMessage, cartEmpty.funcs.onConfirm, cartEmpty.funcs.onCancel);
                    } else {
                        cartEmpty.vars.form.submit();
                    }
                }
            }
        };
        var cartSubmit = {
            init: function () {
                cartSubmit.vars = {
                    form: $('#cart-submit-form'),
                    contactElement: $('#shipping-address [name*="[contact]"]').eq(0),
                    phoneElement: $('#shipping-address [name*="[phone]"]').eq(0),
                    button: $('[href="#anchorCheckout"] button'),
                    tooltip: $('.tooltip-total .tooltiptext'),
                    addressCheck: $('.shipping-address-check-btn'),
                    shippingAddressChecked: false
                };

                cartSubmit.validators = [];

                // checkout button management
                cartSubmit.vars.button.on({
                    'cartGift:enable': cartSubmit.buttonControl,
                    'cartGift:disable': cartSubmit.buttonControl,
                    'cartGift:unSelect': cartSubmit.buttonControl,
                    'itemGift:enable': cartSubmit.buttonControl,
                    'itemGift:disable': cartSubmit.buttonControl,
                    'merchantComment:write': cartSubmit.buttonControl,
                    {% if risk %}
                    'cartRisk:enable': cartSubmit.buttonControl,
                    'cartRisk:disable': cartSubmit.buttonControl,
                    {% endif %}
                });

                cartSubmit.vars.addressCheck.on('click', function () {
                    cartSubmit.vars.shippingAddressChecked = true;
                    cartSubmit.buttonControl();
                });

                cartSubmit.vars.form.on("submit", function () {
                    window.UI.Modal.showLoading();
                });
            },

            addValidator: function (validator) {
                cartSubmit.validators.push(validator);
            },

            buttonControl: function (e, popup = false) {
                // if has contact and phone and cart gift ok
                // then we can enable
                // otherwise we cannot

                // todo missing gift check
                // todo put cart gift logic into this twig file

                if (
                    cartSubmit.vars.contactElement.val().length
                    && cartSubmit.vars.phoneElement.val().length
                ) {

                    let result = true;

                    cartSubmit.validators.forEach(
                        function (validator) {
                            if (!validator()) {
                                result = false;
                            }
                        }
                    );
                    let nbMerchantUnactive = $('.merchant_not_active').length;
                    let nbOfferUnavailable = $('.offer_not_available').length;
                    if (result === true && nbOfferUnavailable==0 && nbMerchantUnactive==0 && cartSubmit.vars.shippingAddressChecked) {
                        cartSubmit.enable();
                        return true;
                    }

                    cartSubmit.disable();
                    return false;
                }

                if (e) {
                    e.preventDefault();
                }

                cartSubmit.disable();
                if (popup) {
                    window.UI.Modal.alert("{{ 'cart.recipient_mandatory'|trans }}");
                }

                return false;
            },

            trigger: function (event) {
                cartSubmit.vars.button.trigger(event);
            },

            enable: function () {
                cartSubmit.vars.button.removeAttr('disabled').css('opacity', 1);
                cartSubmit.vars.tooltip.css('visibility', 'hidden');
                cartSubmit.vars.shippingAddressChecked ? cartSubmit.vars.addressCheck.addClass('disabled-btn') : cartSubmit.vars.addressCheck.removeClass('disabled-btn');

            },

            disable: function () {
                cartSubmit.vars.button.attr('disabled', 'disabled').css('opacity', 0.25);
                cartSubmit.vars.tooltip.css('visibility', 'visible');
                cartSubmit.vars.shippingAddressChecked ? cartSubmit.vars.addressCheck.addClass('disabled-btn') : cartSubmit.vars.addressCheck.removeClass('disabled-btn');
            }
        };


        (function ($) {
            $.fn.cartItemGift = function () {
                var cartId = {{ cart.id }};

                // when cart item is marked has gift
                // then notify cartGift
                this.on('change', function () {
                    var checkbox = $(this);
                    if (checkbox.is(':checked')) {
                        enableGift(checkbox);
                    } else {
                        disableGift(checkbox);
                    }
                });

                this.on('cartGift:disable', function () {
                    var checkbox = $(this);
                    checkbox.prop('checked', false);
                    disableGift(checkbox);
                });

                function enableGift(checkbox) {
                    setItemGift(checkbox, true);
                }

                function disableGift(checkbox) {
                    setItemGift(checkbox, false);
                }

                function setItemGift(checkbox, enable) {
                    var itemId = checkbox.attr('name').replace('cartItemGift[', '').replace(']', '');

                    var formData = new FormData();
                    formData.append('form[cartId]', cartId);
                    formData.append('form[itemId]', itemId);
                    if (enable) {
                        formData.append('form[enable]', 1);
                    }
                    formData.append('form[_token]', '{{ csrf_token('form') }}');

                    if (enable) {
                        checkbox.trigger('itemGift:enable');
                    } else {
                        checkbox.trigger('itemGift:disable');
                    }

                    $.ajax({
                        type: 'POST',
                        url: '{{ path('front.cart.item-gift-enable') }}',
                        data: formData,
                        processData: false,
                        contentType: false
                    });
                }
            };
        })(jQuery);

        /**
         * cartGift allows to enable or disable cart gift on current metacart
         * on element click an ajax call is triggered
         * configuration settings:
         *  - enable: When set to true the function enable the cart gift if set to false the function disable the cart gift
         */
        var cartGift = {

            // cart gift selection has 3 states:
            // - unselected (no radio button selected)
            // - enabled (yes radio button selected)
            // - disabled (no radio button selected)

            // when cart gift is unselected
            // then checkout button is disabled

            // when cart gift is disabled
            // then checkout button is enabled

            // when cart gift is enabled
            // and at least one item is selected as gift
            // then checkout button is enabled

            // when cart gift is enabled
            // and no item is selected as gift
            // then checkout button is disabled

            init: function () {
                cartGift.vars = {
                    enabled: {{ cartGift }},
                    enableCartGift: $('#cart-gift-enable'),
                    disableCartGift: $('#cart-gift-disable'),
                    checkboxesSections: $('section.gift'),
                    warning: $('.cart-gif-warning')
                };

                cartGift.setup();
            },

            setup: function () {

                cartSubmit.addValidator(function () {
                    return {{ not merchantsOrdersAmountNotEnough }};
                });
                cartSubmit.addValidator(cartGift.valid);

                // init cartGiftItems
                cartGift.initGiftItems();

                // cart gift triggers
                cartGift.vars.enableCartGift.on('click.cartGift', function (e) {
                    cartGift.enableCartGift();
                });

                cartGift.vars.disableCartGift.on('click.cartGift', function (e) {
                    cartGift.disableCartGift();
                });

                // cart checkboxes management
                cartGift.vars.checkboxesSections.on({
                    'cartGift:enable': function () {
                        // show checkboxes
                        cartGift.vars.checkboxesSections.removeClass('hidden');
                    },
                    'cartGift:disable': function () {
                        // hide checkboxes
                        cartGift.vars.checkboxesSections.addClass('hidden');
                    },
                    'itemGift:enable': function () {
                        // enable or disable checkout button
                        cartSubmit.trigger('itemGift:enable');
                    },
                    'itemGift:disable': function () {
                        // enable or disable checkout button
                        cartSubmit.trigger('itemGift:disable');
                    }
                });

                // initialisation of cartGift enabled property
                cartGift.changeEnabled(cartGift.vars.enabled);
            },

            valid: function () {
                cartGift.vars.warning.hide();
                if (cartGift.isEnabled() === null) {
                    return false;
                }

                if (cartGift.isEnabled() && !cartGift.hasAtLeastOneGift()) {
                    cartGift.vars.warning.show();
                    return false;
                }

                return true;
            },

            isEnabled: function () {
                return cartGift.vars.enabled;
            },

            enableCartGift: function () {
                cartGift.setCartGift(true);
            },

            disableCartGift: function () {
                cartGift.vars.checkboxesSections.find('input:checked').trigger('cartGift:disable');
                cartGift.setCartGift(false);
            },

            hasAtLeastOneGift: function () {
                var nbGift = cartGift.vars.checkboxesSections.find('input:checked').length;
                return (nbGift > 0);
            },

            initGiftItems: function () {
                cartGift.vars.checkboxesSections.find('input').cartItemGift();
            },

            changeEnabled: function (enable) {
                cartGift.vars.enabled = enable;
                if (enable === true) {
                    cartGift.vars.checkboxesSections.trigger('cartGift:enable');
                    cartSubmit.trigger('cartGift:enable');
                    return;
                }

                if (enable === false) {
                    cartGift.vars.checkboxesSections.trigger('cartGift:disable');
                    cartSubmit.trigger('cartGift:disable');
                    return;
                }

                cartGift.vars.checkboxesSections.trigger('cartGift:unSelecte');
                cartSubmit.trigger('cartGift:unSelect');

            },

            setCartGift: function (enable) {
                var formData = new FormData();
                formData.append('form[cartId]', {{ cart.id }});
                if (enable) {
                    formData.append('form[enable]', 1); // todo replace by radio button yes/no
                }
                formData.append('form[_token]', '{{ csrf_token('form') }}');

                cartGift.changeEnabled(enable);

                $.ajax({
                    type: 'POST',
                    url: '{{ path('front.cart.gift-enable') }}',
                    data: formData,
                    processData: false,
                    contentType: false
                });
            }
        };

        var merchantComment = {
            init: function () {
                merchantComment.vars = {
                    comments: $('.supplier-comment textarea'),
                    mandatoryComments: $('.supplier-comment.mandatory textarea'),
                    writingTimer: 0
                };

                cartSubmit.addValidator(merchantComment.valid);
                merchantComment.vars.comments.on('keyup', merchantComment.writing)
            },

            valid: function () {
                return merchantComment.mandatoryCommentsFilled();
            },

            writing: function () {
                var $this = $(this);
                var merchantId = $this.attr('name').replace('merchantComment-', '');
                var comment = $this.val();
                clearTimeout(merchantComment.vars.writingTimer);
                merchantComment.vars.writingTimer = setTimeout(function () {
                    merchantComment.writeComment(merchantId, comment);
                }, 1000);
            },

            writeComment: function (merchantId, comment) {
                var formData = new FormData();
                formData.append('form[cartId]', {{ cart.id }});
                formData.append('form[merchantId]', merchantId);
                formData.append('form[comment]', comment);
                formData.append('form[_token]', '{{ csrf_token('form') }}');

                $.ajax({
                    type: 'POST',
                    url: '{{ path('front.cart.merchant-comment') }}',
                    data: formData,
                    processData: false,
                    contentType: false
                });

                cartSubmit.trigger('merchantComment:write');
            },

            mandatoryCommentsFilled: function () {
                var filled = true;
                merchantComment.vars.mandatoryComments.each(function () {
                    var $this = $(this);
                    var comment = $this.val().trim();

                    $this.removeClass('is-invalid');
                    if (!(comment.length > 0)) {
                        filled = false;
                        $this.addClass('is-invalid');
                    }
                });

                return filled;
            }
        };
        {% if risk %}
        var cartRisk = {

            init: function () {
                let maxWidth = $(".cart-gift-container").width();
                $(".cart-gift-container").each(function(){
                    let this_width = $(this).outerWidth();
                    if (this_width > maxWidth) maxWidth = this_width;
                });
                $(".cart-gift-container").css("width", maxWidth+"px");
                cartRisk.vars = {
                    enabled: {{ cartRisk }},
                    enableCartRisk: $('#cart-risk-enable'),
                    disableCartRisk: $('#cart-risk-disable')
                };

                cartRisk.setup();
            },

            setup: function () {
                cartSubmit.addValidator(cartRisk.valid);

                // cart risk triggers
                cartRisk.vars.enableCartRisk.on('click.cartRisk', function (e) {
                    cartRisk.enableCartRisk();
                });

                cartRisk.vars.disableCartRisk.on('click.cartRisk', function (e) {
                    cartRisk.disableCartRisk();
                });

                // initialisation of cartRisk enabled property
                cartRisk.changeEnabled(cartRisk.vars.enabled);
            },

            valid: function () {
                if (cartRisk.isEnabled() === null) {
                    return false;
                }

                if (cartRisk.isEnabled()) {
                    return true;
                }
                window.UI.Modal.alert("{{ 'cart.table_label.risk.warning'|trans }}");
                return false;

            },

            isEnabled: function () {
                return cartRisk.vars.enabled;
            },

            enableCartRisk: function () {
                cartRisk.setCartRisk(true);
            },

            disableCartRisk: function () {
                cartRisk.setCartRisk(false);
            },

            changeEnabled: function (enable) {
                cartRisk.vars.enabled = enable;
                if (enable === true) {
                    cartSubmit.trigger('cartRisk:enable');
                    return;
                }
                if (enable === false) {
                    cartSubmit.trigger('cartRisk:disable');
                    return;
                }
            },

            setCartRisk: function (enable) {
                var formData = new FormData();
                formData.append('form[cartId]', {{ cart.id }});
                if (enable) {
                    formData.append('form[enable]', 1); // todo replace by radio button yes/no
                }
                formData.append('form[_token]', '{{ csrf_token('form') }}');

                cartRisk.changeEnabled(enable);

                $.ajax({
                    type: 'POST',
                    url: '{{ path('front.cart.risk-enable') }}',
                    data: formData,
                    processData: false,
                    contentType: false
                });
            }
        };
        {% endif %}

        $(document).ready(function () {
            cartEmpty.init();
            cartSubmit.init();
            cartGift.init();
            {% if risk %}
            cartRisk.init();
            {% endif %}
            merchantComment.init();
            cartSubmit.buttonControl();
        });
    </script>

{% endblock %}
