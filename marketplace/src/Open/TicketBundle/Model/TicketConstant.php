<?php
/**
 * Created by PhpStorm.
 * User: PPH04712
 * Date: 07/02/2018
 * Time: 11:07
 */

namespace Open\TicketBundle\Model;


class TicketConstant
{

    const FRONT_BASE = "OpenFrontBundle::base.html.twig";

    const TICKET_ADMIN = 0;
    const TICKET_BUYER = 1;
    const TICKET_ANONYMOUS = 3;

    const BASES = [
        self::TICKET_ADMIN                  => '@OpenBack/base.html.twig',
        self::TICKET_BUYER                  => 'OpenFrontBundle::menu/buyer_menu.html.twig',
        self::TICKET_ANONYMOUS              => self::FRONT_BASE
    ];

    const ADD_ROUTES = [
        self::TICKET_ADMIN                  => 'admin.ticket.create',
        self::TICKET_BUYER                  => 'buyer.ticket.create',
    ];

    const EDIT_ROUTES =[
        self::TICKET_ADMIN                  => 'admin.ticket.edit',
        self::TICKET_BUYER                  => 'buyer.ticket.edit',
        self::TICKET_ANONYMOUS              => 'anonymous.ticket.edit',
    ];

	const LIST_ROUTES =[
		self::TICKET_ADMIN                  => 'admin.ticket.list',
		self::TICKET_BUYER                  => 'buyer.ticket.list',
	];

    const STATUS_INVALID = 0;
    const STATUS_NEW = 9;
    const STATUS_OPEN = 10;
    const STATUS_OPERATOR_RESPONDED = 11;
    const STATUS_USER_RESPONDED = 12;
    /*const STATUS_IN_PROGRESS = 11;
    const STATUS_INFORMATION_REQUESTED = 12;
    const STATUS_ON_HOLD = 13;*/
    const STATUS_RESOLVED = 14;
    const STATUS_CLOSED = 15;

    const STATUSES = [
        self::STATUS_INVALID               => 'STATUS_INVALID',
        self::STATUS_NEW                   => 'STATUS_NEW',
        self::STATUS_OPEN                  => 'STATUS_OPEN',
        self::STATUS_OPERATOR_RESPONDED    => 'STATUS_OPERATOR_RESPONDED',
        self::STATUS_USER_RESPONDED    	   => 'STATUS_USER_RESPONDED',
        //self::STATUS_IN_PROGRESS           => 'STATUS_IN_PROGRESS',
        //self::STATUS_INFORMATION_REQUESTED => 'STATUS_INFORMATION_REQUESTED',
        //self::STATUS_ON_HOLD               => 'STATUS_ON_HOLD',
        self::STATUS_RESOLVED              => 'STATUS_RESOLVED',
        self::STATUS_CLOSED                => 'STATUS_CLOSED',
    ];


}