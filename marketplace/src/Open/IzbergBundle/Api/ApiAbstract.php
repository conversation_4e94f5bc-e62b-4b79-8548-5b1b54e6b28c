<?php

namespace Open\IzbergBundle\Api;

use Doctrine\Common\Annotations\AnnotationReader;
use Generator;
use Open\IzbergBundle\ApiInterface;
use Open\IzbergBundle\Dto\Request;
use Open\IzbergBundle\Model\IzbergResponse;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\PropertyInfo\Extractor\PhpDocExtractor;
use Symfony\Component\Serializer\Encoder\JsonEncoder;
use Symfony\Component\Serializer\Mapping\Factory\ClassMetadataFactory;
use Symfony\Component\Serializer\Mapping\Loader\AnnotationLoader;
use Symfony\Component\Serializer\NameConverter\CamelCaseToSnakeCaseNameConverter;
use Symfony\Component\Serializer\Normalizer\ArrayDenormalizer;
use Symfony\Component\Serializer\Normalizer\DateTimeNormalizer;
use Symfony\Component\Serializer\Normalizer\ObjectNormalizer;
use Symfony\Component\Serializer\Serializer;
use Symfony\Component\Serializer\SerializerInterface;
use Symfony\Contracts\HttpClient\Exception\ClientExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\RedirectionExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\ServerExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\TransportExceptionInterface;
use Symfony\Contracts\HttpClient\HttpClientInterface;

abstract class ApiAbstract implements ApiInterface, LoggerAwareInterface
{
    public const HEADER_ACCEPT_LANGUAGE = 'Accept-Language';
    public const LOG_IZBERG_API_ERROR = "IZBERG_API_ERROR";
    public const IZBERG_ATTRIBUTE_NOT_FOUND = 'IZBERG_ATTRIBUTE_NOT_FOUND';
    protected string $applicationId;

    protected LoggerInterface $logger;
    protected SerializerInterface $serializer;
    protected HttpClientInterface $httpClient;
    protected string $secretKey;

    public function __construct()
    {
        $this->initSerializer();
    }

    public function setHttpClient(HttpClientInterface $httpClient)
    {
        $this->httpClient = $httpClient;
    }

    public function setApplicationId(string $applicationId)
    {
        $this->applicationId = $applicationId;
    }

    /**
     * @return string
     */
    public function getApplicationId(): string
    {
        return $this->applicationId;
    }

    private function initSerializer()
    {
        $classMetadataFactory = new ClassMetadataFactory(new AnnotationLoader(new AnnotationReader()));
        $objectNormalizer = new ObjectNormalizer(
            $classMetadataFactory,
            new CamelCaseToSnakeCaseNameConverter(),
            null,
            new PhpDocExtractor()
        );
        $arrayDeNormalizer = new ArrayDenormalizer();
        $dateNormalizer = new DateTimeNormalizer(['datetime_format' => 'Y-m-d\TH:i:s.uP']);
        $jsonEncoder = new JsonEncoder();
        $this->serializer = new Serializer([$dateNormalizer, $arrayDeNormalizer, $objectNormalizer], [$jsonEncoder]);
    }

    /**
     * @param string $route
     * @param array $query
     * @param string $objectClass
     * @param int $limit
     * @param int $offset
     * @return Generator
     * @throws ClientExceptionInterface
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws TransportExceptionInterface
     */
    protected function fetchAll(
        string $route,
        array $query,
        string $objectClass,
        int $limit = 5,
        int $offset = 0
    ): Generator {
        do {
            $defaultQuery = [
                'limit' => $limit,
                'offset' => $offset,
            ];

            $queryToSend = $query + $defaultQuery;
            $content = $this->get((new Request($route))->setQuery($queryToSend));

            /** @var IzbergResponse $izbergResponse */
            $izbergResponse = $this->serializer->deserialize($content, $objectClass, 'json');

            foreach ($izbergResponse->getObjects() as $object) {
                yield $object;
            }

            $limit = $izbergResponse->getMeta()->getLimit();
            $offset = $izbergResponse->getMeta()->getOffset() + $limit;
        } while ($izbergResponse->getMeta()->getNext());
    }

    /**
     * @param string $route
     * @param array $query
     * @param string $objectClass
     * @param int $limit
     * @param int $offset
     * @return array
     * @throws ApiException
     */
    protected function fetchAllQuick(
        string $route,
        array $query,
        string $objectClass,
        int $limit = 100,
        int $offset = 0
    ): array {

        $result = [];
        do {
            $defaultQuery = [
                'limit' => $limit,
                'offset' => $offset,
            ];

            $queryToSend = $query + $defaultQuery;
            $content = $this->get((new Request($route))->setQuery($queryToSend));
            /** @var IzbergResponse $izbergResponse */
            $izbergResponse = $this->serializer->deserialize($content, $objectClass, 'json');

            foreach ($izbergResponse->getObjects() as $object) {
                $result[] = $object;
            }

            $limit = $izbergResponse->getMeta()->getLimit();
            $offset = $izbergResponse->getMeta()->getOffset() + $limit;
        } while ($izbergResponse->getMeta()->getNext());
        return $result;
    }

    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }

    /**
     * @return string
     */
    public function getSecretKey(): string
    {
        return $this->secretKey;
    }

    /**
     * @param string $secretKey
     * @return ApiAbstract
     */
    public function setSecretKey(string $secretKey): ApiAbstract
    {
        $this->secretKey = $secretKey;
        return $this;
    }


    public function post(Request $request): string
    {
        $request->setMethod(Request::POST);
        return $this->sendRequest($request);
    }

    public function put(Request $request): string
    {
        $request->setMethod(Request::PUT);
        return $this->sendRequest($request);
    }

    public function get(Request $request): string
    {
        $request->setMethod(Request::GET);
        $request->setReturnData(false);
        return $this->sendRequest($request);
    }

    public function patch(Request $request): string
    {
        $request->setMethod(Request::PATCH);
        return $this->sendRequest($request);
    }

    protected function delete(Request $request): string
    {
        $request->setMethod(Request::DELETE);
        return $this->sendRequest($request);
    }

    /**
     * @throws ApiException
     */
    private function sendRequest(Request $request): string
    {
        $options = [];
        if (in_array($request->getMethod(), [Request::PATCH, Request::POST])) {
            $options ["json"] = $request->getData();
        }
        $options ["headers"] = $request->getHeader();
        $options ["query"] = $request->getQuery();

        if ($request->isReturnData()) {
            if (!isset($options["query"])) {
                $options["query"] = [];
            }
            $options["query"]['return_data'] = 'true';
        }

        try {
            $response = $this->httpClient->request($request->getMethod() ?? 'POST', $request->getUrl(), $options);

            $content = $response->getContent(false);

            try {
                $content = $response->getContent();
            } catch (ClientExceptionInterface $ex) {
                if ($content !== null && trim($content) !== "") {
                    $message = $content;
                } else {
                    $message = $ex->getMessage();
                }
                throw new ApiException(sprintf("izberg return this error:%s", $message), intval($ex->getCode()));
            }

            return $content;
        } catch (
            TransportExceptionInterface |
            ServerExceptionInterface |
            RedirectionExceptionInterface $exception
        ) {
            $this->logger->error($exception->getMessage());
            throw new ApiException("technical error:" . $exception->getMessage());
        }
    }


    protected function getLogger()
    {
        return $this->logger;
    }
}
