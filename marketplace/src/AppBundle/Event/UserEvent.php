<?php

namespace AppBundle\Event;

use AppBundle\Entity\User;
use Symfony\Contracts\EventDispatcher\Event;

class UserEvent extends Event
{
    public const USER_CREATED = 'USER_CREATED';
    public const USER_ACTIVATED = 'USER_ACTIVATED';
    public const USER_DEACTIVATED = 'USER_DEACTIVATED';
    public const USER_PASSWORD_CHANGED = 'USER_PASSWORD_CHANGED';
    public const USER_PROMOTED = 'USER_PROMOTED';
    public const USER_DEMOTED = 'USER_DEMOTED';

    public User $user;
    public string $eventName;

    public function __construct(User $user, string $eventName)
    {
        $this->user = $user;
        $this->eventName = $eventName;
    }
}
