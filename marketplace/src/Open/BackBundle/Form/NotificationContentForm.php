<?php
/**
 * Created by PhpStorm.
 * User: PPH04712
 * Date: 29/01/2018
 * Time: 10:07
 */

namespace Open\BackBundle\Form;


use AppBundle\Entity\NodeContent;
use FOS\CKEditorBundle\Form\Type\CKEditorType;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\HiddenType;
use Symfony\Component\Form\Extension\Core\Type\SubmitType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

class NotificationContentForm extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options)
    {

        // only the value can be changed from this form
        $builder
            ->add('id', HiddenType::class)
            ->add('lang', HiddenType::class)
            ->add(
                'title',
                null,
                array(
                    'label' => 'back.notification.edit.title'
                )
            )
            ->add(
                'body',
                CKEditorType::class,
                array(
                    'label' => 'back.notification.edit.body'
                )
            )->add('save',
                SubmitType::class);


    }

    public function configureOptions(OptionsResolver $resolver)
    {
        $resolver->setDefaults([
            'translation_domain' => 'AppBundle',
            'data_class' => NodeContent::class,
            'validation_groups' => 'page'
        ]);
    }

}
