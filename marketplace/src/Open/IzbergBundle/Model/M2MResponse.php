<?php

namespace Open\IzbergBundle\Model;

class M2MResponse
{
    private string $accessToken;
    private int $expiresIn;

    public function __construct(
        string $accessToken,
        int    $expiresIn
    )
    {
        $this->accessToken = $accessToken;
        $this->expiresIn = $expiresIn;
    }

    /**
     * @return string
     */
    public function getAccessToken(): string
    {
        return $this->accessToken;
    }

    /**
     * @param string $accessToken
     * @return M2MResponse
     */
    public function setAccessToken(string $accessToken): M2MResponse
    {
        $this->accessToken = $accessToken;
        return $this;
    }


    /**
     * @return int
     */
    public function getExpiresIn(): int
    {
        return $this->expiresIn;
    }

    /**
     * @param int $expiresIn
     * @return M2MResponse
     */
    public function setExpiresIn(int $expiresIn): M2MResponse
    {
        $this->expiresIn = $expiresIn;
        return $this;
    }
}
