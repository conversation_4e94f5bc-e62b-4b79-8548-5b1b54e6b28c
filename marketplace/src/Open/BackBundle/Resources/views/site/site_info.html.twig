{% extends '@OpenBack/base.html.twig' %}
{% trans_default_domain "AppBundle" %}
{% block body %}

    <div style="width:100%; padding: 0 0 20px 5px">
        <a href="{{ path('admin.site.list', {'qualifier': 'all'}) }}" style="display:flex; align-items: center; color: #9600FF">
            <div style="width: 10px; height: 10px; border: solid; border-color: #9600FF; border-width: 0 2px 2px 0;transform: rotate(135deg); margin-right: 5px"></div>
            {{ 'back.back_to_list' | trans }}
        </a>
    </div>

    <div style="display: flex; justify-content: space-between; align-items: flex-end; margin-bottom: 20px;">
        <span style="text-transform: uppercase; font-weight: 600">{{ 'back.site.form.info' | trans }}</span>
        <div>
            {% if not site.enabled %}
                <a href="{{ path('admin.site.activate', { 'id' : site.id})}}"
                   class="btn btn-info"
                   style="margin-right: 10px"
                   role="button" aria-pressed="true">{{ 'back.user.form.activate' | trans }}</a>
            {% endif %}
            <a href="{{ path('admin.site.edit', { 'id' : site.id})}}"
               class="btn btn-info"
               role="button" aria-pressed="true">{{ 'back.site.modification.edit_name' | trans }}</a>
        </div>
    </div>
        <table class="table table-bordered">
            <tr>
                <td style="width: 300px">{{ 'back.site.form.id' | trans }}</td>
                <td> {{ site.id }}</td>
            </tr>
            <tr>
                <td>{{ 'back.site.form.name' | trans }}</td>
                <td> {{ site.name }}</td>
            </tr>
            <tr>
                <td>{{ 'back.site.form.company' | trans }}</td>
                <td>
                    {% if site.company %}
                        {{ site.company.name }}
                    {% endif %}
                </td>
            </tr>
        </table>
    </div>
    <div class="row" style="margin-top: 30px">
        <div class="col-lg-12">
            <p style="text-transform: uppercase; font-weight: 600">{{ 'site.list.title'|trans }}</p>
            {% if users|length > 0 %}
            <table class="table table-striped">
                <thead>
                <tr>
                    <th>{{ 'site.list.users.id'|trans }}</th>
                    <th>{{ 'site.list.users.firstname'|trans }}</th>
                    <th>{{ 'site.list.users.lastname'|trans }}</th>
                    <th>{{ 'site.list.users.function'|trans }}</th>
                    <th>{{ 'site.list.users.role'|trans }}</th>
                    <th>{{ 'site.list.users.action'|trans }}</th>
                </tr>
                </thead>
                <tbody>
                {% for user in users %}
                    <tr>
                        <td>{{ user.id }}</td>
                        <td>{{ user.firstname }}</td>
                        <td>{{ user.lastname }}</td>
                        <td>{{ user.function }}</td>
                        <td>{{ ('back.user.role.secondary.'~ user.roles|first)|trans }}</td>
                        <td>
                            {# TODO redirect to user detail#}
                            <a class="action visualisationPage" href="#">
                                <svg class="Icon" alt={{ 'back.commons.view'|trans }}>
                                    <use xlink:href="#eye"></use>
                                </svg>
                            </a>
                        </td>
                    </tr>
                {% endfor %}
                </tbody>
            </table>
            {% else %}
                <p style="background-color: #FFF; padding: 0.75rem; border: 1px solid #dee2e6;">{{ 'site.list.no_user'|trans }}</p>
            {% endif %}
        </div>
    </div>

    <div style="display:flex; justify-content: space-between; align-items: flex-end; margin: 30px 0 20px">
        <span style="text-transform: uppercase; font-weight: 600">{{ 'back.shipping_points.shipping_points' | trans }}</span>
        <div>
            <a href="{{ path('admin.shipping_point.add', {'id': site.id}) }}"
               class="btn btn-info"
               role="button" aria-pressed="true">{{ 'back.shipping_points.add'|trans }}</a>
            {% if site.shippingPoints|length == 1 %}
                <a href="{{ path('admin.shippingpoint.edit', {'siteId': site.id, 'shippingId' : site.shippingPoints[0].id}) }}"
                   style="margin-left: 10px">
                    <button type="buttons" class="btn btn-primary">{{ 'back.shipping_points.edit'|trans }}</button>
                </a>
            {% endif %}
        </div>
    </div>

        {% for shippingPoint in site.shippingPoints %}
        <table class="table table-bordered" style="margin-bottom: 10px">
            <tr>
                <td colspan="2" class="text-center font-weight-bold">{{ shippingPoint.name }}</td>
            </tr>
            <tr>
                <td style="width: 300px">{{ 'back.site.form.address' | trans }}</td>
                <td>
                    {% if shippingPoint.address %}
                        {{ shippingPoint.address.address }}
                    {% endif %}
                </td>
            </tr>
            <tr>
                <td>{{ 'back.site.form.address2' | trans }}</td>
                <td>
                    {% if shippingPoint.address %}
                        {{ shippingPoint.address.address2 }}
                    {% endif %}
                </td>
            </tr>
            <tr>
                <td>{{ 'back.site.form.zipCode' | trans }}</td>
                <td>
                    {% if shippingPoint.address %}
                        {{ shippingPoint.address.zipCode }}
                    {% endif %}
                </td>
            </tr>
            <tr>
                <td>{{ 'back.site.form.city' | trans }}</td>
                <td>
                    {% if shippingPoint.address %}
                        {{ shippingPoint.address.city }}
                    {% endif %}
                </td>
            </tr>
            <tr>
                <td>{{ 'back.site.form.region' | trans }}</td>
                <td>
                    {% if shippingPoint.address %}
                        {{ shippingPoint.address.region | trans }}
                    {% endif %}
                </td>
            </tr>
            <tr>
                <td>{{ 'back.site.form.country' | trans }}</td>
                <td>
                    {% if shippingPoint.address %}
                        {{ shippingPoint.address.country | trans}}
                    {% endif %}
                </td>
            </tr>
        </table>
        {% if site.shippingPoints|length > 1 %}
            <div class="row" style="margin-bottom: 20px;">
                <div class="col-lg-12" style="display:flex;justify-content: flex-end">
                    <a href="{{ path('admin.shippingpoint.edit', {'siteId': site.id, 'shippingId' : shippingPoint.id}) }}">
                        <button type="buttons" class="btn btn-primary">{{ 'back.shipping_points.edit'|trans }}</button>
                    </a>
                </div>
            </div>
        {% endif %}
        {% endfor %}

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            {% if company.status != 'valid' %}
                // Prevent clicks on the links available only if company is valid
                $('#validButton').on('click', function (ev) {
                    ev.preventDefault();

                    window.UI.Modal.alert('{{ errorValid }}');
                    return false;
                });
            {% endif %}
        });
    </script>
{% endblock %}