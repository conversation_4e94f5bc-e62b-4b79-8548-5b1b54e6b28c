<?php

namespace Open\BackBundle\Controller;

use AppBundle\Controller\MkoController;
use AppBundle\Entity\Image;
use AppBundle\Entity\Node;
use AppBundle\Entity\NodeContent;
use AppBundle\Repository\NodeRepository;
use AppBundle\Services\NotificationService;
use Doctrine\ORM\EntityManagerInterface;
use Open\BackBundle\Form\IdForm;
use Open\BackBundle\Form\SliderForm;
use Open\LogBundle\Service\LogService;
use Open\LogBundle\Utils\EventNameEnum;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Security;
use Symfony\Component\Filesystem\Filesystem;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Exception\AccessDeniedHttpException;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Contracts\Translation\TranslatorInterface;

;

class SliderController extends MkoController
{

    private const SLIDER = 'slider';
    private const BACKGROUND_SLIDER_DIR_PARAM = 'slider_background_directory';

    private NotificationService $notificationService;

    /**
     * SliderController constructor.
     * @param NotificationService $notificationService
     */
    public function __construct(NotificationService $notificationService)
    {
        $this->notificationService = $notificationService;
    }


    /**
     * @Route("/slider/list/", name="admin.slider.list")
     * @Security("is_granted('ROLE_ADMIN') or is_granted('ROLE_OPERATOR')")
     */
    public function listSlidersAction(Request $request, NotificationService $notificationService): Response
    {
        $qb = $notificationService->getDefaultQueryBuilder();
        $qb->andWhere("e.type = :slider");
        $qb->setParameter(self::SLIDER, self::SLIDER);
        $qb->orderBy('e.orderNode', 'ASC');

        $paginator = $notificationService->getPaginatorByQb($qb, 1, 25, $request);

        return $this->render('@OpenBack/slider/slider_list.html.twig',
            [
                'pagination' => $paginator,
                'locale' => $request->getLocale(),
                'form_change_slider_status' => $this->createForm(IdForm::class, null)->createView()
            ]
        );
    }

    /**
     * @Route("/slider/{id}/edit", name="admin.slider.edit")
     * @Route("/slider/add", name="admin.slider.add")
     * @Security("is_granted('ROLE_ADMIN') or is_granted('ROLE_OPERATOR')")
     */
    public function editSliderAction(Request $request, EntityManagerInterface $em, NodeRepository $nodeRepository, TranslatorInterface $translator, LogService $logger, $id = null)
    {
        $isBackgroundSet = false;

        if (!is_null($id)) {
            /** @var Node $node */
            $node = $nodeRepository->findOneById($id);
            $actualNode = clone $node;
            if (!is_null($actualNode->getBackgroundImage()) && !empty($actualNode->getBackgroundImage())) {
                $isBackgroundSet = true;
            }

            if (is_null($node)) {
                throw new AccessDeniedHttpException("the specified entity is not exists");
            }

            //we only want slider here
            if ($node->getType() != self::SLIDER) {
                throw new AccessDeniedHttpException("the specified entity is not a slider item");
            }
        } else {
            /** @var Node $node */
            $node = new Node();
            $node->setType(self::SLIDER);
            $node->setStatus('draft');
        }

        // If no en content found for this node then add an one
        if (is_null($node->getContent('en'))) {
            $en = new NodeContent();
            $en->setNode($node);
            $en->setLang('en');
            $node->addContent($en);
        }

        // If no fr content found for this node then add an one
        if (is_null($node->getContent('fr'))) {
            $fr = new NodeContent();
            $fr->setNode($node);
            $fr->setLang('fr');
            $node->addContent($fr);
        }

        // If no fr content found for this node then add an one
        if (is_null($node->getContent('de'))) {
            $de = new NodeContent();
            $de->setNode($node);
            $de->setLang('de');
            $node->addContent($de);
        }

        if (is_null($node->getContent('nl'))) {
            $nl = new NodeContent();
            $nl->setNode($node);
            $nl->setLang('nl');
            $node->addContent($nl);
        }

        $form = $this->createForm(
            SliderForm::class,
            $node
        );

        $form->handleRequest($request);
        if ($form->isSubmitted() && $form->isValid()) {
            $node = $form->getData();

            // Background image
            if (!is_null($node->getBackgroundImage()) && !empty($node->getBackgroundImage())) {
                $file = $node->getBackgroundImage();
                $img = new Image();
                $img->setName($file->getClientOriginalName());
                $img->setMime($file->getMimeType());
                $img->setBlob(file_get_contents($file->getRealPath()));
                $node->setBackgroundImage($img);
            } // Si le backgroud n'est pas vide, on le laisse tel quel
            else if ($isBackgroundSet) {
                $node->setBackgroundImage($actualNode->getBackgroundImage());
            } else {
                $node->setBackgroundImage(null);
            }

            try {
                $em->persist($node);
                $em->flush();
                if ($id) {
                    $this->addFlash('success', $translator->trans('back.slider.update.confirm', [], self::TRANSLATION_DOMAIN));
                } else {
                    $this->addFlash('success', $translator->trans('back.slider.create.confirm', [], self::TRANSLATION_DOMAIN));
                }
                return $this->redirectToRoute('admin.slider.list');
            } catch (\Exception $e) {
                $logger->error("Error while saving slider item: " . $e->getMessage(), EventNameEnum::TECHNICAL_ERROR, $this->getUser()->getUsername(), ["exception" => $e->getTraceAsString()]);
                if (!is_null($id)) {
                    $this->addFlash('error', $translator->trans('back.slider.update.error', [], self::TRANSLATION_DOMAIN));
                } else {
                    $this->addFlash('error', $translator->trans('back.slider.create.error', [], self::TRANSLATION_DOMAIN));
                }
            }
        }

        return $this->render(
            '@OpenBack/slider/slider_edit.html.twig',
            [
                'form' => $form->createView(),
                'id' => $id,
                'nodeType' => self::SLIDER,
                'sliderItem' => $node
            ]
        );
    }

    /**
     * @Route("/slider/{id}/delete", name="admin.slider.delete")
     * @Security("is_granted('ROLE_ADMIN') or is_granted('ROLE_OPERATOR')")
     */
    public function deleteSliderAction(EntityManagerInterface $em, NodeRepository $nodeRepository, TranslatorInterface $translator, LogService $logger, $id): RedirectResponse
    {
        $imageDirectory = $this->getParameter(self::BACKGROUND_SLIDER_DIR_PARAM);

        /** @var Node $node */
        $node = $nodeRepository->findOneById($id);
        if (is_null($node)) {
            throw new AccessDeniedHttpException("the specified entity is not exists");
        }

        //we only want slider here
        if ($node->getType() != self::SLIDER) {
            throw new AccessDeniedHttpException("the specified entity is not a slider item");
        }

        try {
            /** @var NodeContent $content */
            foreach ($node->getContent() as $content) {
                if (empty($content->getTitle()) && empty($content->getBody())) {
                    $node->removeContent($content);

                    $em->remove($content);
                    $em->flush();
                }
            }

            if (!is_null($node->getBackgroundImage())) {
                $fileSystem = new Filesystem();
                $fileSystem->remove($imageDirectory . $node->getBackgroundImage());
            }

            $em->remove($node);
            $em->flush();

            $this->removeOldBackgroundFiles();

            $this->addFlash('success', $translator->trans('back.slider.delete.confirm', [], self::TRANSLATION_DOMAIN));
        } catch (\Exception $e) {
            $logger->error("Error while removing slider item: " . $e->getMessage(), EventNameEnum::TECHNICAL_ERROR, $this->getUser()->getUsername(), ["exception" => $e->getTraceAsString()]);
            $this->addFlash('error', $translator->trans('back.slider.delete.error', [], self::TRANSLATION_DOMAIN));
        }

        return $this->redirectToRoute('admin.slider.list');
    }

    /**
     * @Route("/slider/changeStatus/{status}", name="admin.slider.status")
     * @Security("is_granted('ROLE_ADMIN') or is_granted('ROLE_OPERATOR')")
     */
    public function changeSliderStatusAction(Request $request, EntityManagerInterface $em, NodeRepository $nodeRepository, TranslatorInterface $translator, LogService $logger, $status): RedirectResponse
    {

        $form = $this->createForm(IdForm::class, null, []);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $id = $form->getData()['id'];

            /** @var Node $node */
            $node = $nodeRepository->findOneById($id);
            if (is_null($node)) {
                throw new AccessDeniedHttpException("the specified entity is not exists");
            }

            //we only want slider here
            if ($node->getType() != self::SLIDER) {
                throw new AccessDeniedHttpException("the specified entity is not a slider item");
            }

            try {
                if ($status === 'enable') {
                    $node->setStatus(Node::STATUS_PUBLISHED);
                } else if ($status === 'disable') {
                    $node->setStatus(Node::STATUS_DRAFT);
                }

                $em->persist($node);
                $em->flush();

                $this->addFlash('success', $translator->trans('back.slider.update.confirm', [], self::TRANSLATION_DOMAIN));
            } catch (\Exception $e) {
                $logger->error("Error while removing slider item: " . $e->getMessage(), EventNameEnum::TECHNICAL_ERROR, $this->getUser()->getUsername(), ["exception" => $e->getTraceAsString()]);
                $this->addFlash('error', $translator->trans('back.slider.update.error', [], self::TRANSLATION_DOMAIN));
            }
        } else {
            throw new BadRequestHttpException('invalid form');
        }


        return $this->redirectToRoute('admin.slider.list');
    }

    private function removeOldBackgroundFiles()
    {
        $nodesBackground = [];
        $fileSystem = new Filesystem();

        // Get files in background image directory
        $imageDirectory = (string) $this->getParameter(self::BACKGROUND_SLIDER_DIR_PARAM);
        $files = array_diff(scandir($imageDirectory), array('.', '..'));

        $qb = $this->notificationService->getDefaultQueryBuilder();
        $qb->andWhere("e.type = :slider");
        $qb->setParameter(self::SLIDER, self::SLIDER);
        $query = $qb->getQuery();
        $nodes = $query->getResult();
        foreach ($nodes as $node) {
            if (!is_null($node->getBackgroundImage())) {
                $nodesBackground[] = $node->getBackgroundImage();
            }
        }


        foreach ($files as $file) {
            if (!in_array($file, $nodesBackground, true)) {
                $fileSystem->remove($imageDirectory . $file);
            }
        }
    }

    private function createBackgeroundDirectory()
    {
        $fileSystem = new Filesystem();
        $imageDirectory = (string) $this->getParameter(self::BACKGROUND_SLIDER_DIR_PARAM);

        if (!$fileSystem->exists($imageDirectory)) {
            $fileSystem->mkdir($imageDirectory);
        }
    }

    /**
     * @return string
     */
    private function generateUniqueFileName()
    {
        // md5() reduces the similarity of the file names generated by
        // uniqid(), which is based on timestamps
        return md5(uniqid());
    }

}
