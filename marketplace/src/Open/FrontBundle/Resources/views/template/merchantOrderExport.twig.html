<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE cXML SYSTEM "http://xml.cxml.org/schemas/cXML/1.2.046/cXML.dtd">
<cXML xml:lang="en-GB" payloadID="{{payloadId}}"
      timestamp="{{timestamp}}">
    <Header>
        <From>
            <Credential domain="NetworkId">
                <Identity>ClickandBuy</Identity>
            </Credential>
        </From>
        <To>
            <Credential domain="{{toDomain}}">
                <Identity>{{toIdentity}}</Identity>
            </Credential>
        </To>
        <Sender>
            <Credential domain="{{senderDomain}}">
                <Identity>{{senderIdentity}}</Identity>
                <SharedSecret>{{senderSecret}}</SharedSecret>
            </Credential>
            <UserAgent>TOTAL-Click&amp;Buy</UserAgent>
        </Sender>
    </Header>
    <Request>
        <OrderRequest>
            <OrderRequestHeader orderID="CDAT{{merchantOrder.id}}"
                                orderDate="{{merchantOrder.createdOn}}" type="new">
                <Total>
                    <Money currency="{{merchantOrder.currency.code}}">{{merchantOrder.amount}}</Money>
                </Total>
                <ShipTo>
                    <Address isoCountryCode="{{shippingAddress.country.code}}" addressID="{{shippingAddressId}}">
                        <Name xml:lang="en">{{entity}}</Name>
                        <PostalAddress name="default">
                            <DeliverTo>{{shippingAddress.firstName}} {{shippingAddress.lastName}}</DeliverTo>
                            <DeliverTo>Click&amp;Buy</DeliverTo>
                            {% for key,street in streets%}
                                {%~ if street is not null ~%}<Street>{{street}}</Street>{%~ endif ~%}
                            {% endfor -%}
                            <City>{{shippingAddress.city}}</City>
                            <PostalCode>{{shippingAddress.zipCode}}</PostalCode>
                            <Country isoCountryCode="{{shippingAddress.country.code}}">{{shippingAddress.country.name}}</Country>
                        </PostalAddress>
                        <Email name="default">{{merchantOrder.user.email}}</Email>
                        <Phone name="work">
                            <TelephoneNumber>
                                <CountryCode isoCountryCode="{{shippingAddress.country.code}}"> </CountryCode>
                                <AreaOrCityCode/>
                                <Number>{{customerPhone}}</Number>
                            </TelephoneNumber>
                        </Phone>
                    </Address>
                </ShipTo>
                <BillTo>
                    <Address isoCountryCode="{{billingAddress.country.code}}" addressID="{{billingAddress.country.id}}">
                        <Name xml:lang="en">{{billingAddress.address}}</Name>
                        <PostalAddress name="default">
                            <DeliverTo>{{billingAddress.address}}</DeliverTo>
                            <Street>{{billingAddress.address2}}</Street>
                            <City>{{billingAddress.city}}</City>
                            <PostalCode>{{billingAddress.zipCode}}</PostalCode>
                            <Country isoCountryCode="{{billingAddress.country.code}}">{{billingAddress.country.name}}</Country>
                        </PostalAddress>
                        <Email name="default">{{merchantOrder.user.email}}</Email>
                    </Address>
                </BillTo>
                <Shipping>
                    <Money currency="{{merchantOrder.currency.code}}">{{merchantOrder.shipping}}</Money>
                    <Description xml:lang="en-GB">{{merchantOrder.order.shippingSpeed}}</Description>
                </Shipping>
                {% for key,attribute in customAttributes %}
                <Extrinsic name="{{key}}">{{attribute}}</Extrinsic>
                {% endfor %}
            </OrderRequestHeader>
            {% for item in merchantOrder.items %}
            <ItemOut quantity="{{item.quantity}}" lineNumber="{{loop.index}}">
                <ItemID>
                    <SupplierPartID>{{item.sku}}</SupplierPartID>
                    <BuyerPartID>{{item.offerId}}</BuyerPartID>
                </ItemID>
                <ItemDetail>
                    <UnitPrice>
                        <Money currency="{{item.currency.code}}">{{item.price}}</Money>
                    </UnitPrice>
                    <Description xml:lang="en">{{item.name}}</Description>
                    <UnitOfMeasure>EA</UnitOfMeasure>
                    <Classification domain="TOTAL">NA</Classification>
                </ItemDetail>
            </ItemOut>
            {% endfor %}
        </OrderRequest>
    </Request>
</cXML>
