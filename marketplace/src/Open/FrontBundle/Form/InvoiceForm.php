<?php

namespace Open\FrontBundle\Form;

use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\HiddenType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

class InvoiceForm extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options)
    {
        $builder
            ->add(
                'invoice',
                TextType::class,
                [
                    'label' => 'form.invoice.search',
                    'attr' => [
                        'placeholder' => 'form.invoice.search',
                    ],
                ]
            )->add(
                'activeTab',
                HiddenType::class
            )->add(
                'page',
                HiddenType::class
            )->setMethod('GET');
    }

    public function configureOptions(OptionsResolver $resolver)
    {
        $resolver->setDefaults(array(
            'translation_domain' => 'AppBundle'
        ));
    }

    public function getBlockPrefix()
    {
        return '';
    }
}
