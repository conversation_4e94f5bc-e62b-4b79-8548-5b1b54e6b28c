<?php
namespace Open\LogBundle\Listeners;

use AppBundle\Security\Provider\TotalBuyerProvider;
use KnpU\OAuth2ClientBundle\Client\ClientRegistry;
use Open\LogBundle\Service\LogService;
use Open\LogBundle\Utils\EventNameEnum;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Security\Core\Authentication\Token\TokenInterface;
use Symfony\Component\Security\Http\Logout\LogoutHandlerInterface;

class LogLogoutListener implements LogoutHandlerInterface
{
    private LogService $logger;
    private ClientRegistry $clientRegistry;

    public function __construct(LogService $logger, ClientRegistry $clientRegistry)
    {
        $this->logger = $logger;
        $this->clientRegistry = $clientRegistry;
    }


    public function logout(Request $request, Response $response, TokenInterface $token)
    {
        /** @var TotalBuyerProvider $provider */
        $provider = $this->clientRegistry->getClient('total_buyer')->getOAuth2Provider();
        $provider->logout();

        $response = new RedirectResponse($provider->logout());
        $response->send();


        $this->logger->info("User has successfully logout", EventNameEnum::SECURITY_LOGOUT, $token->getUsername());
    }
}
