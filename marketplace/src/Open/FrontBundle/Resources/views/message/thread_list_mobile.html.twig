{% trans_default_domain 'AppBundle' %}

        {# @var thread \AppBundle\Model\Thread #}
        {% for thread in threads %}
            <a class="pointer" onclick="window.location='{{ path('thread.details', {'threadId': thread.id, 'isMobile': true}) }}';">
                <section class="order-summary {% if thread.unread %}bg-grey{% endif %}">
                    <section>
                        <section>
                            <span>{{ ('message.labels.thread_title') | trans }}</span> : {{ thread.id }}
                        </section>
                    </section>
                    <section>
                        <section><span>{{ ('message.labels.subject') | trans }}</span> : {{ thread.subject }}</section>
                        <img alt="open" src="{{ asset('images/arrow-next.svg') }}">
                    </section>
                </section>
            </a>
        {% endfor %}
        <div class="show-more-container mobile-only" onclick="ShowMoreThreads();">
            <span class="show-more-img"><img src="{{ asset('images/plus.svg') }}"></span>
            <span>{{ 'orders.show_more'|trans | upper }}</span>
        </div>
