{% extends '@OpenBack/base.html.twig' %}
{% trans_default_domain 'AppBundle' %}
{% block body %}

<div class="row">
    <div class="col-md-12">
        <div class="card">
                {% set domain_title = 'system.settings.' ~ domain ~ '.title' %}
            <h3 class="card-header">{{ domain_title|trans }}</h3>
            <div class="card-block">
                <p class="card-text">
                    <ul>
                    {% for group,settings in groups %}
                        {% set group_title = 'system.settings.' ~ domain ~ '.' ~ group ~ '.title' %}
                        <li>
                            <a href="{{ path('system.settings.group', {domain: domain, group: group}) }}">{{  group_title|trans }}</a>
                        </li>
                    {% endfor %}
                    </ul>
                </p>
            </div>
        </div>
    </div>
</div>

{% endblock %}