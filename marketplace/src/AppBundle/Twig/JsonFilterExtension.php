<?php

namespace AppBundle\Twig;

use Twig\Extension\AbstractExtension;
use Twig\TwigFilter;

class JsonFilterExtension extends AbstractExtension
{
    public function getName()
    {
        return 'change.set.filter.extension';
    }

    public function getFilters()
    {
        return [
            new TwigFilter('json_decode', [$this, 'jsonDecode']),
            new TwigFilter('json_to_html', [$this, 'jsonToHtml']),
        ];
    }

    public function jsonDecode($str, $assoc = false)
    {
        return json_decode($str, $assoc);
    }

    public function jsonToHtml($str)
    {
        $result = "<ul>";
        if ($str && is_string($str)){
            $this->serializeJsonArray($result, json_decode($str, true));
        }
        $result .= "</ul>";
        return $result;
    }

    private function serializeJsonArray (&$result, $jsonArray, $prefix='')
    {
        if (is_array($jsonArray)) {
            foreach ($jsonArray as $field => $value) {
                if (is_string($value) || is_numeric($value)) {
                    $this->appendField($result, $field, $value, $prefix);
                } else if (is_array($value)) {
                    $this->serializeJsonArray($result, $value, $field . '.');
                }
            }
        }
    }

    private function appendField(&$result, $field, $value, $prefix = "")
    {
            $result .= '<li>'.$prefix.$field.' = '.htmlspecialchars($value).'</li>';
    }
}