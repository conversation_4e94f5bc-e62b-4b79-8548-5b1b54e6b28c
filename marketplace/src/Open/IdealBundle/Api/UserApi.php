<?php

namespace Open\IdealBundle\Api;

use Open\IdealBundle\Api;
use Open\IdealBundle\Model\Request;
use Symfony\Contracts\HttpClient\ResponseStreamInterface;

class UserApi extends Api
{

    public function retrieveUsers(?string $continuationToken = null): ResponseStreamInterface
    {

        $squery = 'join MainRecord u join u.Organization o join o.OrganizationType ot join u.WorkingLocation wl join wl.ParentLocation pwl join pwl.ParentLocation gpwl join PresenceState s join u.PersonalTitle pt join u.PreferredLanguage pl join u.CostCenter cc join u.EmployeeType et join u.AssignmentCompany ac join ac.Location acl join u.Manager m select MainEmployeeId, MainLastName, MainPhoneticFirstName, u.SecondaryEmailAddress, u.EmailAddress, ot.ShortName_en, o.DisplayEntity, wl.Name_fr, wl.Uid, wl.<PERSON>, wl.Street2, wl.Street3, wl.<PERSON>, wl.<PERSON>, wl.Locality, pwl.Name_fr, gpwl.Name_fr, pwl.Uid, gpwl.Uid, s.Identifier, pt.Name_fr, pl.DisplayName, u.ToipPhoneNumber, u.PhoneNumber, cc.Name, u.CostCenterText, et.Name_fr, ac.Uid, ac.Name, acl.Name_fr, acl.Uid, m.MainEmployeeId, u.UserRecordWhenIn, u.UserRecordWhenOut where (MainEmployeeId!%="L9" and MainEmployeeId!%="N" and MainEmployeeId!=null and MainFirstName!=null and MainLastName!=null and s.Identifier="P") order by MainEmployeeId asc';
        $path = "/Custom/Resources/Directory_User/View";

        $query = [
            "path" => $path,
            "squery" => $squery,
            "PageSize" => $this->apiConfiguration->getNbUserByCall()
        ];

        if ($continuationToken != null) {
            $query["ContinuationToken"] = $continuationToken;
        }

        return $this->getStream(
            (new Request("/api/Resource/Directory_User?api-version=1.0"))
                ->setQuery($query)
                ->setMethod(Request::GET)
                ->setConf([Request::TIMEOUT => $this->apiConfiguration->getTimeout()])
        );


    }

}
