FROM ruby:2.7.2-alpine3.12

RUN set -x \
    && apk add --no-cache \
        libstdc++ \
        sqlite-libs \
    && apk add --no-cache --virtual .build-deps \
        build-base \
        sqlite-dev \
    && adduser --ingroup root --system mailcatcher \
    && gem install mailcatcher \
    && apk del .build-deps

USER mailcatcher

# SMTP port
EXPOSE 1025

# Webserver port
EXPOSE 1080

CMD ["mailcatcher", "--http-ip=0.0.0.0", "--smtp-ip=0.0.0.0", "-f"]
