{% extends '@OpenFront/base.html.twig' %}
{% trans_default_domain 'AppBundle' %}

{% block title %} {{ 'user.profile.title' | trans }}{% endblock %}

{% block stylesheets %}{% endblock %}

{% block body %}

    {% block searchbar %}
        {% if formSearchBar is defined %}
            {% include('@OpenFront/component/search-bar.html.twig') %}
        {% else %}
            {{ render(controller("Open\\FrontBundle\\Controller\\SearchBarController::formAction")) }}
        {% endif %}
    {% endblock %}

    <div class="delegation">
        <div class="ta-c">
            <section class="delegation-title account-title">{{ 'user.delegation.title' | trans }}</section>
        </div>
        <div class="delegation-content">
            <section class="delegation-content-list">
                <span class="total-subtitle">{{ 'user.delegation.subtitle' | trans | upper }} : </span>
                <section class="empty-delegation">
                    {% if delegates is empty %}
                        {% include '@OpenFront/shared/delegation_empty.html.twig' %}
                    {% endif %}
                </section>
                <section class="delegations-table" {{ (delegates) ? 'style="display:block"' : 'style="display:none"'}}>
                    {% for delegate in delegates %}
                        {% include '@OpenFront/shared/delegation_row_mobile.html.twig' with { delegate : delegate } %}
                    {% endfor %}
                </section>
            </section>

            <section class="delegation-content-add-action">
                <form id="delegation" method="post" name="form">
                    <span class="total-subtitle-2">{{ 'user.delegation.ajouter_delegation' | trans | upper }}</span>
                    <section class="flex">
                        <input id="autocomplete-delegation-mobile" type="text" autocomplete="off"/>
                        <div class="add-delegation pointer" onclick="addDelegationMobile()">
                            <span class="add-delegation-img"><img src="{{ asset('images/plus.svg') }}"></span>
                            <span>{{ 'user.delegation.add_action'|trans }}</span>
                        </div>
                    </section>
                </form>
            </section>
        </div>
    </div>
{% endblock %}

{% block javascripts %}
    <script>
        $(document).ready(function() {
            $('#autocomplete-delegation-mobile').autocomplete({
                source: function(request, response) {
                    $.post("{{ path('front.user.delegation.autocomplete') }}", request, response);
                },
                select: function(event, ui) {

                    $('.add-delegation').css('display', 'flex');
                    $('.add-delegation').attr('data-id', ui.item.id);

                },
                minLength: 3
            }).autocomplete('instance')._renderItem = function(ul, item) {
                return $('<li>')
                    .append(item.label)
                    .appendTo(ul);
            };

        });

        let addDelegationMobile = function() {
            let modalLoading = window.UI.Modal.showLoading();
            let username = $('.add-delegation')[0].dataset.id;

            $.ajax({
                url: "{{ path('front.user.delegation.new', {'isMobile': true }) }}",
                data: {
                    'form[_token]': "{{ csrf_token('add') }}",
                    'form[username]': username
                },
                type: 'POST',
                success: function (htmlResult) {
                    if($('.delegations-table').css('display') === 'none') {
                        $('.delegations-table').css('display', 'block');
                        $('.delegation-content-empty').remove();
                    }
                    $('.delegations-table').append(htmlResult);
                    $('#autocomplete-delegation-mobile').val('');
                    $('.add-delegation').attr('data-id', '');
                    $('.add-delegation').css('display', 'none');
                    modalLoading.close();
                },
                error: function () {
                    modalLoading.close();
                },
                complete: function () {
                    window.UI.Utils.accordion();
                }
            });
        }
    </script>
{% endblock %}

