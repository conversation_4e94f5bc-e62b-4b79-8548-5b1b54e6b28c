{% extends '@OpenFront/base.html.twig' %}
{% trans_default_domain 'AppBundle' %}


{% block title %}
    {{ 'invitation.list.title'|trans }}
{% endblock %}

{% block body %}
    <div class="alert close">
        <p>{{ 'invitation.flash.ok' | trans }}</p>
    </div>
    {% block searchbar %}
        {% if formSearchBar is defined %}
            {% include('@OpenFront/component/search-bar.html.twig') %}
        {% else %}
            {{ render(controller("Open\\FrontBundle\\Controller\\SearchBarController::formAction")) }}
        {% endif %}
    {% endblock %}

    <div class="Page-inner">
        <div class="Page-content">
            <div style="text-align:center; margin-top: 80px">
                <section class="account-header account-title">{{ 'invitation.list.title' | trans }}</section>
            </div>


            <div class="tabs__content">
                <div class="tab__content active">
                    {% block invitationlist %}{% endblock %}
                </div>
            </div>

        </div>

    </div>
{% endblock %}
{% block javascripts %}
    <script>
        $(document).ready(function () {

            var responsiveEnabled = {
                details: {
                    display: $.fn.dataTable.Responsive.display.childRowImmediate,
                    type: 'none',
                    target: ''
                }
            };

            var responsive = false;

            if (window.innerWidth <= 900) {
                responsive = responsiveEnabled;
            }

            var orderTable = $('#all-invitations').DataTable({
                responsive: responsive,
                "processing": true,
                "serverSide": true,
                "ajax": "{{ path('front.supplier.invitation.list.all') }}",
                "columns": [
                    {
                        "data": "company_name"
                    },
                    {
                        "data": "email"
                    },
                    {
                        "data": "created_date"
                    },
                    {
                        "data": "status"
                    }, {
                        "data": "creator",
                        render: function (data) {
                            if (data.is_buyer) {
                                return ' <div class="tooltip-total tooltip-buy-again" style="margin-right: 0">\n' + data.user_name + '\n' + '<div class="tooltiptext">\n' + data.first_name + ' ' + data.last_name + '\n' + '<br>' + data.email + '</div>\n' + '</div>'
                            } else {
                                return '{{ 'invitation.source.operator' | trans }}';
                            }
                        }
                    }
                ],
                order: [
                    [0, 'desc']
                ],
                columnDefs: [
                    {
                        targets: '_all',
                        className: "sorting_disabled",
                        orderable: false
                    }
                ],
                "dom": '<"top"if>rt<"bottom"pl>',
                "lengthMenu": [
                    [
                        10, 25, 50, 100
                    ],
                    [
                        "{{ 'table.perPage'|trans({'%num%':10}, 'AppBundle') }}", "{{ 'table.perPage'|trans({'%num%':25}, 'AppBundle') }}", "{{ 'table.perPage'|trans({'%num%':50}, 'AppBundle') }}", "{{ 'table.perPage'|trans({'%num%':100}, 'AppBundle') }}"
                    ]
                ],
                "language": {
                    "info": "",
                    "search": "",
                    "lengthMenu": "_MENU_",
                    searchPlaceholder: "{{ 'invitation.list.search_table'|trans([], 'AppBundle') }}",
                    "paginate": {
                        "next": "<img src='/images/shape_right.svg'>",
                        "previous": "<img src='/images/shape_left.svg'>"
                    }
                },
                fnDrawCallback: function () {
                    let search = $('#all-invitations_wrapper').find('input[type=search]').val();
                    let emptyResponse = $(this).find('.dataTables_empty').length;
                    if (emptyResponse && search.length == 0) {
                        $(this).parent().hide();
                        $('#all-empty').removeClass('close');
                    } else {
                        $(this).parent().show();
                        $('#all-empty').addClass('close');
                    }

                }
            });

            var row = orderTable.row(function (idx, data, node) {
                var orderId = '{{ app.request.get('rid') }}';
                return data[0] == orderId;
            });
            if (row.length > 0) {
                row.show().draw(false);
            }

            $(document).on('click', '#resend-send-btn', function (e) {
                e.preventDefault();
                var data = {
                    'form[_token]': '{{ csrf_token('send_again') }}',
                    'form[invitId]': $('#invitId').val(),
                    'form[message]': $('#resend-message').val()
                };
                loadingModal = window.UI.Modal.showLoading();
                $("#resendModal").modal('hide');

                $.post("{{ path('front.supplier.invitation.resend') }}", data).done(function (e) {
                    if (loadingModal !== null) {
                        loadingModal.close();
                    }
                    let alert = $('.alert');

                    alert.removeClass('close');
                    alert.fadeOut(3000, function () {
                        $(this).addClass('close');
                        $(this).removeAttr("style");

                    });
                }).fail(function (e) {
                    alert("KO");
                    window.location.reload();
                });
            });

            $('[data-resendid]').on('click', function (e) {
                $('#invitId').val($(this).data('resendid'));
                $('#resend-message').val('');
            });
        });
    </script>
{% endblock %}
