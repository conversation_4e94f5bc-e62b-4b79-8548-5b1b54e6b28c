<?php

namespace Open\FrontBundle\EventSubscriber;

use Open\LogBundle\Service\LogService;
use Open\LogBundle\Utils\EventNameEnum;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;

class EmailResetSubscriber implements EventSubscriberInterface
{


    /**
     * @var LogService $logger the logger of the application
     */
    private $logger;

    /**
     * OfferSubscriber constructor.
     *
     * @param LogService $logger the logger of the application
     */
    public function __construct(LogService $logger)
    {
        $this->logger = $logger;
    }

    public static function getSubscribedEvents(): array
    {
        // Catch login actions to trigger logs actions
        return [
            'USER_RESETTING_SEND_EMAIL_INITIALIZE' => 'onResettingEmailInitialize'
        ];
    }

    public function onResettingEmailInitialize($event)
    {
        if (null === $event->getUser()) {
            $this->logger->info("anonymous user has requested email reset for an email address that doesn't exist", EventNameEnum::SECURITY_EVENT, null, [
                "email" => $event->getRequest()->request->get("username")
            ]);
        }
    }
}
