<?php

namespace Open\IzbergBundle\Api;

use Open\IzbergBundle\Api;
use Open\IzbergBundle\Model\FetchMerchantOrderRefundsResponse;
use Open\IzbergBundle\Model\Refund;

class RefundApi extends Api
{

    private const REFUND_SLUG = 'refund/';

    public function  getUri(){
        return 'refund';
    }

    public function getItemClass(){
        return Refund::class;
    }

    public function findRefundByOrderNumber($orderNumber){
        $response = $this->sendApiRequest(
            self::HTTP_GET_OPERATION,
            self::REFUND_SLUG . '?order_id_number=' . $orderNumber
        );

        return $this->serializer->deserialize($response->raw_body, Refund::class, 'json');
    }

    /**
     * @return Refund[]
     */
    public function findRefundsByMerchantOrderId($merchantOrderId, $onlyCompleted = false, $onlyWithoutInvoice = false): array
    {
        $route = self::REFUND_SLUG . '?merchant_order=' . $merchantOrderId;

        if ($onlyCompleted) {
            $route .= '&status=complete';
        }
        if ($onlyWithoutInvoice) {
            $route .= '&customer_invoice=none';
        }

        $result = [];
        do {
            $response = $this->sendApiRequest(
                self::HTTP_GET_OPERATION,
                $route
            );

            foreach ($response->body->objects as $object) {
                $result[] = $this->serializer->deserialize(json_encode($object), Refund::class, 'json');
            }

            $route = $response->body->meta->next;
        } while ($route !== null);

        return $result;
    }
}

