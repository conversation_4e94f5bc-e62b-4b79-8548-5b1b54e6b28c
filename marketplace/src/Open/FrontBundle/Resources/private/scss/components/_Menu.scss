@import "../variables";
@import "~breakpoint-sass/stylesheets/breakpoint";

#title-menu-overlay {
    width: 100%;
    padding: 15px 0;
    background-color: $WHITE;

    div {
        font-weight: bold;
        color: $TOTAL_RED;
        text-align: left;
        width: 85%;
        max-width: 1400px;
        font-size: 14px;
        padding-left: 45px;
        margin: 0 auto 0;
    }
}

#menu-account-mobile {
    width: 100%;
    background-color: $WHITE;
    padding: 10px 10px 0 10px;

    section {
        display: flex;
        text-align: center;
        border-bottom: 1px solid $TOTAL_DARK_GREY3;
        padding: 1rem;
        justify-content: space-between;

        a {
            display: inline-flex;
            align-items: center;

            &:hover {
                text-decoration: none;
            }

            > div {
                margin-right: 20px;
            }

            .Icon {
                width: 18px;
                height: 18px;
            }
        }

        .onboarding-step {
            display: block;
        }
    }
}

.overlay {
    height: 100%;
    width: 100%;
    position: fixed;
    z-index: 3;
    @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
        top: 50px;
    }
    @include breakpoint($BREAKPOINT_DESKTOP) {
        top: 95px;
    }
    left: -100%;
    background-color: rgba(53, 53, 53, 0.6);
    overflow-x: hidden;
    transition: all ease-in-out 0.4s;
    opacity: 0;

    &.open {
        left: 0;
        opacity: 1;
    }
}

.overlay .overlay-content-1 section {
    padding-left: 25px;
    padding-right: 15px;
    height: 45px;
    border-bottom: 1px solid $TOTAL_GREY7;
    display: flex;
    align-items: center;
    cursor: pointer;
}

.overlay .overlay-content-1 a {
    text-transform: uppercase;
    font-size: 12px;
    font-weight: 500;
    letter-spacing: 0.46px;
    color: #353535;
}

.overlay-content-1 section:hover .triangle {
    border-left: 7px solid #fff;
}

.overlay-content-1 section:hover {
    background-color: $TOTAL_RED;
}

.overlay-content-1 section:hover a {
    color: #fff !important;
}

#myNav {
    @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
        display: none;
    }

    .overlay {
        background-color: $WHITE;
    }

    .overlay-content {
        font-family: 'Roboto', sans-serif;
        position: relative;
        top: 0;
        width: 85%;
        max-width: 1400px;
        height: calc(100% - 141px);
        background-color: #fff;
        margin: auto;
        border: 1px solid #eee;
        display: flex;
        overflow: auto;
    }

    .overlay-content-1 {
        border-right: 1px solid #eee;
        font-size: 12px;
    }

    .overlay-content-2 {
        display: none;
        padding: 20px;
        max-height: 750px;
        word-wrap: break-word;
        z-index: 9999;

        &.menu_item-current {
            display: flex;
            flex-direction: row;
            flex-wrap: wrap;
        }

        p {
            text-transform: uppercase;
            padding-bottom: 10px;
            border-bottom: 1px solid $TOTAL_RED;
            margin-bottom: 0 !important;
            color: $TOTAL_RED;
            font-weight: bold;
        }

        ul {
            list-style: none;
            padding: 10px 0 0 0;

            li {
                text-decoration: none;
                line-height: 2;

                &:hover {
                    cursor: pointer;
                    font-weight: bold;
                }
            }
        }
    }

    .overlay-content-3 {
        padding: 10px;
        min-width: 230px;
        flex-wrap: wrap;
        width: 25%;
    }

    .overlay-content-3 p {
        display: inline-block;
        border-bottom: 1px solid $TOTAL_RED;
    }
}

@include breakpoint($BREAKPOINT_DESKTOP) {
    #container-content-2 {
        overflow: auto;
        flex: 1;
        background: #fff;
    }
}

#mobile-nav {
    @include breakpoint($BREAKPOINT_DESKTOP) {
        display: none;
    }

    #title-menu-overlay-mobile {
        width: 100%;
        padding: 15px 0;
        background-color: $WHITE;

        div {
            font-weight: bold;
            color: $TOTAL_RED;
            text-align: center;
            font-size: 14px;
        }
    }

    #categories-back {
        cursor: pointer;
        text-align: left !important;
        margin-left: 30px;
    }

    .overlay-content {
        font-family: 'Roboto', sans-serif;
        position: relative;
        top: 0;
        height: calc(100% - 141px);
        background-color: #fff;
        margin: auto;
        display: flex;
        overflow: auto;
    }

    .overlay-content-1 {
        width: 100%;
        border-right: 1px solid $TOTAL_GREY7;
        border-top: 1px solid $TOTAL_GREY7;
        font-size: 12px;

        section {
            padding: 0 35px 0 25px;
            height: 45px;
            border-bottom: 1px solid $TOTAL_GREY7;
            display: flex;
            align-items: center;
            cursor: pointer;
            justify-content: space-between;

            .icon-triangle {
                height: 100%;
                width: 35px;
                padding-left: 15px;
                padding-right: 15px;
            }
        }
    }

    .overlay-content-2 {
        width: 0;
        display: none;

        &.menu_item-current {
            display: flex !important;
            flex-direction: column;
            width: 100%;
        }

        p {
            text-transform: uppercase;
            padding: 0 0 10px 30px;
            border-bottom: 1px solid $TOTAL_RED;
            margin-bottom: 0 !important;
            color: $TOTAL_RED;
            font-weight: bold;
            width: 100%;
        }

        ul {
            list-style: none;
            padding: 10px 0 0 0;
            display: none;
            overflow: hidden;

            li {
                text-decoration: none;
                line-height: 2;
                padding: 10px 0 10px 30px;
                border-bottom: 1px solid $TOTAL_GREY7;

                &:hover {
                    cursor: pointer;
                    font-weight: bold;
                }
            }
        }
    }

    .overlay-content-3 {
        padding: 10px 0 0 0;
        min-width: 230px;
    }

    .overlay-content-3 p {
        display: inline-flex;
        justify-content: space-between;
        border-bottom: 1px solid $TOTAL_RED;
        padding-right: 25px;
        cursor: pointer;

        a {
            color: $TOTAL_RED !important;

            &:hover {
                color: $TOTAL_RED !important;
            }
        }

        .plus {
            display: block;
        }

        .moins {
            display: none;
        }

        &.active {
            .plus {
                display: none;
            }

            .moins {
                display: block;
            }
        }
    }
}

.overlay a {
    text-decoration: none;
    color: #353535;
    transition: 0.3s;
}

.overlay a:hover,
.overlay a:focus {
    color: #353535;
}

.overlay .closebtn {
    position: absolute;
    top: 10px;
    left: 23%;
    z-index: 1;
    height: 30px;
    width: 30px;
    padding: 0px;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
}

.icon-triangle {
    height: 9px;
    width: 4px;
    margin-left: 10px;

    use#icon-triangle-hover {
        opacity: 0;
    }

    &:hover use#icon-triangle {
        opacity: 0;
    }

    &:hover use#icon-triangle-hover {
        opacity: 1;
    }
}

.overlay .closebtn:hover {
    color: $TOTAL_RED !important;
}

@media screen and (max-height: 450px) {
    .overlay a {
        font-size: 20px;
    }
    .overlay .closebtn {
        font-size: 40px;
        top: 15px;
        right: 35px;
    }
}

#account > span .Icon {
    height: 15px;
    position: absolute;
    top: 33px;
    fill: $APPLI_DARK_YELLOW;

    @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
        top: 117px;
        left: 185px;
    }
}

.onboarding-step {
    color: $TOTAL_DARK_GREY;
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 1px;
    font-weight: 700;
    margin-bottom: 20px;

    @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
        display: none !important;
    }
}

.onboarding-point {
    width: 38px;
    height: 38px;
    border-radius: 50%;
    background-color: $WHITE;
    border: 6px solid $TOTAL_LIGHT_GREY;
    display: flex;
    align-items: center;
    justify-content: center;

    .Icon {
        width: 16px;
        height: 12px;
    }

    .icon-white {
        display: none;
    }

    .icon-grey {
        display: block;
    }
}

.background-line-progress {
    height: 6px;
    width: 100%;
    background-color: $TOTAL_LIGHT_GREY;
    border-radius: 3px;
    margin-top: -22px;
    z-index: 2;

    @include breakpoint($BREAKPOINT_MOBILE_MAX) {
        height: 4px;
    }
}

.btn-onboarding-breadcrumb:not(:disabled):not(.disabled):active,
.btn-onboarding-breadcrumb:not(:disabled):not(.disabled).active,
.show > .btn-onboarding-breadcrumb.dropdown-toggle {
    .onboarding-step {
        color: $PRIMARY_COLOR;
    }

    .onboarding-point {
        width: 32px;
        height: 32px;
        margin: 3px 0;
        background-color: $PRIMARY_COLOR;
        border: 0;

        .icon-white {
            display: block;
        }

        .icon-grey {
            display: none;
        }
    }
}

.onboarding_breadcrumb
.onboarding-menu-item
a.btn-onboarding-breadcrumb.disabled {
    color: $BLACK;
}

.btn:not(:disabled):not(.disabled) {
    cursor: pointer;
}

.btn.disabled {
    cursor: default;
}

.btn:focus {
    box-shadow: none;
}

.desktop_breadcrumb {
    display: flex;
    align-items: center;
    height: 160px;
    background-color: $WHITE;

    .logo--home {
        margin: 0 63px 0 30px;
    }
}

.breadcrumb-part {
    width: 100%;
    margin-right: 90px;
}

.onboarding_breadcrumb {
    display: flex;
    overflow: hidden;
    justify-content: space-between;
    font-size: 18px;
}

.onboarding_breadcrumb.hide,
.mobile_breadcrumb.hide {
    display: none;

    + .container .Messages .Message-item.Message--success {
        margin-top: 110px;
    }
}

.onboarding-menu-item {
    display: flex;

    @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
        width: 38px;
    }

    &:first-child {
        margin-left: 35px;

        @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
            margin-left: 0;
        }
    }
}

.onboarding_breadcrumb .onboarding-menu-item a {
    color: $PRIMARY_COLOR;
    text-decoration: none;
    outline: none;
    display: flex;
    flex-direction: column;
    align-items: center;
    z-index: 100;

    &:hover {
        text-decoration: none;
    }
}

.mobile_breadcrumb {
    margin: 100px auto 30px;
    width: 90%;
}

.side-menu-container {
    display: flex;
    flex-direction: column;
    align-self: flex-start;
    margin: 30px 0 60px;

    &:hover {
        a {
            max-width: 100%;
            overflow: visible;
        }
    }

    .side-menu-item {
        border-left: 4px solid $WHITE;
        text-transform: uppercase;
        font-size: 0.8125rem;
        letter-spacing: 1px;
        font-weight: 700;
        display: flex;
        align-items: center;
        height: 70px;
        position: relative;

        .Icon {
            width: 16px;
            height: 16px;
            fill: $TOTAL_DARK_GREY;
            margin: 0 20px 0 30px;

            &.stroke {
                stroke: $TOTAL_DARK_GREY;
            }
        }

        .item-title {
            display: flex;
            align-items: center;
        }

        .arrow {
            display: none;
        }

        @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding-right: 20px;
        }
    }

    a {
        color: $TOTAL_DARK_GREY;
        background-color: $WHITE;

        &:hover,
        &.active {
            color: $PRIMARY_COLOR;
            text-decoration: none;

            .side-menu-item {
                border-color: $PRIMARY_COLOR;
            }

            .Icon {
                fill: $PRIMARY_COLOR;

                &.stroke {
                    stroke: $PRIMARY_COLOR;
                }
            }
        }

        @include breakpoint($BREAKPOINT_DESKTOP) {
            max-width: 70px;
            overflow: hidden;
        }

        @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
            display: none;
            background-color: $WHITE;
            box-shadow: 0px 0px 4px rgba($TOTAL_GREY, 0.2);

            &.active {
                display: block;

                .arrow {
                    display: block;
                }
            }
        }
    }

    @include breakpoint($BREAKPOINT_DESKTOP) {
        transition: width 0.5s ease;
        width: 70px;
        margin-right: 30px;
        background-color: $WHITE;

        .item-label {
            opacity: 0;
            transition: opacity 0.1s ease-out;
            white-space: nowrap;
        }

        .side-menu-item .Icon {
            margin-left: 24px;
        }

        .side-menu-item {
            width: 200px;
            overflow: hidden;
        }

        &:hover {
            width: 270px;

            .item-label {
                opacity: 1;
                transition: opacity 0.7s ease-in;
            }

            .side-menu-item {
                width: 100%;
            }
        }
    }

    @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
        width: 100%;
        height: auto;
        padding: 0 20px;
        background-color: transparent;
        box-shadow: none;
        margin: 15px 0 30px;

        &.show-all-menu {
            a {
                display: block;
            }

            .arrow {
                display: none;
            }

            .hideSideMenu {
                display: block;
            }
        }
    }
}

.breadcrumb-logout {
    margin-right: 20px;
    height: 65px;

    .logout-link {
        text-decoration: none;

        &:hover {
            text-decoration: none;
        }
    }
}
