<?php

namespace AppBundle\Model;


class Price
{
    /**
     * @var string currency
     */
    private $currency;

    /**
     * @var float $price value of the price
     */
    private $price;

    /**
     * @var bool whether the currency is the one of the offer
     */
    private $offerCurrency;

    /**
     * @return string
     */
    public function getCurrency(): string
    {
        return $this->currency;
    }

    /**
     * @param string $currency
     * @return Price
     */
    public function setCurrency(string $currency): Price
    {
        $this->currency = $currency;
        return $this;
    }

    /**
     * @return float
     */
    public function getPrice(): float
    {
        return $this->price;
    }

    /**
     * @param float $price
     * @return Price
     */
    public function setPrice(float $price): Price
    {
        $this->price = $price;
        return $this;
    }

    /**
     * @return bool
     */
    public function isOfferCurrency(): bool
    {
        return $this->offerCurrency;
    }

    /**
     * @param bool $offerCurrency
     * @return Price
     */
    public function setOfferCurrency(bool $offerCurrency): Price
    {
        $this->offerCurrency = $offerCurrency;
        return $this;
    }



}
