<?php

namespace Symfony\Component\DependencyInjection\Loader\Configurator;

use Open\IdealBundle\ApiConfiguration;
use Open\IdealBundle\IdealClient;
use Open\IdealBundle\IdealClientFactory;
use Open\Webhelp\WebhelpClient;
use Open\Webhelp\WebHelpClientFactory;


return function (ContainerConfigurator $configurator) {
    // default configuration for services in *this* file
    $services = $configurator->services()
        ->defaults()
        ->autowire()      // Automatically injects dependencies in your services.
        ->autoconfigure() // Automatically registers your services as commands, event subscribers, etc.
    ;

    // makes classes in src/ available to be used as services
    // this creates a service per class whose id is the fully-qualified class name
    $services->load('Open\\IdealBundle\\', '../../*')
        ->exclude('../../{DependencyInjection,Model,Resources,OpenIdealBundle.php}');

    $services->set(ApiConfiguration::class)
        ->args([
            '%env(IDEAL_API_SCHEME)%',
            '%env(IDEAL_API_DOMAIN)%',
            '%env(IDEAL_API_CLIENT_ID)%',
            '%env(IDEAL_API_CLIENT_SECRET)%',
            '%env(IDEAL_API_APIM_KEY)%',
            '%env(IDEAL_TIMEOUT)%',
            '%env(IDEAL_NB_USER_BY_CALL)%'
        ]);

    $services->set(IdealClientFactory::class);
    $services->set(IdealClient::class)
        ->factory([service(IdealClientFactory::class), 'buildIdealClient'])
        ->tag("monolog.logger",["channel"=>"ideal"]);
};
