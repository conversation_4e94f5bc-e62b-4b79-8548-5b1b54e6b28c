<?php

namespace Open\FrontVendorBundle\Controller;

use AppB<PERSON>le\Controller\MkoController;
use AppBundle\Exception\MarketPlaceException;
use AppBundle\Services\MarketPlaceService;
use KnpU\OAuth2ClientBundle\Client\ClientRegistry;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

class SecurityController extends MkoController
{
    /**
     * @Route("/front/supplier/connect/start/{marketplaceName}", name="front_supplier_connect_start", options={"i18n"=false})
     * @throws MarketPlaceException
     */
    public function connectAction(MarketPlaceService $marketPlaceService, ClientRegistry $clientRegistry, string $marketplaceName): Response
    {
        $marketplace = $marketPlaceService->getMarketPlaceByName($marketplaceName);
        $clientRegistryName = sprintf('izberg_vendor_client_%s', $marketplace->getName());
        $params = [
            'scope' => 'openid profile email merchant:read identity:read',
            'approval_prompt' => 'none',
            'response_type' => 'code',
            'flow' => 'merchant',
            'audience' => 'vendor.auth',
        ];
        return $clientRegistry->getClient($clientRegistryName)->redirect([], $params);
    }

    /**
     * @Route("/front/supplier/connect", name="front_supplier_connect_check", options={"i18n"=false})
     */
    public function connectCheckAction(): RedirectResponse
    {
        return $this->redirectToRoute('supplier_homepage');
    }

    /**
     * @Route("/front/supplier/logout", name="supplier.logout", options={"i18n"=false})
     */
    public function logoutAction()
    {
    }
}
