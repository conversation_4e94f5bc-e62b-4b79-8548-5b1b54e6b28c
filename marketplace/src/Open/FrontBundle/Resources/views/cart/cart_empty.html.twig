{% extends '@OpenFront/base.html.twig' %}
{% trans_default_domain 'AppBundle' %}

{% block title %} {{ 'cart.detail' | trans }}{% endblock %}

{% block stylesheets %}
{% endblock %}

{% block body %}

    {% block searchbar %}
        {% if formSearchBar is defined %}
            {% include('@OpenFront/component/search-bar.html.twig') %}
        {% else %}
            {{ render(controller("Open\\FrontBundle\\Controller\\SearchBarController::formAction")) }}
        {% endif %}
    {% endblock %}

    {% include('@OpenFront/cart/cart_empty_partial.html.twig') %}
{% endblock %}
