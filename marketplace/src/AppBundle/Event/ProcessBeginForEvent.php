<?php

namespace AppBundle\Event;

use AppBundle\Model\ProcessId;
use Symfony\Contracts\EventDispatcher\Event;

class ProcessBeginForEvent extends Event
{
    public const NAME = 'processBeginFor';

    /** @var ProcessId */
    private $processId;

    public function __construct(ProcessId $processId)
    {
        $this->processId = $processId;
    }

    public function getProcessId(): ProcessId
    {
        return $this->processId;
    }
}
