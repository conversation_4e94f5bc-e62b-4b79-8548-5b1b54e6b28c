
{% form_theme form 'Form/appli_layout.html.twig' %}

{% extends '@OpenFront/base.html.twig' %}

{% set videoUrlEn = "https://total-mc35-front-pad.damdy.com/player-html5-e8f56fb8894ee9c3deced94a02299344.html" %}
{% set videoUrlFr = "https://total-mc35-front-pad.damdy.com/player-html5-5322f442cbb6282f464d7a003204d4ba.html" %}

{% set locale = (app.request.locale|split('_')[0]) | lower %}
{% set videoUrl = locale == "fr" ? videoUrlFr : videoUrlEn %}

{% block body %}
    <img class="Page-inner">
    <img class="register-background" src="{{ asset('images/light-form-bg.svg') }}">
    <div class="register-header">
        <section class="register-header-mobile-text mobile-only">
            {{ 'registration.introduction'|trans ({}, 'AppBundle')|raw }}
        </section>
        <section class="register-header-row">
            <section class="register-header-row">
                <section class="register-header-title w-48">
                    <section class="register-title-lf">
                        {{ 'registration.header.title'|trans ({}, 'AppBundle')|raw }}
                    </section>
                    <section class="register-header-subtitle">
                        {{ 'registration.header.subtitle'|trans ({}, 'AppBundle')|raw }}
                        {% set filePath =  marketplaceCode != 'FRA' ? '/docs-supplier/click_and_buy_support_marketplace_fournisseurs-' ~ marketplaceCode ~ '.pdf' : '/docs-supplier/click_and_buy_support_marketplace_fournisseurs.pdf' %}
                        <a href="{{ asset(filePath) }}" target="_blank">
                            <section class="register-more-info">
                                <span>{{ 'registration.header.more_info'|trans ({}, 'AppBundle') }}</span>
                            </section>
                        </a>
                    </section>
                </section>
                <section class="w-48 video-section">
                    <section class="register-header-video">
                        <iframe id="video-frame" src={{ videoUrl }} frameborder="0" type="text/html" mozallowfullscreen="true" webkitallowfullscreen="true" allowfullscreen="true" autoplay="true">
                        </iframe>
                    </section>
                </section>

            </section>
        </section>
    </div>
    <div class="form-register">
        <div class="form-title">
            <h1>
                {{ 'registration.title'|trans ({}, 'AppBundle')|raw }}
            </h1>
        </div>

        {{
            form_start(
                form,
                {
                    'method': 'post',
                    'action': path(app.request.attributes.get('_route'), app.request.attributes.get('_route_params')),
                    'attr': {
                        'class': 'registration_register',
                        'id':'js-form-register',
                        'autocomplete':'off'
                    }
                }
            )
        }}

        <div class="form-fields activities-list">
            <section class="activities-list-title label">
                {{ form_label(form.categories) }}
                {{ form_errors(form.categories) }}
            </section>
            {{ form_widget(form.categories, {'attr': {'class': 'form-fields'}}) }}
        </div>

        <div class="form-fields">
            {{ form_row(form.raisonSociale, {'attr':{'maxlength':'50', 'tabindex':'2'}}) }}
            {{ form_errors(form.raisonSociale) }}

            <div class="form-row">
                {{ form_label(form.language) }}
                {{ form_widget(form.language, {'attr':{'id':'registration_form_language', 'class': 'form-select', 'tabindex':'4'}}) }}
                <small class="error">{{ form_errors(form.currency) }}</small>
            </div>

            <div class="form-row">
                {{ form_label(form.currency) }}
                {{ form_widget(form.currency, {'attr':{'id':'registration_form_currency', 'data-sort': 'true', 'class': 'form-select', 'tabindex':'6'}}) }}
                <small class="error">{{ form_errors(form.currency) }}</small>
            </div>

            {{ form_row(form.lastname, {'attr':{'tabindex':'8'}}) }}
            {{ form_errors(form.lastname) }}

            {{ form_row(form.plainPassword, {'attr':{'tabindex':'10'}}) }}


        </div>

        <div class="form-fields">


            <div class="form-row">
                <div class="label-tooltip">
                    {{ form_label(form.marketplace) }}
                </div>
                {{ form_widget(form.marketplace, {'attr':{'tabindex':'3', 'disabled':'true'}}) }}

                <small class="error">{{ form_errors(form.marketplace) }}</small>
            </div>
            <div class="form-row">
                <div class="label-tooltip">
                    {{ form_label(form.country) }}
                </div>
                {{ form_widget(form.country, {'attr':{'id':'registration_form_country', 'data-sort': 'true', 'class': 'form-select', 'tabindex':'5'}}) }}

                <small class="error">{{ form_errors(form.country) }}</small>
            </div>

            {{ form_row(form.identification, {'attr':{'tabindex':'7'}}) }}

            {{ form_row(form.firstname, {'attr':{'tabindex':'11'}}) }}
            {{ form_errors(form.firstname) }}

            {{ form_row(form.email, {'attr':{'tabindex':'9'}}) }}

            {{ form_row(form.mainPhoneNumber, {'attr':{'tabindex':'12', 'pattern':'\\d*'}}) }}


            {% do form.function.setRendered %}

        </div>

        <div class="form-fields cgu-validation">
            {{ form_widget(form.cgu, {'attr':{'tabindex':'12'}, 'path': '#'}) | raw }}
            <div class="register-cgu-label">
                {% set params =  {'%SUFFIX%': '-'~marketplaceCode}  %}
                {{ 'cgu.read_cgu' | trans(params, 'AppBundle') |raw }}
            </div>
        </div>

        {% if captcha_enabled and form.hiddenRecaptcha is defined %}
            <div class="captcha-fields mt-2">
                <div class="g-recaptcha" data-sitekey="{{ form.hiddenRecaptcha.vars.attr['data-sitekey'] }}"></div>
            </div>
        {% endif %}
        <div class="btn-fields mt-5 pt-md-4 pr-md-4">
            <button id="js-submit-button" type="submit" value="{{ 'back.user.form.validate'|trans({}, 'AppBundle') }}">
                {{ 'btn.accept'|trans({}, 'AppBundle') }}
            </button>
            <button class="btn-total-primary btn-total-cancel" type="reset" value="{{ 'back.user.form.cancel'|trans({}, 'AppBundle') }}">
                {{ 'btn.cancel'|trans({}, 'AppBundle') }}
            </button>
            {{ form_end(form) }}
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            var $form = $('#js-form-register');

            // Initialize the custom company identification validation
            window.UI.CompanyIdentification.init(
                '#registration_form_country'
            );

            // Initialize country selection
            window.UI.CountrySelector.init(
              '#registration_form_country',
              '#registration_form_identification'
            );

            window.UI.Select.init();

            // Hack
            $('#registration_form_identification').parent('.form-row').addClass('CompanyIdentification');

            // Hack phone numbers pattern message
            if($form.data('validator')) {
                $form.data('validator').settings.messages['registration_form[mainPhoneNumber]'].pattern = '{{ 'form.contact.phone_regex'|trans({}, 'validators') }}';
            }

            var identErrorTxt = '{{ 'registration.error.identification_already_used'|trans({}, 'AppBundle') }}';

            if($(".CompanyIdentification .error ul li").text() === identErrorTxt){
                window.UI.Modal.confirm(
                    '',
                    "{{ 'registration.error.identification_already_used_alert'|trans({}, 'AppBundle')|raw }}",
                    function () {
                      window.UI.Modal.showLoading();
                      window.location.href = "{{ path('anonymous.ticket.create') }}"
                    },
                    function () {
                    }
                );
            }
         });

        jQuery(document).ready(function() {
            $('#js-submit-button').on('click',function (evt) {
                $('#registration_form_categories-error, #registration_form_cgu-error').remove();
                if($("input[id^='registration_form_categories']:checked").length === 0){
                    var  errorText = "{{ 'buyer.registration.error.blank'|trans({}, 'messages')|raw }}";
                    var errorHtml = '<small id="registration_form_categories-error" style="color: #E8A7A7; display: block !important;">'+ errorText +'</small>';
                    $('#registration_form_categories').append(errorHtml);
                    return false;
                }
                if($("input[id^='registration_form_cgu']:checked").length === 0){
                    let  errorText = "{{ 'cgu.accept'|trans({}, 'AppBundle') |raw }}";
                    let errorHtml = '<small id="registration_form_cgu-error" style="color: #E8A7A7; display: block !important;">'+ errorText +'</small>';
                    $('.register-cgu-label').append(errorHtml);
                    return false;
                }

                {% if captcha_enabled and form.hiddenRecaptcha is defined %}
                    let $recaptcha = $('#{{ form.hiddenRecaptcha.vars.id }}');

                    if (grecaptcha.getResponse() === '') {
                        evt.preventDefault();
                        alert('{{ 'buyer.registration.error.captcha_blank'|trans }}');
                    } else {
                        $recaptcha.val(grecaptcha.getResponse());
                    }

                {% endif %}
            });

            $('form').submit(function(e) {
                $(':disabled').each(function(e) {
                    $(this).removeAttr('disabled');
                })
            });

        });
    </script>
    {% if captcha_enabled and form.hiddenRecaptcha is defined %}
        <script src="https://www.google.com/recaptcha/api.js?hl={{ app.request.locale }}" async defer></script>
    {% endif %}
{% endblock %}
