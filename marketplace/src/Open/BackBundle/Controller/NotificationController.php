<?php

namespace Open\BackBundle\Controller;

use AppBundle\Controller\MkoController;
use AppBundle\Entity\Node;
use AppBundle\Entity\NodeContent;
use AppBundle\Exception\MailException;
use AppBundle\Exception\MailValidationException;
use AppBundle\Exception\TemplateException;
use AppBundle\Repository\NodeRepository;
use AppBundle\Services\EmailTemplateService;
use AppBundle\Services\MailService;
use AppBundle\Services\NotificationService;
use Doctrine\ORM\EntityManagerInterface;
use Open\BackBundle\Form\NotificationForm;
use Open\LogBundle\Service\LogService;
use Open\LogBundle\Utils\EventNameEnum;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Security;
use Symfony\Component\Form\ClickableInterface;
use Symfony\Component\Form\FormError;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Exception\AccessDeniedHttpException;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Contracts\Translation\TranslatorInterface;

class NotificationController extends MkoController
{

    private const EMAIL = 'email';

    /**
     * @Route("/notification/list/", name="admin.notification.list")
     * @Security("is_granted('ROLE_ADMIN') or is_granted('ROLE_OPERATOR')")
     */
    public function listNotificationsAction(Request $request, NotificationService $notificationService): Response
    {
        $qb = $notificationService->getDefaultQueryBuilder();
        $qb->andWhere("e.type = :email");
        $qb->setParameter(self::EMAIL, self::EMAIL);
        $qb->orderBy("e.slug");

        $paginator = $notificationService->getPaginatorByQb($qb, 1, 200, $request);


        return $this->render('@OpenBack/notification/notification_list.html.twig',
            [
                'pagination' => $paginator,
                'locale' => $request->getLocale()
            ]
        );


    }


    /**
     * @Route("/notification/{id}/edit", name="admin.notification.edit")
     * @Route("/notification/add", name="admin.page.add")
     * @Security("is_granted('ROLE_ADMIN') or is_granted('ROLE_OPERATOR')")
     */
    public function editNotificationAction(Request $request, EntityManagerInterface $em, NodeRepository $nodeRepository, TranslatorInterface $translator, LogService $logger, EmailTemplateService $emailTemplateService, MailService $emailService, $id)
    {
        /** @var Node $node */
        $node = $nodeRepository->findOneBy(['id' => $id, 'type' => Node::TYPE_EMAIL]);

        //we only want emails here
        if ($node->getType() != self::EMAIL) {
            throw new AccessDeniedHttpException("the specified entity is not an email");
        }

        //list of variables for this template. If an error occurred while trying to get the list of variables, the list will be empty
        //so the editor will not shown any available variables.
        $variables = [];

        try {
            $variables = array_keys($emailTemplateService->getTemplate($node->getSlug())->getVariables());
        } catch (TemplateException $e) {
            $logger->error("enable to get list of variables for template", EventNameEnum::EMAIL_ERROR, $this->getUsername(),
                [
                    "templateName" => $node->getSlug(),
                    "message" => $e->getMessage()
                ]);
        }

        // If no en content found for this node then add an one
        if (is_null($node->getContent('en'))) {
            $en = new NodeContent();
            $en->setNode($node);
            $en->setLang('en');
            $node->addContent($en);
        }

        // If no fr content found for this node then add an one
        if (is_null($node->getContent('fr'))) {
            $fr = new NodeContent();
            $fr->setNode($node);
            $fr->setLang('fr');

            $node->addContent($fr);
        }

        if (is_null($node->getContent('nl'))) {
            $nl = new NodeContent();
            $nl->setNode($node);
            $nl->setLang('nl');

            $node->addContent($nl);
        }

        $form = $this->createForm(
            NotificationForm::class,
            $node
        );

        $form->handleRequest($request);
        if ($form->isSubmitted() && $form->isValid()) {
            $saveAndSend = $form->get("saveAndSend");
            if ($saveAndSend instanceof ClickableInterface) {

                if ($saveAndSend->isClicked() && empty($form->get("email")->getData())) {
                    $form->get("email")->addError(new FormError($translator->trans("back.notification.edit.empty_email_error", [], self::TRANSLATION_DOMAIN)));
                } else {
                    $sendTestEmail = false;
                    $recipientEmail = $form->get("email")->getData();
                    if ($saveAndSend->isClicked()) {
                        $sendTestEmail = true;
                    }

                    $node = $form->getData();

                    $baseTemplate = $emailTemplateService->getTemplate($node->getSlug());

                    $currentValidatedLanguage = null;
                    try {

                        /** @var NodeContent $content */
                        foreach ($node->getContent() as $content) {
                            $currentValidatedLanguage = $content->getLang();
                            //modify the content of the base template
                            $baseTemplate->setContent($content->getBody());
                            //set the subject to validate
                            $baseTemplate->setSubject($content->getTitle());

                            //validate the content
                            $emailService->validateEmailTemplate($baseTemplate);

                            if ($sendTestEmail) {
                                $emailService->sendEmailMessage($baseTemplate->getTemplateName(),
                                    $currentValidatedLanguage, $recipientEmail, $baseTemplate->getVariables());
                            }
                        }

                        $em->flush();

                        $this->addFlash('success', $translator->trans('back.notification.edit.confirm', [], self::TRANSLATION_DOMAIN));

                        if ($sendTestEmail) {
                            $message = $translator->trans('node.form.test', [], self::TRANSLATION_DOMAIN);
                            $message = str_replace('%email%', $recipientEmail, $message);
                            $this->addFlash('success', $message);
                        }


                    } catch (MailException $e) {
                        $message = $translator->trans('node.form.error.template_validation', [], self::TRANSLATION_DOMAIN);
                        $message = str_replace('%message%', preg_replace("/\"__string_template__.*\"/", "template", $e->getMessage()), $message);
                        $message = str_replace('%locale%', $currentValidatedLanguage, $message);

                        $this->addFlash('danger', $message);
                    } catch (MailValidationException $e) {
                        $this->addFlash('danger', $e->getMessage());
                    } catch (\Exception $e) {
                        $logger->error("Error while saving notification: " . $e->getMessage(), EventNameEnum::TECHNICAL_ERROR, $this->getUser()->getUsername(), ["exception" => $e->getTraceAsString()]);
                        if (!is_null($id)) {
                            $this->addFlash('danger', $translator->trans('node.form.submit.error.update', [], self::TRANSLATION_DOMAIN));
                        } else {
                            $this->addFlash('danger', $translator->trans('node.form.submit.error.create', [], self::TRANSLATION_DOMAIN));
                        }
                    }
                }
            }
        }

        return $this->render('@OpenBack/notification/notification_edit.html.twig', [
            'form' => $form->createView(),
            'id' => $id,
            'nodeType' => 'Email',
            'templateVariables' => $variables
        ]);
    }
}
