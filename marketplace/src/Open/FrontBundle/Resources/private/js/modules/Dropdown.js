(function ($) {
    let hideDropdown = function (id) {
        if ($('#' + id).attr("data-show-dropdown")) {
            $('#' + id).removeAttr("data-show-dropdown");
        }
    };

    let hideDropdownContainer = function () {
        $('.dropdown-content').removeClass('show');
        $('.dropdown-content').removeAttr("data-show-dropdown")
    };

    let dropdownDelay = function (id) {
        var myId = id;
        var delay = 500;
        $('.dropdown-content').attr("data-show-dropdown", true);

        if (id == 'Dropdown-Account' || id == 'Dropdown-Lang' || id=='Mobile-Dropdown-Lang') {
            delay = 0;
        }

        setTimeout(function () {
            if ($('.dropdown-content').attr("data-show-dropdown")) {
                dropdownClick(myId);
            }
        }, delay);
    };

    let dropdownClick = function (id) {
        document.getElementById(id).classList.toggle("show");
        let dropdowns = document.getElementsByClassName("dropdown-content");
        for (let i = 0; i < dropdowns.length; i++) {
            let dropdown = $(dropdowns[i]);
            if (dropdown.closest("#" + id).length === 0 && dropdown.hasClass('show')) {
                dropdown.removeClass('show');
            }
        }
    };

    let dropdownHamburgerClick = function (id) {
        dropdownClick(id);
        let target = $(document.getElementById(id));
        let dpDisplay = target.parent().find('.dropdown-display');
        let label = target.parent().find(".arrow");
        let dpContent = target.parent().find('.dropdown-content');

        resetHamburger(target);

        if (dpContent.hasClass('show')) {
            dpDisplay.addClass('active');
            label.removeClass('down');
            label.addClass('up');
        } else {
            dpDisplay.removeClass('active');
            label.removeClass('up');
            label.addClass('down');
        }
    };

    let resetHamburger = function (target) {
        let parent = target.parent().parent();
        let dropdowns = parent.find('.dropdown-display');
        for (let i = 0; i < dropdowns.length; i++) {
            if ($(dropdowns[i]).hasClass('active')) {
                $(dropdowns[i]).removeClass('active');
            }
        }

        let arrows = parent.find('.arrow');
        for (let i = 0; i < arrows.length; i++) {
            if ($(arrows[i]).hasClass('up')) {
                $(arrows[i]).removeClass('up');
                $(arrows[i]).addClass('down');
            }
        }
    };

    $(window).click(function (event) {
        let target = $(event.target);
        if (target.closest('.dropdown').length === 0) {
            let dropdowns = document.getElementsByClassName("dropdown-content");
            for (let i = 0; i < dropdowns.length; i++) {
                let openDropdown = $(dropdowns[i]);
                if (openDropdown.hasClass('show')) {
                    openDropdown.removeClass('show');
                }
            }
        }
    });

    module.exports = {
        hideDropdown: hideDropdown,
        hideDropdownContainer: hideDropdownContainer,
        dropdownClick: dropdownClick,
        dropdownHamburgerClick: dropdownHamburgerClick,
        dropdownDelay: dropdownDelay
    };
})(jQuery);