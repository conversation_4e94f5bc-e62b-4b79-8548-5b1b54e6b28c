{% trans_default_domain 'AppBundle' %}

<div id="menu-account-mobile" class="mobile-only">

    <section>
        <a href="{{ path('front.user.profile') }}">
            <div>
                <img src="{{ asset('images/ico-profile.svg') }}">
            </div>
            {{ 'menu.desktop.profile'| trans([],'AppBundle') }}
        </a>
        <div class="close-account-menu">
            <img alt="close menu" class="close-search-img" style="cursor: pointer" src="{{ asset('images/close-search.svg') }}" onclick="window.UI.Menu.closeMobileMenuAccount()">
        </div>
    </section>
    <section>
        <a href="{{ path('list.shipping.address', {'isMobile': true }) }}">
            <div>
                <img src="{{ asset('images/marker.svg') }}">
            </div>
            {{ 'menu.desktop.shipping_address'| trans([],'AppBundle') }}
        </a>
    </section>
    <section>
        <a href="{{ path('front.orders.list') }}">
            <div>
                <img src="{{ asset('images/ico-order.svg') }}">
            </div>
            {{ 'menu.desktop.command'| trans([],'AppBundle') }}
        </a>
    </section>
    {% if isManager %}
        <section>
            <a href="{{ path('front.ordersToValidate.categories') }}">
                <div style="position: relative">
                    {% if countOrdersToValidate > 0 %}
                        <div class="counter-bubble-container orders-counter">
                            <div class="counter-bubble">
                                {{ countOrdersToValidate }}
                            </div>
                        </div>
                    {% endif %}
                    <img src="{{ asset('images/ico-order.svg') }}">
                </div>
                {{ 'menu.desktop.orders_to_validate'| trans([],'AppBundle') }}
            </a>
        </section>
    {% endif %}
    <section>
        <a href="{{ path('front.quotes.list') }}">
            <div style="position: relative">
                {% if countQuotesToRead > 0 %}
                    <div class="counter-bubble-container orders-counter">
                        <div class="counter-bubble">
                            {{ countQuotesToRead }}
                        </div>
                    </div>
                {% endif %}
                <img src="{{ asset('images/icon-devis.svg') }}">
            </div>
            {{ 'menu.desktop.quotes'| trans([],'AppBundle') }}
        </a>
    </section>
    <section>
        <a href="{{ path('thread.list') }}">
            <div style="position: relative">
                {% if app.session.get('unreadMessage', 0) > 0 %}
                    <div class="counter-bubble-container messages-counter">
                        <div class="counter-bubble">
                            {{ app.session.get('unreadMessage', 0) }}
                        </div>
                    </div>
                {% endif %}
                <img class="pt-6" src="{{ asset('images/ico-message.svg') }}">
            </div>
            {{ 'menu.desktop.messages'| trans([],'AppBundle') }}
        </a>
    </section>
    {% if isManager and showValidationMenu%}
    <section>

            <a href="{{ path('front.user.delegation', {'isMobile': true }) }}">
                <div>
                    <img src="{{ asset('images/delegation.svg') }}">
                </div>
                {{ 'user.delegation.menu_title'| trans([],'AppBundle') }}
            </a>

    </section>
    {% endif %}
    {% if isManager %}
    <section>
        <a href="{{ path('front.reports.list') }}">
            <div>
                <img src="{{ asset('images/reporting.svg') }}">
            </div>
            {{ 'user.reports.menu_title'| trans([],'AppBundle') }}
        </a>
    </section>
    {% endif %}
    <section>
    <a href="{{ path('front.supplier.invitation.list') }}">
        <div>
            <img src="{{ asset('images/ico-invitation-off.svg') }}">
        </div>
        {{ 'menu.desktop.invitations'| trans([],'AppBundle') }}
    </a>
    </section>
    <section>
        <a href="{{ path('buyer.logout') }}" class="logout-link">
            <div >
                <svg class="Icon icon-grey" style="margin:0">
                    <use xlink:href="#icon-logout"></use>
                </svg>
            </div>
            {{ 'home.logout'| trans([],'AppBundle') }}
        </a>
    </section>
</div>

