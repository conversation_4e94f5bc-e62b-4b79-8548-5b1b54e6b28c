<?php

namespace Open\IdealBundle\Model;

class Entry
{
    private ?string $MainEmployeeId = null;
    private ?string $MainLastName = null;
    private ?string $MainPhoneticFirstName = null;
    private ?string $Id = null;
    private ?MainRecord $mainRecord = null;

    /**
     * @return string|null
     */
    public function getId(): ?string
    {
        return $this->Id;
    }

    /**
     * @param string|null $Id
     * @return Entry
     */
    public function setId(?string $Id): Entry
    {
        $this->Id = $Id;
        return $this;
    }

    /**
     * @return MainRecord|null
     */
    public function getMainRecord(): ?MainRecord
    {
        return $this->mainRecord;
    }

    /**
     * @param MainRecord|null $mainRecord
     * @return Entry
     */
    public function setMainRecord(?MainRecord $mainRecord): Entry
    {
        $this->mainRecord = $mainRecord;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getMainEmployeeId(): ?string
    {
        return $this->MainEmployeeId;
    }

    /**
     * @param string|null $MainEmployeeId
     * @return Entry
     */
    public function setMainEmployeeId(?string $MainEmployeeId): Entry
    {
        $this->MainEmployeeId = $MainEmployeeId;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getMainLastName(): ?string
    {
        return $this->MainLastName;
    }

    /**
     * @param string|null $MainLastName
     * @return Entry
     */
    public function setMainLastName(?string $MainLastName): Entry
    {
        $this->MainLastName = $MainLastName;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getMainPhoneticFirstName(): ?string
    {
        return $this->MainPhoneticFirstName;
    }

    /**
     * @param string|null $MainPhoneticFirstName
     * @return Entry
     */
    public function setMainPhoneticFirstName(?string $MainPhoneticFirstName): Entry
    {
        $this->MainPhoneticFirstName = $MainPhoneticFirstName;
        return $this;
    }


}