<?php

namespace AppBundle\Model\Cart;

use AppBundle\Entity\Country;
use AppBundle\Model\CustomsInfo;

class CartMerchant
{
    private const DELIVERY_OPTION_STANDARD = 'delivery-option-standard';
    private const DELIVERY_OPTION_FAST = 'delivery-option-fast';

    /**
     * @var int
     */
    private $id;

    /**
     * @var string
     */
    private $name;

    /**
     * @var string
     */
    private $description;

    /**
     * @var string
     */
    private $longDescription;

    /**
     * @var string
     */
    private $logoImage;

    /**
     * @var Country
     */
    private $country;

    /**
     * @var string
     */
    private $conditions;

    /**
     * @var array
     */
    private $subTotalVat;

    /**
     * @var float
     */
    private $subTotalWithoutVat;

    /**
     * @var float
     */
    private $deliveryPrice;

    /**
     * @var float
     */
    private $vat;

    /**
     * @var float
     */
    private $total;

    /**
     * @var float
     */
    private $totalWithoutDeliveryPrice;

    /**
     * @var array<CartItem>
     */
    private $items;

    /**
     * @var CustomsInfo
     */
    private $vatInformation;

    /**
     * @var float
     */
    private $rating;

    /**
     * @var bool
     */
    private $standardDeliveryOption;

    /**
     * @var float
     */
    private $standardDeliveryOptionPrice;

    /**
     * @var bool
     */
    private $fastDeliveryOption;

    /**
     * @var float
     */
    private $fastDeliveryOptionPrice;


    /** @var array */
    private $fastShippingOptions;

    /** @var array */
    private $standardShippingOptions;

    /** @var int */
    private $cartId;

    /**
     * @var string
     */
    private $selectedDeliveryOption;

    /**
     * @var string|null
     */
    private $comment;

    /**
     * @var string|null
     */
    private $commentPlaceHolder;

    /**
     * @var bool
     */
    private $merchantIsActive;

    private ?float $minimumOrderAmount = null;

    public function __construct()
    {
        $this->fastShippingOptions = [];
        $this->standardShippingOptions = [];
        $this->fastDeliveryOption = false;
        $this->standardDeliveryOption = false;
        $this->merchantIsActive = false;

    }

    public function getId(): int
    {
        return $this->id;
    }

    public function setId(int $id): self
    {
        $this->id = $id;
        return $this;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(string $name = null): self
    {
        $this->name = $name;
        return $this;
    }

    public function getDescription(): ?string
    {
        return $this->description;
    }

    public function setDescription(string $description = null): self
    {
        $this->description = $description;
        return $this;
    }

    public function getLongDescription(): ?string
    {
        return $this->longDescription;
    }

    public function setLongDescription(string $longDescription = null): self
    {
        $this->longDescription = $longDescription;
        return $this;
    }

    public function getLogoImage(): ?string
    {
        return $this->logoImage;
    }

    public function setLogoImage(string $logoImage = null): self
    {
        $this->logoImage = $logoImage;
        return $this;
    }

    public function getCountry(): Country
    {
        return $this->country;
    }

    public function setCountry(Country $country): self
    {
        $this->country = $country;
        return $this;
    }

    public function getConditions(): ?string
    {
        return $this->conditions;
    }

    public function setConditions(string $conditions = null): self
    {
        $this->conditions = $conditions;
        return $this;
    }

    public function getSubTotalVat(): array
    {
        return $this->subTotalVat;
    }

    public function setSubTotalVat(array $subTotalVat): self
    {
        $this->subTotalVat = $subTotalVat;
        return $this;
    }

    public function addSubTotalVat(string $taxRate, float $vat): self
    {
        if (!array_key_exists($taxRate, $this->subTotalVat)) {
            $this->subTotalVat[$taxRate] = 0;
        }

        $this->subTotalVat[$taxRate] += $vat;

        return $this;
    }

    public function getSubTotalWithoutVat(): float
    {
        return $this->subTotalWithoutVat;
    }

    public function setSubTotalWithoutVat(float $subTotalWithoutVat): self
    {
        $this->subTotalWithoutVat = $subTotalWithoutVat;
        return $this;
    }

    public function addSubTotalWithoutVat(float $total): self
    {
        $this->subTotalWithoutVat += $total;
        return $this;
    }

    public function getVat(): float
    {
        return $this->vat;
    }

    public function setVat(float $vat): self
    {
        $this->vat = $vat;
        return $this;
    }

    public function addVat(float $vat): self
    {
        $this->vat += $vat;
        return $this;
    }

    public function getTotal(): float
    {
        return $this->total;
    }

    public function setTotal(float $total): self
    {
        $this->total = $total;
        return $this;
    }

    public function addTotal(float $total): self
    {
        $this->total += $total;
        $this->totalWithoutDeliveryPrice += $total;

        return $this;
    }

    /**
     * @return array<CartItem>
     */
    public function getItems(): array
    {
        return $this->items;
    }

    /**
     * @param array<CartItem> $items
     * @return $this
     */
    public function setItems(array $items): self
    {
        $this->items = $items;
        return $this;
    }

    public function getVatInformation(): CustomsInfo
    {
        return $this->vatInformation;
    }

    public function setVatInformation(CustomsInfo $vatInformation): self
    {
        $this->vatInformation = $vatInformation;
        return $this;
    }

    public function getRating(): ?float
    {
        return $this->rating;
    }

    public function setRating(?float $rating): self
    {
        $this->rating = $rating;
        return $this;
    }

    public function getDeliveryPrice(): ?float
    {
        return $this->deliveryPrice;
    }

    public function setDeliveryPrice(float $deliveryPrice): self
    {
        $this->deliveryPrice = $deliveryPrice;
        return $this;
    }

    public function hasDeliveryOptions(): bool
    {
        return ($this->fastDeliveryOption || $this->standardDeliveryOption);
    }

    public function addStandardDeliveryShippingGroupItem(CartItemShippingGroup $cartItemShippingGroup)
    {
        $this->standardDeliveryOption = true;
        $this->standardDeliveryOptionPrice += $cartItemShippingGroup->getDeliveryPrice();
        $this->standardShippingOptions[] = $cartItemShippingGroup;
    }

    public function addFastDeliveryShippingGroupItem(CartItemShippingGroup $cartItemShippingGroup, bool $differentThanStandard = false)
    {
        $this->fastDeliveryOption = ($this->fastDeliveryOption || $differentThanStandard);
        $this->fastDeliveryOptionPrice += $cartItemShippingGroup->getDeliveryPrice();
        $this->fastShippingOptions[] = $cartItemShippingGroup;
    }

    public function getFastShippingOptions(): array
    {
        return $this->fastShippingOptions;
    }

    public function getStandardShippingOptions(): array
    {
        return $this->standardShippingOptions;
    }

    public function hasFastDeliveryOption(): bool
    {
        return ($this->fastDeliveryOption === true);
    }

    public function hasStandardDeliveryOption(): bool
    {
        return ($this->standardDeliveryOption === true);
    }

    public function getStandardDeliveryOptionPrice(): float
    {
        return $this->standardDeliveryOptionPrice;
    }

    public function getFastDeliveryOptionPrice(): float
    {
        return $this->fastDeliveryOptionPrice;
    }

    public function useStandardDeliveryOption(): self
    {
        $this->selectedDeliveryOption = self::DELIVERY_OPTION_STANDARD;
        $this->deliveryPrice = $this->standardDeliveryOptionPrice;
        $this->total = $this->totalWithoutDeliveryPrice;
        $this->total = $this->total + $this->deliveryPrice;

        $this->items = array_map(
            function(CartItem $cartItem) {

                $groupShippingOption = $cartItem->getStandardDeliveryOption();
                if ($groupShippingOption) {
                    $cartItem->setDeliveryTime($groupShippingOption->getDeliveryTime());
                }

                return $cartItem;
            },
            $this->items
        );

        return $this;
    }

    public function useFastDeliveryOption(): self
    {
        $this->selectedDeliveryOption = self::DELIVERY_OPTION_FAST;
        $this->deliveryPrice = $this->fastDeliveryOptionPrice;
        $this->total = $this->totalWithoutDeliveryPrice;
        $this->total = $this->total + $this->deliveryPrice;

        $this->items = array_map(
            function(CartItem $cartItem) {
                $groupShippingOption = $cartItem->getFastDeliveryOption();
                if ($groupShippingOption) {
                    $cartItem->setDeliveryTime($groupShippingOption->getDeliveryTime());
                }

                return $cartItem;
            },
            $this->items
        );

        return $this;
    }

    public function isStandardDeliveryOptionSelected(): bool
    {
        return ($this->selectedDeliveryOption === self::DELIVERY_OPTION_STANDARD);
    }

    public function isFastDeliveryOptionSelected(): bool
    {
        return  ($this->selectedDeliveryOption === self::DELIVERY_OPTION_FAST);
    }

    public function getCartId(): int
    {
        return $this->cartId;
    }

    public function setCartId(int $cartId): self
    {
        $this->cartId = $cartId;
        return $this;
    }

    public function getComment(): ?string
    {
        return $this->comment;
    }

    public function setComment(?string $comment): self
    {
        $this->comment = $comment;
        return $this;
    }

    public function getCommentPlaceHolder(): ?string
    {
        return $this->commentPlaceHolder;
    }

    public function setCommentPlaceHolder(?string $commentPlaceHolder): self
    {
        $this->commentPlaceHolder = $commentPlaceHolder;
        return $this;
    }

    /**
     * @return bool
     */
    public function isMerchantIsActive(): bool
    {
        return $this->merchantIsActive;
    }

    /**
     * @param bool $merchantIsActive
     */
    public function setMerchantIsActive(bool $merchantIsActive): void
    {
        $this->merchantIsActive = $merchantIsActive;
    }

    public function getMinimumOrderAmount(): ?float
    {
        return $this->minimumOrderAmount;
    }

    public function setMinimumOrderAmount(?float $minimumOrderAmount): self
    {
        $this->minimumOrderAmount = $minimumOrderAmount;
        return $this;
    }

    /**
     * @return float
     */
    public function getTotalWithoutDeliveryPrice(): float
    {
        return $this->totalWithoutDeliveryPrice;
    }
}
