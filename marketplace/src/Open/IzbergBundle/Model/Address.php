<?php
/**
 * Created by PhpStorm.
 * User: CMA15450
 * Date: 24/03/2017
 * Time: 16:27
 */
namespace Open\IzbergBundle\Model;

use Symfony\Component\Validator\Constraints as Assert;
use <PERSON><PERSON>\Serializer\Annotation\Type;
use <PERSON><PERSON>\Serializer\Annotation as JSM;


/**
 * Entity to map an Address object
 * @JSM\ExclusionPolicy("none")
 */
class Address
{

    /**
     * @Type("integer")
     */
    private $id;

    /**
     * @Assert\NotBlank()
     * @Type("string")
     */
    private $name;


    /**
     * @Assert\NotBlank()
     * @Type("string")
     */
    private $first_name;


    /**
     * @Assert\NotBlank()
     * @Type("string")
     */
    private $last_name;



    /**
     * @Assert\NotBlank()
     * @Type("string")
     */
    private $phone;

    /**
     * @Assert\NotBlank()
     * @Type("string")
     */
    private $fax;


    /**
     * @Assert\NotBlank()
     * @Type("string")
     */
    private $address;

    /**
     * @Type("string")
     */
    private $address2;

    /**
     * @Assert\NotBlank()
     * @Type("string")
     */
    private $zipcode;

    /**
     * @Assert\NotBlank()
     * @Type("string")
     */
    private $city;

    /**
     * @Assert\NotBlank()
     * @Type("Open\IzbergBundle\Model\Country")
     * @JSM\Accessor(getter="getCountry",setter="setCountry")
     */
    private $country;

    /**
     * @Type("string")
     */
    private $state;

    /**
     * Get id
     * @return integer
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * Set id
     * @param $id
     * @return integer
     */
    public function setId($id)
    {
        return $this->id = $id;
    }

    /**
     * Get address name
     * @return string
     */
    public function getName()
    {
        return $this->name;
    }

    /**
     * @return mixed
     */
    public function getFirstName()
    {
        return $this->first_name;
    }

    /**
     * set Address name
     * @param $name
     * @return string
     */
    public function setName($name)
    {
        return $this->name = $name;
    }

    /**
     * @param mixed $first_name
     */
    public function setFirstName($first_name): void
    {
        $this->first_name = $first_name;
    }

    /**
     * @return mixed
     */
    public function getLastName()
    {
        return $this->last_name;
    }

    /**
     * @param mixed $last_name
     */
    public function setLastName($last_name): void
    {
        $this->last_name = $last_name;
    }

    /**
     * Get address name
     * @return string
     */
    public function getPhone()
    {
        return $this->phone;
    }

    /**
     * set Address name
     * @param $name
     * @return string
     */
    public function setPhone($phone)
    {
        return $this->phone = $phone;
    }

    /**
     * Get address name
     * @return string
     */
    public function getFax()
    {
        return $this->fax;
    }

    /**
     * set Address name
     * @param $name
     * @return string
     */
    public function setFax($fax)
    {
        return $this->fax = $fax;
    }


    /**
     * Get address line 1
     * @return string
     */
    public function getAddress()
    {
        return $this->address;
    }

    /**
     * Set address line 1
     * @param $address
     * @return string
     */
    public function setAddress($address)
    {
        return $this->address = $address;
    }

    /**
     * Get address line 2
     * @return string
     */
    public function getAddress2()
    {
        return $this->address2;
    }

    /**
     * Set address line 2
     * @param $address
     * @return string
     */
    public function setAddress2($address)
    {
        return $this->address2 = $address;
    }

    /**
     * Get Zip Code
     * @return mixed
     */
    public function getZipcode()
    {
        return $this->zipcode;
    }

    /**
     * Set Zip Code
     * @param $zipcode
     * @return mixed
     */
    public function setZipcode($zipcode)
    {
        return $this->zipcode = $zipcode;
    }

    /**
     * Get city name
     * @return string
     */
    public function getCity()
    {
        return $this->city;
    }

    /**
     * Set city name
     * @param $city
     * @return string
     */
    public function setCity($city)
    {
        return $this->city = $city;
    }

    /**
     * Get Country
     * Todo: Fix inconsistancies (Izberg API returns either a Country object or a resource_uri)
     * @return mixed
     */
    public function getCountry()
    {
            // Return the resource uri instead of the whole country
            return $this->country;

    }

    /**
     * Set country
     * @param string Country $country
     */
    public function setCountry($country)
    {
        /** @var Country $country */
        $this->country = $country;
    }

    /**
     * @return string State
     */
    public function getState()
    {
        return $this->state;
    }

    /**
     * @param string $state
     */
    public function setState($state): void
    {
        $this->state = $state;
    }


}
