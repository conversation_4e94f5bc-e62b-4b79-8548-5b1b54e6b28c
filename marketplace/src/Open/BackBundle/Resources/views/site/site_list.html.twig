{% extends '@OpenBack/base.html.twig' %}
{% form_theme form 'bootstrap_4_layout.html.twig' %}
{% trans_default_domain "AppBundle" %}

{% block body %}
    <div class="d-flex export-btn">
        <a href="{{ path('admin.site.export', {'qualifier': qualifier_val}) }}" class="btn btn-sm btn-primary btn-dark" role="button" aria-pressed="true">{{ 'back.commons.export_csv'|trans }}</a>
        <button type="button" class="btn btn-sm btn-primary btn-dark" style="margin-left: 20px;" data-toggle="modal" data-target="#modalNewSite">{{ 'back.site.list.add'|trans }}</button>
    </div>

    <div class="d-flex justify-content-center">
        <table class="table table-hover">
            <thead>
            <tr>
                {# sorting of properties based on query components #}
                <th>{{ 'back.commons.id'|trans }}</th>
                <th>{{ knp_pagination_sortable(pagination, 'back.site.list.name'|trans, 's.name') }}</th>
                <th>{{ knp_pagination_sortable(pagination, 'back.site.list.company'|trans, 'c.name') }}</th>
                <th>{{ 'back.site.list.nbShippingPoints' | trans }}</th>
                <th>{{ 'back.commons.actions'|trans }}</th>
            </tr>
            <tr>
                {{ form_start(form) }}
                <th>{{ form_widget(form.id, {'attr': {'style': 'width:50px'}}) }}</th>
                <th>{{ form_widget(form.name) }}</th>
                <th>{{ form_widget(form.company) }}</th>
                <th></th>
                <input type="submit" style="opacity:0; position: fixed; left: -9999px;"></input>
                {{ form_end(form) }}
            </tr>
            </thead>
            <tbody>
            {% for site in pagination %}
                <tr>
                    <td>{{ site.id }}</td>
                    <td>{{ site.name }}</td>
                    <td>
                        {% if site.company %}
                            {{ site.company.name }}
                        {% endif %}
                    </td>
                    <td>
                        {% if site.shippingPoints %}
                               {{ site.shippingPoints | length }}
                            {% else %}
                            0
                        {% endif %}
                    </td>
                    <td class="text-center">
                        <a class="action" href="{{ path('admin.site.info', {'id':site.id}) }}">
                            <span title="{{ 'back.commons.edit'|trans }}">
                                <svg class="Icon">
                                    <use xlink:href="#eye" />
                                </svg>
                            </span>
                        </a>
                    </td>
                </tr>
            {% endfor %}
            </tbody>
        </table>
    </div>
    {# display navigation #}
    <div class="d-flex justify-content-left">
        <div class="navigation col-md-10">
            {{ knp_pagination_render(pagination) }}
        </div>
        <div class="col-md-2">Total: {{ pagination.getTotalItemCount }}</div>
    </div>

    <div id="modalNewSite" class="modal Modal" tabindex="-1" role="dialog" aria-labelledby="modalNewSite" aria-hidden="true" data-backdrop="static">
        <div class="modal-dialog">
            <div class="modal-content Modal-content js-modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" >{{ 'site.form.add_modal'|trans }}</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                </div>
                <div class="modal-body">
                    {{ form_start(formModal) }}
                    <div class="form-group">
                        {{ form_row(formModal.name, {'attr' : {'class' : 'form-control'}}) }}
                    </div>
                    <div class="form-group">
                        {{ form_row(formModal.company, {'attr' : {'class' : 'form-control'}}) }}
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary js-cancel-button" data-dismiss="modal">{{ 'modal.cancel'|trans }}</button>
                    <button id="js-add-lang-button" class="btn btn-info">{{ 'node.form.lang.button'|trans  }}</button>
                </div>
                {{ form_end(formModal) }}
            </div>
        </div>
    </div>

    <script type="application/javascript">
        jQuery(document).ready(function() {
            $(".filter_select").change(function() {
                $('form[name="filter_site"]').submit();
            });

            $("#clearForm").click(function() {
                $(".resetable_input").val("");
                $('form[name="filter_site"]').submit();
            });

            let selectCountryText = $("#filter_site_country option:selected").text();

            $("#filter_site_country").html($('#filter_site_country option').sort(function(x, y) {
                return $(x).text().toUpperCase() < $(y).text().toUpperCase() ? -1 : 1;
            }));

            $("#filter_site_country option").filter(function() {
                return $(this).text() === selectCountryText;
            }).prop('selected', true);

        });

    </script>
{% endblock %}