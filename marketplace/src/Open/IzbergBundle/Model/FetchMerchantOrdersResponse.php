<?php
/**
 * Created by PhpStorm.
 * User: QAR14123
 * Date: 08/06/2018
 * Time: 13:04
 */

namespace Open\IzbergBundle\Model;

use Doctrine\Common\Collections\ArrayCollection;
use <PERSON><PERSON>\Serializer\Annotation as JSM;
use <PERSON><PERSON>\Serializer\Annotation\Type;

/**
 * Entity to map a Izberg currency object
 * @JSM\ExclusionPolicy("none")
 */
class FetchMerchantOrdersResponse
{
    /**
     * @Type("ArrayCollection<Open\IzbergBundle\Model\OrderMerchant>")
     */
    private $objects;


    public function getObjects(): ArrayCollection
    {
        return $this->objects;
    }

    /**
     * @param mixed $objects
     */
    public function setObjects($objects)
    {
        $this->objects = $objects;
    }

    public function hasMerchantOrders()
    {
        return ($this->getObjects()->count());
    }
}