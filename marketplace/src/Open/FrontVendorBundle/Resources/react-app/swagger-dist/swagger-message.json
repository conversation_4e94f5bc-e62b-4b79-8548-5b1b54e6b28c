{"swagger": "2.0", "info": {"description": "Message Api Documentation", "version": "1.0.0", "title": ""}, "host": "localhost:8080", "basePath": "/api/message", "tags": [{"name": "Message", "description": "Message API"}], "paths": {"/message/{threadId}": {"get": {"tags": ["Message"], "summary": "get list of message", "description": "get list of message", "operationId": "getMessages", "parameters": [{"in": "path", "name": "threadId", "description": "Thread id", "required": true, "type": "integer", "format": "int64"}], "produces": ["application/json"], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/PaginatedMessage"}}, "400": {"description": "Bad request", "schema": {"$ref": "#/definitions/ApiError"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/ApiError"}}, "503": {"description": "Service Unavailable"}}}}, "/message/refresh/{threadId}": {"get": {"tags": ["Message"], "summary": "get list of unread message", "description": "get list of unread message", "operationId": "refreshMessageView", "parameters": [{"in": "path", "name": "threadId", "description": "Thread id", "required": true, "type": "integer", "format": "int64"}], "produces": ["application/json"], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/PaginatedMessage"}}, "400": {"description": "Bad request", "schema": {"$ref": "#/definitions/ApiError"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/ApiError"}}, "503": {"description": "Service Unavailable"}}}}, "/message/{threadId}/create": {"post": {"tags": ["Message"], "summary": "Create a message", "description": "Create a message", "operationId": "createMessage", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "path", "name": "threadId", "description": "Thread id", "required": true, "type": "integer", "format": "int64"}, {"in": "body", "name": "quote", "description": "quote to create ", "required": true, "schema": {"$ref": "#/definitions/QuoteMessage"}}], "responses": {"201": {"description": "Created", "schema": {"$ref": "#/definitions/QuoteMessage"}}, "400": {"description": "Bad request", "schema": {"$ref": "#/definitions/ApiError"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/ApiError"}}, "503": {"description": "Service Unavailable"}}}}}, "definitions": {"User": {"type": "object", "properties": {"id": {"description": "id of the user", "type": "integer", "format": "int64"}, "firstname": {"description": "first name of the user", "type": "integer", "format": "int64"}, "lastname": {"description": "last name of the user", "type": "integer", "format": "int64"}}}, "Merchant": {"type": "object", "properties": {"id": {"description": "id of the Merchant", "type": "integer", "format": "int64"}, "name": {"description": " name of the merchant", "type": "integer", "format": "int64"}}}, "QuoteMessage": {"type": "object", "properties": {"created_at": {"description": "date of the message", "type": "string", "format": "date-time"}, "sender": {"description": "sender", "type": "string"}, "sender_id": {"description": "sender id", "type": "integer", "format": "int64"}, "status": {"description": "status", "type": "string"}, "body": {"description": "body of message", "type": "string"}, "event_name": {"description": "event_name", "type": "string"}, "files": {"description": "quote item list details", "type": "array", "items": {"$ref": "#/definitions/MessageAttachment"}}}}, "MessageAttachment": {"type": "object", "properties": {"id": {"description": "id of the attachment", "type": "integer", "format": "int64"}, "fileName": {"description": " name of the attachment", "type": "string"}}}, "PaginatedMessage": {"description": "Pagination system", "type": "object", "properties": {"current_page": {"description": "current page", "type": "integer"}, "items": {"type": "array", "items": {"$ref": "#/definitions/QuoteMessage"}}, "page_count": {"description": " number of page", "type": "integer"}, "items_per_page": {"description": "item per page", "type": "integer"}, "total_item_count": {"description": "total number of item", "type": "integer"}}}, "ApiError": {"type": "object", "properties": {"code": {"description": "Error code", "type": "string"}, "label": {"description": "Error label", "type": "string"}}}}}