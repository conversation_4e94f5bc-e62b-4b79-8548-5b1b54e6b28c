<li class="dropdown {{ extendedClass }}">
    <div class="dropdown-display"
            {% if hamburger is defined and hamburger %}
                onclick="window.UI.Dropdown.dropdownHamburgerClick('{{ idContent }}')"
            {% elseif extendedClass is defined and extendedClass == 'categories' %}
                onmouseover="window.UI.Dropdown.dropdownDelay('{{ idContent }}')"
            {% else %}
                onclick="window.UI.Dropdown.dropdownDelay('{{ idContent }}')"
            {% endif %}
         onmouseleave="window.UI.Dropdown.hideDropdown('{{ idContent }}')"
    >
        {% block display %}
        {% endblock %}
    </div>

    <div id="{{ idContent }}" class="dropdown-content">
        <div class="dropdown-list" {% if notMouseLeave is not defined or notMouseLeave != true %} onmouseleave="window.UI.Dropdown.hideDropdownContainer()" {% endif %}>
            {% block dropdownList %}
            {% endblock %}
        </div>
        {% if dropdownCache is defined and dropdownCache %}
            <div class="dropdown-cache"></div>
        {% endif %}
    </div>
</li>