<?php

namespace Open\BackBundle\Controller;

use AppBundle\Controller\MkoController;
use AppBundle\Entity\Site;
use AppBundle\Entity\User;
use AppBundle\Form\UserForm;
use AppBundle\Model\Id;
use AppBundle\Repository\CompanyRepository;
use AppBundle\Repository\SiteRepository;
use AppBundle\Services\ActionHistoryService;
use AppBundle\Services\BuyerService;
use AppBundle\Services\CountryService;
use AppBundle\Services\CSVService;
use AppBundle\Services\LanguageService;
use AppBundle\Services\MailService;
use AppBundle\Services\MarketPlaceService;
use AppBundle\Services\SecurityService;
use AppBundle\Services\UserBddService;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\ORM\EntityManagerInterface;
use Open\BackBundle\Form\FilterAdminType;
use Open\BackBundle\Form\FilterBuyerType;
use Open\BackBundle\Form\IdForm;
use Open\BackBundle\Form\UserMarketPlaces;
use Open\BackBundle\Form\UserRoles;
use AppBundle\Entity\Company;
use Open\LogBundle\Service\LogService;
use Open\LogBundle\Utils\EventNameEnum;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Security;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Routing\Generator\UrlGeneratorInterface;
use Symfony\Component\Security\Csrf\TokenGenerator\TokenGeneratorInterface;
use Symfony\Contracts\EventDispatcher\Event;
use Symfony\Contracts\Translation\TranslatorInterface;

class Usercontroller extends MkoController
{
    const TRANSLATOR = 'translator';
    const TRANSLATION_DOMAIN = 'AppBundle';
    const USER_SERVICE = 'service.user';
    const USER_BUNDLE = 'AppBundle:User';
    const BUYERADDRESS_BUNDLE = 'AppBundle:BuyerAddress';
    const METACART_BUNDLE = 'AppBundle:MetaCart';
    const MARKETPLACE_BUNDLE = 'AppBundle:Marketplace';
    const ROLE_SUPER_ADMIN = 'ROLE_SUPER_ADMIN';
    const SUCCESS = 'success';
    const ERROR = 'error';
    const ROUTE_INFO = 'admin.user.info';
    private const MANUALLY = true;

    private UserBddService $userBddService;
    private TranslatorInterface $translator;
    private LanguageService $languageService;
    private BuyerService $buyerService;
    private LogService $logger;
    private MailService $mailService;
    private EntityManagerInterface $em;

    public function __construct(
        UserBddService      $userBddService,
        TranslatorInterface $translator,
        LanguageService     $languageService,
        BuyerService        $buyerService,
        LogService          $logger,
        MailService         $mailService,
        EntityManagerInterface $em
    )
    {
        $this->userBddService = $userBddService;
        $this->translator = $translator;
        $this->languageService = $languageService;
        $this->buyerService = $buyerService;
        $this->logger = $logger;
        $this->mailService = $mailService;
        $this->em = $em;
    }

    /**
     * @Route("/user/list/{qualifier}", name="admin.user.list", defaults={"_locale"="en"})
     * @Security("is_granted('ROLE_ADMIN') or is_granted('ROLE_OPERATOR')")
     */
    public function listBuyer(Request $request, $qualifier)
    {
        $options = [];
        $options['admin'] = true;

        $form = $this->createForm(FilterBuyerType::class, null, $options);
        $form->remove('service');
        $form->handleRequest($request);
        if ($form->isSubmitted() && $form->isValid()) {
            $filter = $form->getData();
        } else {
            $filter = [];
        }
        $paginator = $this->userBddService->getBuyerFilteredPaginator(1, 200, $request, $filter, $qualifier);

        //render list of users
        return $this->render('@OpenBack/user/buyer_list.html.twig', [
            'pagination' => $paginator,
            'qualifier_val' => $qualifier,
            'form' => $form->createView(),
            'locale' => $request->getLocale()
        ]);
    }

    /**
     * @Route("/user/status", name="admin.user.status", defaults={"_locale"="en"}, methods="POST")
     * @Security("is_granted('ROLE_ADMIN') or is_granted('ROLE_OPERATOR')")
     */
    public function setBuyerStatusAction(Request $request, BuyerService $buyerService): Response
    {
        if ($request->isMethod(Request::METHOD_POST) && $this->isCsrfTokenValid('set_buyers_status', (string) $request->request->get('token'))) {
            $status = (bool)$request->request->get('setStatus');
            $buyerIggs = $request->request->get('buyers');

            if (is_array($buyerIggs)) {
                foreach ($buyerIggs as $igg) {
                    $buyer = $buyerService->getBuyerFromUsername($igg);

                    if ($buyer instanceof User) {
                        $this->logger->info(
                            "Change buyer Status",
                            $status ? EventNameEnum::USER_ENABLE : EventNameEnum::USER_DISABLE,
                            $this->getUser()->getUsername(), ["USERNAME" => $igg]
                        );

                        try {
                            $buyerService->setBuyerStatus($buyer, $this->getUser(), $status, self::MANUALLY);
                        } catch (\Exception $e) {
                            $this->logger->error(
                                self::class . '::SetBuyerStatus ' . $e->getTraceAsString(),
                                $status ? EventNameEnum::USER_ENABLE : EventNameEnum::USER_DISABLE,
                                $this->getUser()->getUsername(), ["USERNAME" => $igg]
                            );
                        }
                    }
                }

                return new Response('ok');
            }
            return new Response('Error', Response::HTTP_BAD_REQUEST);
        }

        return new Response('Not,allowed', Response::HTTP_FORBIDDEN);
    }

    /**
     * @Route("/admin/list/{qualifier}", name="admin.admin.list", defaults={"_locale"="en"})
     * @Security("is_granted('ROLE_ADMIN') or is_granted('ROLE_OPERATOR')")
     */
    public function listAdmins(Request $request, $qualifier)
    {
        $options = [];
        $options['admin'] = true;

        $form = $this->createForm(FilterAdminType::class, null, $options);
        $form->handleRequest($request);
        if ($form->isSubmitted() && $form->isValid()) {
            $filter = $form->getData();
        } else {
            $filter = [];
        }

        $filter['page'] = 'adminList';

        $paginator = $this->userBddService->getCustomUsersFilteredPaginator(1, 25, $request, $filter, $qualifier);

        //render list of users
        return $this->render('@OpenBack/user/admin_list.html.twig', [
            'pagination' => $paginator,
            'qualifier_val' => $qualifier,
            'form' => $form->createView(),
            'locale' => $request->getLocale()
        ]);
    }

    /**
     * @Route("/user/{id}/info", name="admin.user.info")
     * @Security("is_granted('ROLE_ADMIN') or is_granted('ROLE_OPERATOR')")
     */
    public function getUserInformations(Request $request, EntityManagerInterface $em, ActionHistoryService $actionHistoryService, $id): ?Response
    {
        /** @var User $user */
        $user = $em->getRepository(self::USER_BUNDLE)->find($id);

        //operator cannot see admin informations
        if (!$this->isGranted(self::ROLE_SUPER_ADMIN) && $user->getRoles()[0] == self::ROLE_SUPER_ADMIN) {
            throw $this->createAccessDeniedException($this->translator->trans(self::ACCESS_DENIED_EXCEPTION));
        }

        $isAdmin = false;
        if ($user->getRoles()[0] == self::ROLE_SUPER_ADMIN || $user->getRoles()[0] == self::ROLE_OPERATOR) {
            $isAdmin = true;
        }

        // Get all the user relationships : superiors (must be one), delegates (could be multiple)
        $superior = $this->buyerService->getBuyerSuperior($user);

        $delegates = $this->buyerService->getBuyerDelegates($user);

        $paginator = $this->userBddService->getUserConnectionsPaginator(1, 10, $request, $id);

        $actionPaginator = $actionHistoryService->getHistoryPaginatorForEntity(1, 10, $request, $user->getTechnicalId());

        $updateUserRolesForm = $this->createForm(UserRoles::class, ['userRoles' => $user->getRoles()]);
        $updateUserRolesForm->handleRequest($request);

        if ($updateUserRolesForm->isSubmitted() && $updateUserRolesForm->isValid()) {
            $data = $updateUserRolesForm->getData();
            $user->setRoles($data['userRoles']);
            $this->userBddService->updateUser($user);
        }

        $updateUserMarketPlaceForm = $this->createForm(UserMarketPlaces::class, ['marketplace' => $user->getMarketPlace()]);
        $updateUserMarketPlaceForm->handleRequest($request);

        if ($updateUserMarketPlaceForm->isSubmitted() && $updateUserMarketPlaceForm->isValid()) {
            $data = $updateUserMarketPlaceForm->getData();
            $this->buyerService->switchUserMarketplace($user, $data['marketplace']);
        }

        return $this->render('@OpenBack/user/user_info.html.twig', [
            'user' => $user,
            'isAdmin' => $isAdmin,
            'superiors' => $superior,
            'delegates' => $delegates,
            'pagination' => $paginator,
            'actionPagination' => $actionPaginator,
            'locale' => $request->getLocale(),
            'currentUser' => $this->getUser(),
            'form_activate_user' => $this->createForm(IdForm::class, new Id($user->getId()))->createView(),
            'form_reset_password_user' => $this->createForm(IdForm::class, new Id($user->getId()))->createView(),
            'updateUserRolesForm' => $updateUserRolesForm->createView(),
            'updateUserMarketplacesForm' => $updateUserMarketPlaceForm->createView(),
        ]);
    }

    /**
     * @Route("/user/add", name="admin.user.add")
     * @Security("is_granted('ROLE_ADMIN') or is_granted('ROLE_OPERATOR')")
     */
    public function addUser(Request $request, EntityManagerInterface $em, EventDispatcherInterface $dispatcher, SecurityService $securityService, UserBddService $userBddService, MarketPlaceService $marketPlaceService, CountryService $countryService)
    {
        $company = null;
        $error = false;

        $sites = new ArrayCollection();

        $from_company_list = false;

        /** @var User $user */
        $user = new User();
        $user->setRoles(['']);
        $groups = ['user', 'Default', 'user_registration', 'user_sites'];

        $options = [
            'validation_groups' => $groups,
            'method' => 'patch',
            'super_admin' => $this->isGranted(self::ROLE_SUPER_ADMIN),
            'languageService' => $this->languageService
        ];

        $form = $this->createForm(
            UserForm::class,
            $user,
            $options
        );


        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            try {
                /** @var User $user */
                $user = $form->getData();

                $username = uniqid('user_', true);
                $user->setUsername($username);
                $user->setEnabled(true);
                $password = $this->createFakePassword();
                $user->setPassword($password);
                $user->setPlainPassword($password);
                $userBddService->setNewTokenPassword($user);
                $user->setMarketPlace($marketPlaceService->getMarketPlaceByName('france'));
                $user->setCountryOfDelivery($countryService->getCountryByCode('france'));

                if ($user->getRole() == 'ROLE_OPERATOR' || $user->getRole() == 'ROLE_SUPER_ADMIN') {
                    $user->setCompany(null);

                }


                $event = new Event();
                $dispatcher->dispatch($event);

                $em->persist($user);
                $em->flush();
                $this->addFlash(self::SUCCESS, $this->translator->trans('back.user.form.creation.ok', [], self::TRANSLATION_DOMAIN));
                $this->logAccountCreation($user);

                //check admin and operator at same time
                if ($securityService->isAdmin($user)) {
                    $this->notifyAdminAccountCreation($user);
                }

                return $this->redirectToRoute('admin.admin.list', ['qualifier' => 'all']);

            } catch (\Exception $e) {
                $this->logger->error(sprintf("Error during saving process : %s", $e->getMessage()), "admin_user_add");
                $username_email = $form->getData()->getEmail();
                $user = $this->userBddService->findUserByEmail($username_email);

                // If the user exists
                if ($user) {
                    if (!$user->isEnabled()) {
                        $this->addFlash('danger', $this->translator->trans('back.user.form.creation.mailKoDisabled', [], 'AppBundle'));
                    } else {
                        $this->addFlash('danger', $this->translator->trans('back.user.form.creation.mailKo', [], 'AppBundle'));
                    }
                } else {
                    $this->addFlash('danger', $this->translator->trans('back.user.form.creation.ko', [], 'AppBundle'));
                }
            }
        }
        return $this->render('@OpenBack/user/user_edit.html.twig', [
            'form' => $form->createView(),
            'from_company_list' => $from_company_list
        ]);
    }


    /**
     * @Route("/user/{id}/edit", name="admin.user.edit")
     * @Route("/company/{companyId}/user/{id}/edit", name="admin.company.user.edit")
     * @Security("is_granted('ROLE_ADMIN') or is_granted('ROLE_OPERATOR')")
     */
    public function editUser(Request $request, EntityManagerInterface $em, CompanyRepository $companyRepository, SiteRepository $siteRepository, $id, $companyId = null)
    {
        $error = false;
        $from_company_list = false;
        $sites = new ArrayCollection();

        /** @var User $user */
        $user = $em->getRepository(self::USER_BUNDLE)->find($id);

        //operator cannot see admin informations
        if (!$this->isGranted(self::ROLE_SUPER_ADMIN) && $user->getRoles()[0] == self::ROLE_SUPER_ADMIN) {
            throw $this->createAccessDeniedException($this->translator->trans(self::ACCESS_DENIED_EXCEPTION));
        }

        $options = [
            'validation_groups' => ['Default'],
            'method' => 'patch',
            'super_admin' => $this->isGranted(self::ROLE_SUPER_ADMIN),
            'languageService' => $this->languageService,
        ];

        if ($companyId) {
            /* @var Company $company */
            $company = $companyRepository->find($companyId);
            $sites = $company->getSites();
            $from_company_list = true;
            $options['from_company_list'] = true;
            $options['company_id'] = $companyId;
        }

        $form = $this->createForm(
            UserForm::class,
            $user,
            $options
        );

        if ($from_company_list) {
            $form->remove('company');
        }

        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            try {
                /** @var User $user */
                $user = $form->getData();

                if ($user->getRole() == 'ROLE_OPERATOR' || $user->getRole() == 'ROLE_SUPER_ADMIN') {
                    $user->setCompany(null);

                } else {
                    if ($from_company_list) {
                        $user->setCompany($company);
                    }
                    if (is_null($user->getSite()) && !$sites->isEmpty()) {
                        $this->addFlash('danger', $this->translator->trans('back.user.form.sitesUserKo', [], 'AppBundle'));
                        $error = true;
                    } else {
                        $userSiteList = new ArrayCollection();
                        $site = $siteRepository->find($user->getSite()->getId());
                        $userSiteList->add($site);

                    }
                }
                $this->logger
                    ->info(
                        $this->translator->trans('log.profile.save'),
                        EventNameEnum::PROFILE_SAVE,
                        $user->getUsername(),
                        [
                            'id' => $user->getId()
                        ]
                    );

                if (!$error) {
                    $em->persist($user);
                    $em->flush();
                    $this->addFlash(self::SUCCESS, $this->translator->trans('back.user.form.modification.ok', [], self::TRANSLATION_DOMAIN));
                    if ($from_company_list) {
                        return $this->redirectToRoute('admin.company.users', ['id' => $company->getId()]);
                    } else {
                        return $this->redirectToRoute(self::ROUTE_INFO, ['id' => $user->getId()]);
                    }
                }
            } catch (\Exception $e) {
                $this->addFlash(self::ERROR, $this->translator->trans('back.user.form.modification.ko', [], self::TRANSLATION_DOMAIN));
            }
        }

        return $this->render('@OpenBack/user/user_edit.html.twig', [
            'form' => $form->createView(),
            'from_company_list' => $from_company_list,
            'userId' => $id
        ]);
    }

    /**
     * @Route("/user/activate", name="admin.user.activate", methods={"POST"})
     * @Security("is_granted('ROLE_ADMIN') or is_granted('ROLE_OPERATOR')")
     * @param Request      $request
     * @param BuyerService $buyerService
     *
     * @return mixed
     */
    public function activateUser(Request $request, EntityManagerInterface $em, LogService $logger)
    {
        $form = $this->createForm(IdForm::class, null, []);
        $form->handleRequest($request);

        $id = $form->getData()['id'];

        if ($form->isSubmitted() && $id !== null) {

            /** @var User $user */
            $user = $em->getRepository(self::USER_BUNDLE)->find($id);

            //add some logs
            $logger->info('user has been enabled', EventNameEnum::USER_ENABLE, $this->getUser()->getUsername(), [
                'USER_ID' => $id,
                'ENABLER_ID' => $user->getUserEnabler()?->getId(),
                'USER_ENABLED' => $user->isEnabled(),
                'ENABLER_ENABLED' => $user->getUserEnabler()?->getEnabled(),
            ]);

            if ($this->isGranted(self::ROLE_BUYER, $user)) {
                $this->buyerService->setBuyerStatus($user, $this->getUser(), true, self::MANUALLY);
            }

            //operator cannot see admin information
            if (!$this->isGranted(self::ROLE_SUPER_ADMIN) && $user->getRoles()[0] === self::ROLE_SUPER_ADMIN) {
                throw $this->createAccessDeniedException($this->translator->trans(self::ACCESS_DENIED_EXCEPTION));
            }

            $user->setEnabled(true);
            $user->setDisabledAt(null);

            try {
                $em->merge($user);
                $em->flush();
                $this->addFlash(self::SUCCESS, $this->translator->trans('back.user.form.activation.ok', [], self::TRANSLATION_DOMAIN));
            } catch (\Exception $e) {
                $this->addFlash(self::ERROR, $this->translator->trans('back.user.form.activation.ko', [], self::TRANSLATION_DOMAIN));
            }

            return $this->redirectToRoute(self::ROUTE_INFO, ['id' => $user->getId()]);
        }

        $logger->error('user sends invalid values for activating user', EventNameEnum::SECURITY_EVENT, $this->getUsername());
        throw $this->createAccessDeniedException();
    }

    /**
     * @Route("/user/deactivate", name="admin.user.deactivate", methods={"POST"})
     * @Security("is_granted('ROLE_ADMIN') or is_granted('ROLE_OPERATOR')")
     * @param Request $request
     *
     * @return mixed
     */
    public function deactivateUserFromUserPage(Request $request)
    {
        return $this->deactivateUser($request, false);
    }


    /**
     * @Route("/company/user/deactivate", name="admin.company.user.deactivate", methods={"POST"})
     * @Security("is_granted('ROLE_ADMIN') or is_granted('ROLE_OPERATOR')")
     * @param $id
     *
     * @return mixed
     */
    public function deactivateUserFromCompany(Request $request)
    {
        return $this->deactivateUser($request, true);
    }


    /**
     * @param      $id
     * @param bool $fromCompany
     *
     * @return mixed
     */
    private function deactivateUser(Request $request, bool $fromCompany)
    {
        $form = $this->createForm(IdForm::class, null, []);
        $form->handleRequest($request);

        $id = $form->getData()['id'];

        if ($form->isSubmitted() && $id !== null) {

            //add some logs
            $this->logger->info("user has been disabled", EventNameEnum::USER_DISABLE, $this->getUser()->getUsername(), ["USER_ID" => $id]);


            /** @var User $user */
            $user = $this->em->getRepository(self::USER_BUNDLE)->find($id);

            if ($this->isGranted(self::ROLE_BUYER, $user)) {
                $this->buyerService->setBuyerStatus($user, $this->getUser(), false, self::MANUALLY);
            }

            //operator cannot see admin informations
            if (!$this->isGranted(self::ROLE_SUPER_ADMIN) && $user->getRoles()[0] == self::ROLE_SUPER_ADMIN) {
                throw $this->createAccessDeniedException($this->translator->trans(self::ACCESS_DENIED_EXCEPTION));
            }
            $thisUser = $this->getUser();
            if ($thisUser instanceof User && $id == $thisUser->getId()){
                throw $this->createAccessDeniedException($this->translator->trans(self::ACCESS_DENIED_EXCEPTION));
            }

            $user->setEnabled(false);
            $user->setDisabledAt(new \DateTime());

            try {
                $this->em->persist($user);
                $this->em->flush();
                $this->addFlash(self::SUCCESS, $this->translator->trans('back.user.form.deactivation.ok', [], self::TRANSLATION_DOMAIN));
            } catch (\Exception $e) {
                $this->addFlash(self::ERROR, $this->translator->trans('back.user.form.deactivation.ko', [], self::TRANSLATION_DOMAIN));
            }

            if ($fromCompany) {
                return $this->redirectToRoute('admin.company.users', ['id' => $user->getCompany()->getId()]);
            } else {
                return $this->redirectToRoute(self::ROUTE_INFO, ['id' => $user->getId()]);
            }
        } else {
            $this->logger->error("user sends invalid values for activating user", EventNameEnum::SECURITY_EVENT, $this->getUsername());
            throw $this->createAccessDeniedException();
        }
    }

    /**
     * @Route("/user/resetPassword", name="admin.user.resetPassword" , methods={"POST"})
     * @Security("is_granted('ROLE_ADMIN') or is_granted('ROLE_OPERATOR')")
     */
    public function resetPasswordUser(Request $request, EntityManagerInterface $em)
    {

        $form = $this->createForm(IdForm::class, null, []);
        $form->handleRequest($request);

        $id = $form->getData()['id'];

        /** @var User $user */
        $user = $em->getRepository(self::USER_BUNDLE)->find($id);

        try {
            if (null === $user->getResetPasswordToken()) {
                $tokenGenerator = $this->get('fos_user.util.token_generator');
                $user->setResetPasswordToken($tokenGenerator->generateToken());
            }
            $this->get('fos_user.mailer')->sendResettingEmailMessage($user);
            $this->get('fos_user.user_manager')->updateUser($user);
            $this->addFlash(self::SUCCESS, $this->translator->trans('back.user.form.resetingPassword.ok', [], self::TRANSLATION_DOMAIN));
        } catch (\Exception $e) {
            $this->addFlash(self::ERROR, $this->translator->trans('back.user.form.resetingPassword.ko', [], self::TRANSLATION_DOMAIN));
        }

        return $this->redirectToRoute(self::ROUTE_INFO, ['id' => $user->getId()]);
    }


    /**
     * @Route("/admin/export/admins", name="admin.admin.export")
     * @Security("is_granted('ROLE_ADMIN') or is_granted('ROLE_OPERATOR')")
     */
    public function exportAdmins(CSVService $cvsService)
    {

        $dateFormat = "d/m/Y H:i:s";
        $filter['page'] = 'adminList';

        $users = $this->userBddService->getAdminsByQualifier($filter);

        /*
         * STEP 1: add custom columns
         */

        /**
         * @var User $user
         */
        foreach ($users as $user) {

            $user->addProperty('id', $user->getId());
            $user->addProperty('email', $user->getEmail());
            $user->addProperty('firstname', $user->getFirstname());
            $user->addProperty('lastname', $user->getLastname());
            $user->addProperty('mainPhoneNumber', $user->getMainPhoneNumber());

            $user->addProperty("translatedType", $this->translator->trans("back.user.role.main." . $user->getRoles()[0], [], self::TRANSLATION_DOMAIN));
            $user->addProperty("translatedRole", $this->translator->trans("back.user.role.secondary." . $user->getRoles()[0], [], self::TRANSLATION_DOMAIN));

            //format creation date
            $user->addProperty("formattedCreationAt", $user->getCreatedAt()->format($dateFormat));

            //format last connexion date
            if ($user->getLastLogin() != null) {
                $user->addProperty("formattedLastConnection", $user->getLastLogin()->format($dateFormat));
            } else {
                $user->addProperty("formattedLastConnection");
            }

            //format disabled at
            if ($user->getDisabledAt() != null) {
                $user->addProperty("formattedDisabledAt", $user->getDisabledAt()->format($dateFormat));
            } else {
                $user->addProperty("formattedDisabledAt");
            }
        }


        /*
         * STEP 2: Now define the content of our export
         */
        $headers = [
            "Id",
            "back.user.list.email",
            "back.user.list.firstname",
            "back.user.list.lastname",
            "back.user.list.role",
            "back.user.list.phone",
            "back.user.list.creation"
        ];

        $properties = [
            "id",
            "email",
            "firstname",
            "lastname",
            "translatedRole",
            "mainPhoneNumber",
            "formattedCreationAt"
        ];


        /*
         * STEP 3: run the export
         */
        $content = $cvsService->objArrayToCSV($users, $headers, $properties);

        return new Response($content, 200, [
            'Content-Encoding' => 'UTF-16',
            'Content-Type' => 'application/CSV; charset=UTF-16',
            'Content-Disposition' => 'attachment; filename="users.csv"']);

    }

    /**
     * @Route("/admin/export/users", name="admin.user.export")
     * @Security("is_granted('ROLE_ADMIN') or is_granted('ROLE_OPERATOR')")
     */
    public function exportUsers(CSVService $cvsService)
    {

        $dateFormat = "d/m/Y H:i:s";
        $filter = [];

        $users = $this->userBddService->getOnlyUsersByQualifier($filter);

        /**
         * @var User $user
         */
        foreach ($users as $user) {
            $user->addProperty('id', $user->getId());
            $user->addProperty('email', $user->getEmail());
            $user->addProperty('firstname', $user->getFirstname());
            $user->addProperty('lastname', $user->getLastname());
            $user->addProperty('mainPhoneNumber', $user->getMainPhoneNumber());

            $user->addProperty("translatedType", $this->translator->trans("back.user.role.main." . $user->getRoles()[0], [], self::TRANSLATION_DOMAIN));
            $user->addProperty("translatedRole", $this->translator->trans("back.user.role.secondary." . $user->getRoles()[0], [], self::TRANSLATION_DOMAIN));

            //format creation date
            $user->addProperty("formattedCreationAt", $user->getCreatedAt()->format($dateFormat));

            //format last connexion date
            if ($user->getLastLogin() != null) {
                $user->addProperty("formattedLastConnection", $user->getLastLogin()->format($dateFormat));
            } else {
                $user->addProperty("formattedLastConnection");
            }

            //format disabled at
            if ($user->getDisabledAt() != null) {
                $user->addProperty("formattedDisabledAt", $user->getDisabledAt()->format($dateFormat));
            } else {
                $user->addProperty("formattedDisabledAt");
            }
        }

        $headers = [
            "Id",
            "back.user.list.email",
            "back.user.list.firstname",
            "back.user.list.lastname",
            "back.user.list.company",
            "back.user.list.role",
            "back.user.list.phone",
            "back.user.list.creation"
        ];

        $properties = [
            "id",
            "email",
            "firstname",
            "lastname",
            "companyName",
            "translatedRole",
            "mainPhoneNumber",
            "formattedCreationAt"
        ];

        $content = $cvsService->objArrayToCSV($users, $headers, $properties);

        return new Response($content, 200, [
            'Content-Encoding' => 'UTF-16',
            'Content-Type' => 'application/CSV; charset=UTF-16',
            'Content-Disposition' => 'attachment; filename="users.csv"']);

    }

    /**
     * @Route("/user/search_delegates/{user_id}", name="admin.user.search.delegates", options={"expose"=true},methods="GET")
     * @Security("is_granted('ROLE_OPERATOR')")
     */
    public function searchDelegate(Request $request, int $user_id, UserBddService $userBddService)
    {
        // is it an Ajax request?
        $isAjax = $request->isXmlHttpRequest();

        if (!$isAjax) {
            return new NotFoundHttpException($this->translator->trans(self::NOT_FOUND_EXCEPTION));
        }


        $term = $request->query->get('term');

        $delegates = $userBddService->findPotentialDelegateByStartToken($term, $user_id);
        $usernames = array_map(function (User $delegate) {
            return ["id" => $delegate->getUsername(), "text" => $delegate->getUsername()];
        }, $delegates);

        $result = ["results" => $usernames];

        return new JsonResponse($result);
    }

    /**
     * @Route("/user/delegate/add", name="admin.user.set.delegate", options={"expose"=true},methods="POST")
     * @Security("is_granted('ROLE_OPERATOR')")
     * @param Request $request
     *
     * @return mixed
     */
    public function setDelegate(Request $request, UserBddService $userBddService, BuyerService $buyerService)
    {
        // is it an Ajax request?
        $isAjax = $request->isXmlHttpRequest();

        if (!$isAjax) {
            return new NotFoundHttpException($this->translator->trans(self::NOT_FOUND_EXCEPTION));
        }

        $igg = $request->request->get('igg');
        $buyer_id = $request->request->get('buyer_id');

        $buyer = $userBddService->findById((int) $buyer_id);
        $delegate = $userBddService->findByUsername((string) $igg);
        $buyerService->attachBuyerToDelegate($buyer, $delegate, $this->getUser(), true);

        $response = ['success' => true, 'error' => false];
        return new JsonResponse($response);
    }

    /**
     * @Route("/user/delegate/delete", name="admin.user.delete.delegate", options={"expose"=true},methods="POST")
     * @Security("is_granted('ROLE_OPERATOR')")
     * @param Request $request
     *
     * @return mixed
     */
    public function deleteDelegate(Request $request, UserBddService $userBddService, BuyerService $buyerService)
    {

        $igg = $request->request->get('igg');
        $buyer_id = $request->request->get('buyer_id');

        $buyer = $userBddService->findById((int) $buyer_id);
        $delegate = $userBddService->findByUsername((string) $igg);

        $user = $this->getUser();
        $buyerService->deleteDelegate($buyer, $delegate, $this->getUser(), true);


        return $this->redirectToRoute('admin.user.list', ['qualifier' => 'all']);
    }

    /**
     * @Route("/user/delegate/delete_ajax", name="admin.user.delete.delegate.ajax", options={"expose"=true},methods="POST")
     * @Security("is_granted('ROLE_OPERATOR')")
     * @param Request $request
     *
     * @return mixed
     */
    public function deleteDelegateAjax(Request $request, UserBddService $userBddService, BuyerService $buyerService)
    {
        // is it an Ajax request?
        $isAjax = $request->isXmlHttpRequest();

        if (!$isAjax) {
            return new NotFoundHttpException($this->translator->trans(self::NOT_FOUND_EXCEPTION));
        }

        $igg = $request->request->get('igg');
        $buyer_id = $request->request->get('buyer_id');

        $buyer = $userBddService->findById((int) $buyer_id);
        $delegate = $userBddService->findByUsername((string) $igg);

        $user = $this->getUser();
        $buyerService->deleteDelegate($buyer, $delegate, $this->getUser(), true);


        $response = ['success' => true, 'error' => false];
        return new JsonResponse($response);
    }

    /**
     * create a fake generated password
     * @return string the generated fake password
     */
    private function createFakePassword()
    {
        // This array must match regexp in AppBundle\Validator\Constraints\SecurePasswordValidator #line 34
        $schars = ['!', '@', '#', '$', '%', '^', '*', '_', '-'];

        $password = substr(md5(uniqid((string) rand(), true)), 0, 15);

        if (strtolower($password) === $password) {
            $password .= chr(rand(65, 90));
        }

        return $password . $schars[array_rand($schars)];
    }

    /**
     * @param User $account
     */
    private function notifyAdminAccountCreation($account)
    {
        $url = $this->generateUrl(
            'user.password.reset',
            [
                'token' => $account->getResetPasswordToken()
            ],
            UrlGeneratorInterface::ABSOLUTE_URL
        );

        if ($account->isEnabled()) {
            $this->mailService->sendEmailMessage(MailService::ADMIN_ACCOUNT_CREATION,
                $account->getLocale(), $account->getEmail(), [

                    'email' => $account->getEmail(),
                    'firstName' => $account->getFirstname(),
                    'lastName' => $account->getLastname(),
                    'link' => $url
                ]
            );
        }
    }

    /**
     * @param User $user
     */
    private function logAccountCreation($user)
    {
        if ($user->getRoles()[0] == self::ROLE_OPERATOR) {
            $key = 'log.operator.created';
        } else if ($user->getRoles()[0] == self::ROLE_SUPER_ADMIN) {
            $key = 'log.admin.created';
        } else {
            $key = 'log.profile.save';
        }

        $this
            ->logger
            ->info(
                $this->translator->trans($key),
                EventNameEnum::PROFILE_SAVE,
                $user->getUsername(),
                [
                    'id' => $user->getId()
                ]
            );
    }
}
