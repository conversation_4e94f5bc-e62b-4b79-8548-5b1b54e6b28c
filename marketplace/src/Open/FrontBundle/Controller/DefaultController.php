<?php

namespace Open\FrontBundle\Controller;

use AppB<PERSON>le\Controller\MkoController;
use AppB<PERSON>le\Entity\Node;
use AppBundle\Entity\NodeContent;
use AppBundle\Entity\User;
use AppBundle\Repository\NodeRepository;
use AppBundle\Services\CartService;
use AppBundle\Services\OrderItemService;
use Doctrine\ORM\EntityManager;
use KnpU\OAuth2ClientBundle\Client\ClientRegistry;
use Open\LogBundle\Service\LogService;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Security;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Contracts\Translation\TranslatorInterface;

class DefaultController extends MkoController
{
    private TranslatorInterface $translator;

    /**
     * DefaultController constructor.
     * @param TranslatorInterface $translator
     */
    public function __construct(TranslatorInterface $translator)
    {
        $this->translator = $translator;
    }

    /**
     * @Route("/connect/buyer", name="connect_buyer_start", options={"i18n"=false})
     */
    public function connectAction(ClientRegistry $clientRegistry, LogService $logger)
    {
        $logger->info("connectAction", "AUTHENT_SSO", null, []);
        return $clientRegistry
            ->getClient('total_buyer')
            ->redirect([], ['approval_prompt' => 'none']);
    }

    /**
     * @Route("/connect/buyer/check", name="connect_buyer_check", options={"i18n"=false})
     */
    public function connectCheckAction(LogService $logger)
    {

        $logger->info("connectCheckAction (should not be called)", "AUTHENT_SSO", null, []);
        // THIS IS HANDLED BY:
        // \AppBundle\Security\TotalBuyerAuthenticator::onAuthenticationFailure
        // \AppBundle\Security\TotalBuyerAuthenticator::onAuthenticationSuccess
        return $this->redirectToRoute('homepage');
    }

    /**
     * @Route("/logout/info", name="buyer.logout.page")
     */
    public function logoutPage(): Response
    {
        return $this->render('@OpenFront/default/logout.html.twig', [
            'full_logout_link' => $this->getParameter('total_open_id_connect_url_logout')
        ]);
    }

    /**
     * @Route("/supplier", name="supplier.landingpage")
     */
    public function supplierLandingPage()
    {
        return $this->render(
            '@OpenFront/default/index.html.twig',
            []
        );
    }

    /**
     * @Route("/", name="supplier.homepage")
     */
    public function supplierHomepageAction()
    {
        /** @var User $user */
        $user = $this->getUser();

        if ($user && $user->hasRole('ROLE_BUYER')) {
            return $this->redirectToRoute('homepage');
        } else {
            return $this->redirectToRoute('front.merchant.registration');
        }


    }

    /**
     * @Route("/home", name="homepage")
     * @Security("is_granted('ROLE_BUYER')")
     */
    public function indexAction(Request $request, OrderItemService $orderItemService, CartService $cartService, TranslatorInterface $translator, NodeRepository $nodeRepository)
    {
        /** @var User $user */
        $user = $this->getUser();

        $offers = $orderItemService->getlastOrderedOffer($user, 4);
        $cart = $cartService->loadCurrentBuyerCart($user);
        $nodes = $nodeRepository->findSlidesHomepage();
        $slides = [];

        foreach ($nodes as $node) {
            $content = $node->getContent($request->getLocale());
            if ($node->getBackgroundImage()) {
                $slides[] = [
                    'image' => $node->getBackgroundImage()->getId(),
                    'title' => $content ? $content->getTitle() : '',
                    'text' => $content ? $content->getBody() : '',
                    'button' => [
                        'name' => $content ? $content->getLinkText() : '',
                        'URL' => $content ? $content->getLink() : '',
                    ],
                ];
            }
        }

        // default slides
        $emptySlides = false;
        if (empty($slides)) {
            $emptySlides = true;
            $slides = array(
                array(
                    'image' => 'slide-1.jpg',
                    'title' => '',
                    'text' => $translator->trans('home.slider.title', [], self::TRANSLATION_DOMAIN),
                    'button' => array('name' => $translator->trans('home.slider.btn', [], self::TRANSLATION_DOMAIN), 'URL' => '#')
                )
            );
        }

        if ($this->getUser()) {
            return $this->render(
                '@OpenFront/default/index-buyer.html.twig',
                [
                    'slides' => $slides,
                    'user' => $user,
                    'cart' => $cart,
                    'isCartCreator' => true,
                    'isUserAuthorizeToEditCart' => true,
                    'isUserValidAssign' => true,
                    'empty_slides' => $emptySlides,
                    'offers' => $offers,
                    'is_homepage' => true,
                    'home_categories' => $this->getParameter('home_category_list' . '_' . $user->getMarketPlace()->getName())
                ]
            );
        }

        return $this->render(
            '@OpenFront/default/index.html.twig',
            [
                'empty_slides' => [],
                'slides' => [],
                'is_homepage' => true
            ]
        );
    }

    /**
     * @Route("/statics/{static}", name="statics")
     * @param $static
     * @return Response
     */
    public function getStaticsAction($static)
    {
        $tpl = '@OpenFront/content:';
        $user = $this->getUser();

        switch ($static) {
            case "cgu":
                $tpl .= "cgu";
                break;
            case "fullwidthproductsfaq":
                $tpl .= "full-width-with-products-and-faq";
                break;
            default:
                $tpl .= "default";
                break;
        }

        return $this->render(
            $tpl . '.html.twig',
            [
                'user' => $user,
            ]
        );
    }

    /**
     * Action that will catch left over from other controllers
     *
     * @param Request $request
     * @param         $slug
     *
     * @return \Symfony\Component\HttpFoundation\RedirectResponse|\Symfony\Component\HttpFoundation\Response
     */
    public function catchAllAction(Request $request, string $slug): Response
    {
        return $this->getNodeContent($request, $slug);
    }

    /**
     * Action that will catch left over from other controllers
     * @Route("/slider/view/{slug}", name="slider.view")
     *
     * @param Request $request
     * @param         $slug
     *
     * @return \Symfony\Component\HttpFoundation\RedirectResponse|\Symfony\Component\HttpFoundation\Response
     */
    public function viewStaticPageAction(Request $request, string $slug): Response
    {
        return $this->getNodeContent($request, $slug);
    }


    private function getNodeContent(Request $request, string $slug): Response
    {

        /** @var EntityManager $em */
        $em = $this->getDoctrine()->getManager();

        /** @var Node $node */
        // Try to find a node that match the url
        $node = $em->getRepository(Node::class)->findBySlug($slug);

        // If no content matching the slug then throw a 404
        if (!$node) {
            throw $this->createNotFoundException($this->translator->trans(self::NOT_FOUND_EXCEPTION));
        }

        /** @var NodeContent $content */
        $content = $node->getContent($request->getLocale());

        // If content not found in requested language thenP throw a 404
        if (!$content) {
            throw $this->createNotFoundException($this->translator->trans(self::NOT_FOUND_EXCEPTION));
        }

        // If Node are not published and user are not operator or super-admin
        if ($node->getStatus() !== Node::STATUS_PUBLISHED && !$this->isGranted('ROLE_OPERATOR') && !$this->isGranted('ROLE_SUPER_ADMIN')) {
            throw $this->createNotFoundException($this->translator->trans(self::NOT_FOUND_EXCEPTION));
        }


        $user = null;

        if ($this->getUser()) {
            $user = $this->getUser();
        }

        switch ($node->getType()) {
            case 'page' :
                return $this->render(
                    '@OpenFront/content/default.html.twig',
                    [
                        'node' => $content,
                        'user' => $user
                    ]
                );
                break;

            default:
                throw $this->createNotFoundException($this->translator->trans(self::NOT_FOUND_EXCEPTION));
                break;
        }
    }
}
