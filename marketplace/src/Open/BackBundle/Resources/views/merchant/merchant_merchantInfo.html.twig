{% extends '@OpenBack/base.html.twig' %}
{% trans_default_domain 'AppBundle' %}
{% block body %}

    <div style="width:100%; padding: 0 0 20px 5px">
        <a href="{{ path('admin.merchant.list',{'mkp_discriminator':merchant.marketplace.totalDiscriminator}) }}" style="display:flex; align-items: center; color: #9600FF">
            <div style="width: 10px; height: 10px; border: solid; border-color: #9600FF; border-width: 0 2px 2px 0;transform: rotate(135deg); margin-right: 5px"></div>
            {{ 'back.back_to_list' | trans }}
        </a>
    </div>

    <div style="margin-bottom: 20px;">

        {# WHEN merchant is pending THEN display "activate", "reject" and "edit" merchant button #}
        {% if merchant.isPending %}

            <a href="{{ path('admin.merchant.activate', { 'id' : merchant.id})}}"
               class="btn btn-info"
               role="button" aria-pressed="true">{{ 'back.user.form.activate' | trans }}</a>

            <a href="{{ path('admin.merchant.reject', { 'id' : merchant.id})}}"
               class="btn btn-danger"
               role="button" aria-pressed="true">{{ 'back.user.form.reject' | trans }}</a>

            <a href="{{ path('admin.merchant.info.edit', { 'id' : merchant.id})}}"
               class="btn btn-info"
               role="button" aria-pressed="true">{{ 'back.merchant.merchantInfo.edit' | trans }}</a>

        {% endif %}
    </div>
    {{ 'back.merchant.merchantInfo.info.title' | trans }}
    <table class="table table-bordered">
        <tr>
            <td>{{ 'back.merchant.merchantInfo.marketplace' | trans }}</td>
            <td>
                {% if merchant.marketplace is not null %}
                    {{ merchant.marketplace.name|trans }}
                {% endif %}
            </td>
        </tr>
        <tr>
            <td>{{ 'back.merchant.merchantInfo.country' | trans }}</td>
            <td>
                {% if merchant.country is not null %}
                    {{ merchant.country.code|trans }}
                {% endif %}
           </td>
        </tr>
        <tr>>
            <td>{{ 'back.merchant.merchantInfo.name' | trans }}</td>
            <td>{{ merchant.name }}</td>
        </tr>
        <tr>>
            <td>{{ 'back.merchant.merchantInfo.firstname' | trans }}</td>
            <td>{{ merchant.firstname }}</td>
        </tr>
        <tr>
            <td>{{ 'back.merchant.merchantInfo.lastname' | trans }}</td>
            <td>{{ merchant.lastname }}</td>
        </tr>
        <tr>
            <td>{{ 'back.merchant.merchantInfo.phoneNumber' | trans }}</td>
            <td>{{ merchant.phoneNumber }}</td>
        </tr>
        <tr>
            <td>{{ 'back.merchant.merchantInfo.currency.title' | trans }}</td>
            <td>{{ merchant.currency }}</td>
        </tr>
        <tr>
            <td>{{ 'back.merchant.merchantInfo.language' | trans }}</td>
            <td>{{ merchant.language }}</td>
        </tr>
        <tr>
            <td>{{ 'back.merchant.merchantInfo.identification' | trans }}</td>
            <td>{{ merchant.identification }}</td>
        </tr>
        <tr>
            <td>{{ 'back.merchant.merchantInfo.email' | trans }}</td>
            <td>{{ merchant.email }}</td>
        </tr>
        <tr>
            <td>{{ 'back.merchant.merchantInfo.source' | trans }}</td>
            <td>{{ merchant.source }}</td>
        </tr>
        <tr>
            <td>{{ 'back.merchant.merchantInfo.buyer_igg' | trans }}</td>
            <td>{{ merchant.buyerIgg }}</td>
        </tr>
        <tr>
            <td>{{ 'back.merchant.merchantInfo.buyer_firstname' | trans }}</td>
            <td>{{ merchant.buyerFirstname }}</td>
        </tr>
        <tr>
            <td>{{ 'back.merchant.merchantInfo.buyer_lastname' | trans }}</td>
            <td>{{ merchant.buyerLastname }}</td>
        </tr>
        <tr>
            <td>{{ 'back.merchant.merchantInfo.activities' | trans }}</td>
            <td>
                {% for category in merchant.categories|split(',') %}
                    <p>{{ category }}</p>
                {% endfor %}
            </td>
        </tr>
        <tr>
            <td>{{ 'back.merchant.merchantInfo.status' | trans }}</td>
            <td>{{ merchant.status }}</td>
        </tr>
        {% if merchant.actionBy is defined and merchant.actionBy is not empty  %}
            <tr>
                <td>{{ 'back.merchant.merchantInfo.lastModifiedBy' | trans }}</td>
                <td>{{ merchant.actionBy.email }}</td>
            </tr>
            <tr>
                <td>{{ 'back.merchant.merchantInfo.lastModifiedAt' | trans }}</td>
                <td>{{ merchant.updatedAt|date }}</td>
            </tr>
        {% endif %}

        {% if merchant.rejectedReason is not null %}
            <tr>
                <td>{{ 'back.merchant.merchantInfo.rejectedReason' | trans }}</td>
                <td>{{ merchant.rejectedReason }}</td>
            </tr>
        {% endif %}
    </table>
{% endblock %}
