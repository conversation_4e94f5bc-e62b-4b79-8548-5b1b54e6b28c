{% extends '@OpenFront/base.html.twig' %}
{% trans_default_domain 'AppBundle' %}

{% block title %}
    {{ 'orders.detail.title' | trans }}
{% endblock %}

{% block body %}
    {% block searchbar %}
        {% if formSearchBar is defined %}
            {% include('@OpenFront/component/search-bar.html.twig') %}
        {% else %}
            {{ render(controller("Open\\FrontBundle\\Controller\\SearchBarController::formAction")) }}
        {% endif %}
    {% endblock %}

    <div class="Page-inner">
        <div class="Page-content">
            <div style="text-align:center">
                <section class="account-header account-title">{{ 'orders.detail.title' | trans }}</section>
            </div>

            <ul class="tabs__items desktop-only">
                {% for tab in tabs %}
                    <li class="tab__item no-js">
                        <a class="{% if tab.isActive %}active{% endif %}" href="{{ path('front.orders.list.status', {'status': tab.status }) }}">
                            {{ tab.label | trans | upper }}
                        </a>
                    </li>
                {% endfor %}
            </ul>

            <div class="tabs__content desktop-only">
                <div class="tab__content active">
                    {% block orderlist %}
                    {% endblock %}
                </div>
            </div>

            <div class="orders-types-list mobile-only ">
                {% for tab in tabs %}
                    <a href="{{ path('front.orders.list.status', {'status': tab.status, 'isMobile': true }) }}">
                        <section class="orders-type">
                            {{ tab.label | trans | upper }}
                            <img alt="open" src="{{ asset('images/arrow-next.svg') }}">
                        </section>
                    </a>
                {% endfor %}
            </div>
        </div>

    </div>
{% endblock %}
{% block javascripts %}
    <script>
        $(document).ready(function() {

            function reorder ($url){

                $.getJSON(
                    $url,
                    null,
                    function(jsonData) {
                        window.location.href = "{{ path('cart.details') }}";
                    }
                ).fail(function (error) {
                    if (error.status === 400) {
                        window.UI.Modal.alert("{{ 'orders.errors.reorder' | trans | escape('js') }}");
                    } else {
                        window.location.reload();
                    }
                });
            }

            $('[data-reorder]').on('click', function(e) {
                e.stopPropagation();

                window.UI.Modal.showLoading();
                $orderUrl =  $(this).data('reorder');
                $.getJSON(
                    $(this).data('checkreorder'),
                    null,
                    function(jsonData) {
                        console.log(jsonData["status"]);
                        if(jsonData["status"]==="ALL"){
                            reorder($orderUrl);
                        }
                        if(jsonData["status"]==="NONE"){
                            window.UI.Modal.alert("{{ 'orders.reorder.unavailable' | trans | escape('js') }}");
                        }
                        if(jsonData["status"]==="SOME") {
                            removeModal = window.UI.Modal.confirm("{{ 'orders.reorder.partial'|trans({}, 'AppBundle')|raw }}", "",
                                function () {
                                    window.UI.Modal.showLoading();
                                    reorder($orderUrl);
                                }, function () {
                                    this.removeModal.close();
                                });
                        }
                    }
                ).fail(function (error) {
                    if (error.status === 400) {
                        window.UI.Modal.alert("{{ 'orders.errors.reorder' | trans | escape('js') }}");
                    } else {
                        window.location.reload();
                    }
                });

            });
        });
    </script>
{% endblock %}
