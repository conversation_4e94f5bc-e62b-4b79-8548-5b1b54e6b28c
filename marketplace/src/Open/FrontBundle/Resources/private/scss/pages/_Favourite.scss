@import "../variables";

body[class*="Page--favori_list"] {
  #favouritesListIntro {
    font-size: 16px;
  }

  .wishlist-table {
    th, td {
      border-right: none;
      vertical-align: middle;
    }

    tbody tr td {
      border-bottom: 1px solid #dee2e6;
    }

    a {
      color: $black;
    }
  }

  .dataTables_wrapper div.dataTables_paginate {
    margin: auto;
  }

  .dataTables_wrapper .dataTables_paginate .paginate_button a {
    height: 24px;
    width: 24px;
  }

  .dataTables_wrapper .dataTables_paginate .paginate_button.next a {
    border: 1px solid $TOTAL_RED !important;
    border-radius: 0;
  }

  .dataTables_wrapper .dataTables_paginate .paginate_button.previous a {
    border: 1px solid $TOTAL_RED !important;
    border-radius: 0;
  }

  .dataTables_wrapper .dataTables_paginate .paginate_button:hover {
    background: none;
    border: none;
  }

  .page-item.active .page-link {
    background-color: $TOTAL_RED !important;
    border-color: $TOTAL_RED !important;
    font-weight: bold;
    z-index: 1;
  }

  .page-link {
    border: none;
    border-top-color: currentcolor;
    border-right-color: currentcolor;
    border-bottom-color: currentcolor;
    border-left-color: currentcolor;
    font-size: 1rem;
    color: #353535;
    padding: 0;
    margin-left: 0;
  }

  .page-link:hover {
    color: #353535;
    font-weight: bold;
    border: none;
    border-top-color: currentcolor;
    border-right-color: currentcolor;
    border-bottom-color: currentcolor;
    border-left-color: currentcolor;
    background: none;
    background-color: rgba(0, 0, 0, 0);
  }

  .pagination {
    height: 30px;
  }

  .wishlist-table.dataTable thead th {
    border-bottom: 1px solid #dee2e6 !important;
    border-top: 1px solid #dee2e6 !important;
  }

  table.dataTable.no-footer {
    border-bottom: 1px solid #dee2e6;
  }
}
