<?php

namespace Open\IdealBundle\Model;

class Manager
{
    private ?string $Id = null;
    private ?string $MainEmployeeId = null;
    private ?ManagerMainRecord  $MainRecord = null;

    /**
     * @return string|null
     */
    public function getId(): ?string
    {
        return $this->Id;
    }

    /**
     * @param string|null $Id
     * @return Manager
     */
    public function setId(?string $Id): Manager
    {
        $this->Id = $Id;
        return $this;
    }

    /**
     * @return ManagerMainRecord|null
     */
    public function getMainRecord(): ?ManagerMainRecord
    {
        return $this->MainRecord;
    }

    /**
     * @param ManagerMainRecord|null $MainRecord
     * @return Manager
     */
    public function setMainRecord(?ManagerMainRecord $MainRecord): Manager
    {
        $this->MainRecord = $MainRecord;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getMainEmployeeId(): ?string
    {
        return $this->MainEmployeeId;
    }

    /**
     * @param string|null $MainEmployeeId
     * @return Manager
     */
    public function setMainEmployeeId(?string $MainEmployeeId): Manager
    {
        $this->MainEmployeeId = $MainEmployeeId;
        return $this;
    }





}