<?php

namespace Open\IzbergBundle\Model;

use Symfony\Component\Validator\Constraints as Assert;
use <PERSON><PERSON>\Serializer\Annotation\Type;
use <PERSON><PERSON>\Serializer\Annotation as JSM;

/**
 *
 * @JSM\ExclusionPolicy("none")
 */
class CartShippingOptionChoice
{
    /**
     * @Type("integer")
     * @Assert\NotBlank()
     */
    private $id;

    /**
     * @Type("double")
     * @Assert\NotBlank()
     */
    private $amountWithoutVat;

    /**
     * @Type("string")
     */
    private $currency;

    /**
     * @Type("integer")
     */
    private $deliveryWithinHours;

    /**
     * @Type("integer")
     */
    private $collectionWithinHours;

    public function getId()
    {
        return $this->id;
    }

    public function setId($id)
    {
        $this->id = $id;
        return $this;
    }

    public function getAmountWithoutVat()
    {
        return $this->amountWithoutVat;
    }

    public function setAmountWithoutVat($amountWithoutVat)
    {
        $this->amountWithoutVat = $amountWithoutVat;
        return $this;
    }

    public function getCurrency()
    {
        return $this->currency;
    }

    public function setCurrency($currency)
    {
        $this->currency = $currency;
        return $this;
    }

    public function getDeliveryWithinHours()
    {
        return $this->deliveryWithinHours;
    }

    public function setDeliveryWithinHours($deliveryWithinHours)
    {
        $this->deliveryWithinHours = $deliveryWithinHours;
        return $this;
    }

    public function getCollectionWithinHours()
    {
        return $this->collectionWithinHours;
    }

    public function setCollectionWithinHours($collectionWithinHours)
    {
        $this->collectionWithinHours = $collectionWithinHours;
        return $this;
    }
}
