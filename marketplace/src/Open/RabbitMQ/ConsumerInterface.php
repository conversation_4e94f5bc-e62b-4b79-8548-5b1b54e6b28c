<?php

namespace Open\RabbitMQ;

/**
 * Class Consumer
 * A Consumer is in charge to read the queue and index the new messages
 * @package Open\Queue\Consumer
 */

interface ConsumerInterface
{

    /**
     * callback method. Called each time a new message is available in the queue
     * @param mixed $msg not typed, because depends on the Consumer implementation
     */
    public function callback($msg): void;

    /**
     * @param mixed $msg mixed $msg not typed, because depends on the Consumer implementation
     * use to send ack to the queue for this message
     */
    public function ackMessage ($msg): void;


    /**
     * run the consumer
     */
    public function run(): void;

}
