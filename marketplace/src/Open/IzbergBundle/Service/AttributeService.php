<?php

namespace Open\IzbergBundle\Service;

use AppBundle\Services\IzbergCustomAttributes;
use Open\IzbergBundle\Api;
use Open\IzbergBundle\Api\AsyncAttributeApi;
use Open\IzbergBundle\ApiRequestParameter;
use Open\IzbergBundle\Dto\AttributeDTO;
use Open\LogBundle\Service\LogService;
use Open\LogBundle\Utils\EventNameEnum;

class AttributeService
{
    private const DEFAULT_LOCALE = "fr";
    private const CACHE_KEYS = "IZBERG_ATTRIBUTES";
    private const CACHE_ATTRIBUTE_ID = "IZBERG_ATTRIBUTE_ID";

    /**
     * @var array $supportedLocales list of supported locales
     */
    private $supportedLocales;

    /**
     * @var RedisService $cacheService the cache service
     */
    private $cacheService;

    /**
     * @var AsyncAttributeApi $attributeApi
     */
    private $attributeApi;

    /**
     * @var LogService $logger
     */
    private $logger;

    public function __construct(
        AsyncAttributeApi $attributeApi,
        RedisService $cacheService,
        array $supportedLocales,
        LogService $logger
    ) {
        $this->attributeApi = $attributeApi;
        $this->cacheService = $cacheService;
        $this->supportedLocales = $supportedLocales;
        $this->logger = $logger;
    }


    /**
     * @param string $locale the locale to use to get the attribute
     * @param string $attributeName the name of the attribute to get
     * @return AttributeDTO the attribute
     */
    public function getAttribute ($attributeName, $locale): AttributeDTO
    {
        $attributes = $this->getCachedAttributes();

        if (array_key_exists($locale, $attributes) && array_key_exists($attributeName, $attributes[$locale]) && $attributes[$locale][$attributeName]->getLabel() !== null){
            return $attributes[$locale][$attributeName];
        }
        //if not found, check for default english version
        if ($locale !== self::DEFAULT_LOCALE){
            return $this->getAttribute($attributeName, self::DEFAULT_LOCALE);
        }

        //if still no return here, we build a default attribute
        $attribute = new AttributeDTO();
        $attribute->setKey($attributeName);
        $attribute->setLabel($attributeName);
        return $attribute;
    }

    /**
     * @param string $locale the locale to use the attribute
     * @return array
     */
    public function getAttributesForLocale($locale){
        return $this->getCachedAttributes()[$locale];
    }


    /**
     * get the izberg identifier of the reconciliation attribute
     */
    public function getReconciliationAttributeId(){
        return $this->getOrderAttributeId('reconciliation_key');
    }

    public function getOrderAttributeId($key, bool $force = false)
    {
        return $this->getAttributeId($key, 'getOrderAttributeId', $force);
    }

    public function getMerchantAttributeId(string $key, bool $force = false)
    {
        return $this->getAttributeId($key, 'getMerchantAttributeId', $force);
    }

    public function getCustomerAttributeId($key, bool $force = false)
    {
        return $this->getAttributeId($key, 'getCustomerAttributeId', $force);
    }

    private function getAttributeId(string $key, string $attributeApiMethod, bool $force = false)
    {
        if (!method_exists($this->attributeApi, $attributeApiMethod)) {
            throw new \InvalidArgumentException(
                sprintf(
                    '%s method does not exists in %s class',
                    $attributeApiMethod,
                    get_class($this->attributeApi)
                )
            );
        }
        $pf_id = $this->attributeApi->getApplicationId();
        $cacheKey = sprintf('%s_%s_%s', self::CACHE_ATTRIBUTE_ID, $key, $pf_id);
        $identifier = $this->cacheService->getItem($cacheKey);
        if (empty($identifier) || $force){
            $identifier = $this->attributeApi->$attributeApiMethod($key);
            if (!$this->saveInCache($cacheKey, $identifier)) {
                $this->logger->error(
                    "error getting attribute ID from Izberg",
                    EventNameEnum::IZBERG_ATTRIBUTE_NOT_FOUND,
                    null,
                    [
                        'attribute_key' => $key,
                    ]
                );
            }
        }

        return $identifier;
    }

    /**
     * update the reconciliation key of a merchant order
     * @param integer $attributeId the identifier of the attribute
     * @param integer $merchantOrderId the identifier of the merchant order
     * @param string $value the value to set for the attribute
     */
    public function updateReconciliationAttribute($attributeId, $merchantOrderId, $value){
        $this->attributeApi->updateReconciliationAttribute($attributeId, $merchantOrderId, $value);
    }

    public function updateMerchantOrderExtraInfo($merchantOrderId, array $orderExtraInfo)
    {
        $defaultOrderExtraInfo = [
            'ZSA-Contact-Info' => '',
            'ZTA-Contact-First-Name' => '',
            'ZUA-Contact-Last-Name' => '',
            'ZVA-Contact-Email' => '',
            'ZWA-Contact-Main-Phone' => '',
            'ZXA-Contact-Phone' => '',
            'ZYA-Job-Title' => '',
            'ZZA-Contact-Comment' => '',
        ];

        $orderExtraInfo = $orderExtraInfo + $defaultOrderExtraInfo;

        $attributes = [];
        foreach($orderExtraInfo as $key => $value) {
            $attributes[$this->getOrderAttributeId($key)] = $value;
        }

        $this->attributeApi->updateMerchantOrderAttributes($merchantOrderId, $attributes);
    }

    /**
     * @param array $merchantOrderIds
     * @param array $commonAttributes
     * @param array $specificAttributes Per merchant order attributes the key is the ID of the merchant order
     *                                  and the value will be an array ofspecific attributes
     */
    public function setMerchantsOrdersExtraInfo(array $merchantOrderIds, array $commonAttributes, array $specificAttributes = [])
    {
        $defaultAttributes = [];

        $commonAttributes = $commonAttributes + $defaultAttributes;

        $attributesNameToId = function($attributes) {
            return array_combine(
                array_map(
                    function($key) {
                        return $this->getOrderAttributeId($key);
                    },
                    array_keys($attributes)
                ),
                array_values($attributes)
            );
        };

        $commonAttributes = call_user_func_array($attributesNameToId, [$commonAttributes]);

        $data = [];
        foreach($merchantOrderIds as $merchantOrderId) {
            $merchantOrderSpecificAttributes = $specificAttributes[$merchantOrderId] ?? [];
            $merchantOrderSpecificAttributes = call_user_func_array($attributesNameToId, [$merchantOrderSpecificAttributes]);

            $attributes = $merchantOrderSpecificAttributes + $commonAttributes;

            foreach($attributes as $attributeId => $attributeValue) {
                if (!empty($attributeValue)) {
                    $data[] = [$merchantOrderId, $attributeId, $attributeValue];
                }
            }
        }

        $this->attributeApi->sendConcurrentApiRequest(
            array_map(
                function(array $datum) {
                    [$merchantOrderId, $attributeId, $attributeValue] = $datum;

                    return new ApiRequestParameter(
                        Api::HTTP_POST_OPERATION,
                        'order_attribute_value/',
                        [
                            'attribute' => '/v1/order_attribute/'.strval($attributeId).'/',
                            'application' => '/v1/application/'.$this->attributeApi->getApplicationId().'/',
                            'value' => $attributeValue,
                            'entity' => '/v1/merchant_order/'.strval($merchantOrderId).'/'
                        ]
                    );
                },
                $data
            ),
            true,
            5,
            [
                Api\AttributeApi::OPTION_NO_DATA_RETURN,
            ]
        );
    }

    /**
     * update merchant order attribute
     * @param $merchantOrderId
     * @param $attributes
     */
    public function updateMerchantOrderAttributes($merchantOrderId, $attributes){
        $attrToUpdate = [];
        foreach($attributes as $key => $value) {
            $attrToUpdate[$this->getOrderAttributeId($key)] = $value;
        }
        $this->attributeApi->updateMerchantOrderAttributes($merchantOrderId, $attrToUpdate);
    }

    public function updateCustomerAttributes($userId, array $customerAttributes){
        $defaultCustomerAttributes = [
            'customer_vat_number' => '',
        ];

        $customerAttributes = $customerAttributes + $defaultCustomerAttributes;

        foreach($customerAttributes as $key => $value) {
            $this->attributeApi->updateCustomerAttribute($this->getCustomerAttributeId($key), $userId, $value);
        }
    }

    /**
     * get all the attributes from the cache
     * @return array an array that contains the attributes in each languages [locale => [attributeKey => attributeValue]
     */
    public function getCachedAttributes(bool $force = false){
        $attributes = $this->cacheService->getItem(self::CACHE_KEYS);
        if ($attributes === null || $force) {
            $attributes = [];
            foreach ($this->supportedLocales as $locale){
                $attributes[$locale] = $this->attributeApi->getAttributes($locale);
            }

            //cache attributes for 24 hours
            $this->saveInCache(self::CACHE_KEYS, $attributes);
        }
        return $attributes;
    }

    public function getAttributesKeys()
    {
        $cachedAttributes = $this->getCachedAttributes();
        $attributeKeys = array_map(
            function(AttributeDTO $attributeDTO) {
                return $attributeDTO->getKey();
            },
            $cachedAttributes[self::DEFAULT_LOCALE] ?? []
        );

        return $attributeKeys;
    }

    public function fetchTechnicalAttributes(string $locale): array
    {
        $attributes = $this->getCachedAttributes();

        if (!isset($attributes[$locale])) {
            return [];
        }

        $technicalAttributes = array_filter(array_keys($attributes[$locale]), function($attributeName) {
            return strpos($attributeName, 'D') === 0;
        });

        sort($technicalAttributes, SORT_STRING);

        return array_map(function($attributeName) {
            return IzbergCustomAttributes::createFullAttributeName($attributeName);
        }, $technicalAttributes);
    }

    private function saveInCache($cacheKey, $value, int $expiration = null): bool
    {
        // invalidate Item if exists
        $this->cacheService->removeItem($cacheKey);

        if (is_array($value) && !count($value)) {
            return false;
        }

        if (!$value) {
            return false;
        }

        $this->cacheService->saveItem($cacheKey, $value, $expiration);
        return true;
    }
}
