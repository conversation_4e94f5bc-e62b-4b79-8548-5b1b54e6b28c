{% extends '@OpenFront/invitation/invitations_list_base.html.twig' %}
{% trans_default_domain 'AppBundle' %}

{% block invitationlist %}
    <div class="text-center">
        <p style="text-align: center"> {{ 'invitation.seller.check' | trans }}</p>
        <br>
        <a href="{{ path('front.supplier.invite') }}" class="btn btn-danger btn-invite-supplier text-uppercase">{{ 'footer.invite_suppplier'|trans }}</a><br><br><br>

        {% set desktopStepper = '/images/buyer-invit-steps-fr.svg' %}
        {% set mobileStepper = '/images/buyer-invit-mobile-steps-fr.svg' %}

        {% if (app.request.locale | lower) in ['de', 'nl'] %}
            {% set desktopStepper = '/images/buyer-invit-steps-' ~ (app.request.locale | lower) ~ '.svg' %}
            {% set mobileStepper = '/images/buyer-invit-mobile-steps-' ~ (app.request.locale | lower) ~ '.svg' %}
        {% endif %}

        <img src="{{ asset(desktopStepper) }}" class="desktop-only" alt="stepper"/>
        <img src="{{ asset(mobileStepper) }}" width="300px" height="200px" class="mobile-only" alt="stepper"/><br><br>
    </div>
    <div class="invitation-container">
        <div class="toogle-title" data-toggle="collapse" data-target="#hide-buyer-invit">
            <section class="subtitle">{{ 'invitation.list.my_invitation.title' | trans |upper  }}</section>
            <div class="open-tab-icon" style="background: url('{{ asset('images/open-tab-icon.png') }}') no-repeat;" data-toggle="collapse" data-target="#version-quote"></div>
        </div>
        <div id="hide-buyer-invit" class="collapse show">
            {% if buyer_invitations|length > 0 %}
            <table id="my-invitations" class="table invitations-table desktop-only">
                <thead>
                <tr>
                    <th class="cell w-300">{{ 'invitation.list.block.supplier' | trans }}</th>
                    <th class="cell w-400">{{ 'invitation.list.block.email' | trans }}</th>
                    <th>{{ 'invitation.list.block.date_title' | trans }}</th>
                    <th class="cell w-300">{{ 'invitation.list.block.status' | trans }}</th>
                    <th></th>
                </tr>
                </thead>
                <tbody>
                {# @var merchant \AppBundle\Entity\Merchant#}
                {% for merchant in buyer_invitations %}
                    <tr>
                        <td>{{ merchant.name }}</td>
                        <td>{{ merchant.email }}</td>
                        <td>{{ merchant.createdDate | localizeddate('short', 'none', locale)}}</td>
                        <td>{{ ('invitation.status.' ~ merchant.status) | lower | trans }}</td>
                        <td class="text-right">
                            {# button relance#}
                            <div data-toggle="modal" data-target="#resendModal" data-resendid="{{ merchant.id }}" class="tooltip-total corner-button" style="white-space: nowrap">
                                <img src="{{ asset('images/bt-invit-relance.svg') }}">
                                <div class="tooltiptext">
                                    {{ 'invitation.list.resend.link.tooltip' | trans }}
                                </div>
                                <span>{{ 'invitation.list.resend.link.label' | trans | upper}}</span>
                            </div>
                        </td>
                    </tr>
                {% endfor%}
                </tbody>
            </table>
            <div class="mobile-only">
                {% for merchant in buyer_invitations %}
                    <div class="item-invit">
                        <div><b>{{ 'invitation.list.block.supplier' | trans }} : </b>{{ merchant.name }}</div>
                        <div><b>{{ 'invitation.list.block.email' | trans }} : </b>{{ merchant.email }}</div>
                        <div><b>{{ 'invitation.list.block.date_title' | trans }} : </b>{{ merchant.registrationDate | localizeddate('short', 'none', locale)}}</div>
                        <div><b>{{ 'invitation.list.block.status' | trans }} : </b>{{ ('invitation.status.' ~ merchant.status) | lower | trans }}</div>
                        <div data-toggle="modal" data-target="#resendModal" data-resendid="{{ merchant.id }}" class="tooltip-total corner-button" style="white-space: nowrap">
                            <img src="{{ asset('images/bt-invit-relance.svg') }}">
                            <div class="tooltiptext">
                                {{ 'invitation.list.resend.link.tooltip' | trans }}
                            </div>
                            <span>{{ 'invitation.list.resend.link.label' | trans | upper}}</span>
                        </div>
                    </div>
                {% endfor%}
            </div>
            {% else %}
                <div class="invitations-list-empty">
                    <h6 style="text-align: center">{{ 'invitation.list.empty' | trans }}</h6>
                </div>
            {% endif %}
        </div>
        <div class="toogle-title" data-toggle="collapse" data-target="#hide-all-invit">
            <section class="subtitle">{{ 'invitation.list.all_invitation.title' | trans | upper }}</section>
            <div class="open-tab-icon" style="background: url('{{ asset('images/open-tab-icon.png') }}') no-repeat;" data-toggle="collapse" data-target="#version-quote"></div>
        </div>
        <div id="hide-all-invit" class="collapse show">
            <div>
                <table id="all-invitations" class="table invitations-table all-invitations-table display responsive nowrap">
                    <thead>
                        <tr>
                            <th class="cell w-300">{{ 'invitation.list.block.supplier' | trans }}</th>
                            <th class="cell w-400">{{ 'invitation.list.block.email' | trans }}</th>
                            <th>{{ 'invitation.list.block.date_title' | trans }}</th>
                            <th class="cell w-300">{{ 'invitation.list.block.status' | trans }}</th>
                            <th>{{ 'invitation.list.block.src' | trans }}</th>
                        </tr>
                    </thead>
                    <tbody>
                    </tbody>
                </table>
                <div id="all-empty" class="invitations-list-empty close">
                    <h6 style="text-align: center">{{ 'invitation.list.empty' | trans }}</h6>
                </div>
            </div>
        </div>
    </div>
    <div class="modal Modal Modal--alert" data-backdrop="static" id="resendModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
        <div class="modal-dialog Modal-wrapper" role="document" style="pointer-events: inherit">
            <div class="Modal-content" style="width: 100%;pointer-events: inherit">
                <div class="Modal-header">
                    <button type="button" class="close js-cancel-button d-block" data-dismiss="modal" aria-label="Close">
                        <span>×</span>
                    </button>
                    <h5 class="Modal-title" id="exampleModalLabel">{{ 'invitation.list.action.title'|trans }}</h5>
                </div>
                <div class="Modal-body">
                    <input type="hidden" id="invitId" name="id">
                    <label for="resend-message" style="position: relative">{{ 'invitation.list.action.message'|trans|upper }}</label>
                    <textarea class="form-control" id="resend-message" name="message" rows="7" style="width: 100%;height: 180px"></textarea>
                </div>
                <div class="Modal-footer">
                    <button type="button" class="btn btn-danger text-uppercase" id="resend-send-btn">{{ 'invitation.list.action.button'|trans|upper }}</button>
                </div>
            </div>
        </div>
    </div>
{% endblock %}
