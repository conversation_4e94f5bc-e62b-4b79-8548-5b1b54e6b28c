<?php
/**
 * Created by PhpStorm.
 * User: AQU04740
 * Date: 13/02/2020
 * Time: 11:23.
 */

namespace AppBundle\Model\Quote;

class QuoteData
{
    private $quoteId;
    private $supplier;
    private $creationDate;
    private $cancelDate;
    private $validationDate;
    private $subject;
    private $quoteNumber;
    private $status;
    /**
     * @var bool
     */
    private $unread;

    public function getQuoteId()
    {
        return $this->quoteId;
    }

    public function setQuoteId($quoteId): self
    {
        $this->quoteId = $quoteId;

        return $this;
    }

    public function getSupplier()
    {
        return $this->supplier;
    }

    public function setSupplier($supplier): self
    {
        $this->supplier = $supplier;

        return $this;
    }

    public function getCreationDate()
    {
        return $this->creationDate;
    }

    public function setCreationDate($creationDate): self
    {
        $this->creationDate = $creationDate;

        return $this;
    }

    public function getValidationDate()
    {
        return $this->validationDate;
    }

    public function setValidationDate($validationDate): self
    {
        $this->validationDate = $validationDate;

        return $this;
    }

    public function getSubject()
    {
        return $this->subject;
    }

    public function setSubject($subject): self
    {
        $this->subject = $subject;

        return $this;
    }

    public function getQuoteNumber()
    {
        return $this->quoteNumber;
    }

    public function setQuoteNumber($quoteNumber): self
    {
        $this->quoteNumber = $quoteNumber;

        return $this;
    }

    public function getCancelDate()
    {
        return $this->cancelDate;
    }

    public function setCancelDate($cancelDate): self
    {
        $this->cancelDate = $cancelDate;

        return $this;
    }

    public function getStatus()
    {
        return $this->status;
    }

    public function setStatus($status)
    {
        $this->status = $status;

        return $this;
    }

    public function isUnread(): bool
    {
        return $this->unread;
    }

    public function setUnread(bool $unread): self
    {
        $this->unread = $unread;

        return $this;
    }
}
