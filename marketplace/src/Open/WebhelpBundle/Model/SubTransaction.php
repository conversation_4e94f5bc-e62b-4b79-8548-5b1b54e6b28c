<?php
/**
 * Created by PhpStorm.
 * User: QAR14123
 * Date: 29/06/2018
 * Time: 11:36
 */

namespace Open\WebhelpBundle\Model;

use <PERSON><PERSON>\Serializer\Annotation\Type;

use <PERSON><PERSON>\Serializer\Annotation as JSM;

/**
 * Class SubTransaction
 * @package Open\WebhelpBundle\Model
 * @JSM\ExclusionPolicy("none")
 */
class SubTransaction
{

    /**
     * @Type("double")
     */
    private $amountReconciled;

    /**
     * @Type("string")
     */
    private $codeSubTransactionWps;

    /**
     * @Type("string")
     */
    private $creationDate;

    /**
     * @Type("string")
     */
    private $reason;

    /**
     * @Type("string")
     */
    private $reconciliationDate;

    /**
     * @Type("string")
     */
    private $status;

    /**
     * @Type("string")
     */
    private $statusTransaction;


    /**
     * @return mixed
     */
    public function getAmountReconciled()
    {
        return $this->amountReconciled;
    }

    /**
     * @param mixed $amountReconciled
     */
    public function setAmountReconciled($amountReconciled)
    {
        $this->amountReconciled = $amountReconciled;
    }

    /**
     * @return mixed
     */
    public function getCodeSubTransactionWps()
    {
        return $this->codeSubTransactionWps;
    }

    /**
     * @param mixed $codeSubTransactionWps
     */
    public function setCodeSubTransactionWps($codeSubTransactionWps)
    {
        $this->codeSubTransactionWps = $codeSubTransactionWps;
    }

    /**
     * @return mixed
     */
    public function getCreationDate()
    {
        return $this->creationDate;
    }

    /**
     * @param mixed $creationDate
     */
    public function setCreationDate($creationDate)
    {
        $this->creationDate = $creationDate;
    }

    /**
     * @return mixed
     */
    public function getReason()
    {
        return $this->reason;
    }

    /**
     * @param mixed $reason
     */
    public function setReason($reason)
    {
        $this->reason = $reason;
    }

    /**
     * @return mixed
     */
    public function getReconciliationDate()
    {
        return $this->reconciliationDate;
    }

    /**
     * @param mixed $reconciliationDate
     */
    public function setReconciliationDate($reconciliationDate)
    {
        $this->reconciliationDate = $reconciliationDate;
    }

    /**
     * @return mixed
     */
    public function getStatus()
    {
        return $this->status;
    }

    /**
     * @param mixed $status
     */
    public function setStatus($status)
    {
        $this->status = $status;
    }

    /**
     * @return mixed
     */
    public function getStatusTransaction()
    {
        return $this->statusTransaction;
    }

    /**
     * @param mixed $statusTransaction
     */
    public function setStatusTransaction($statusTransaction): void
    {
        $this->statusTransaction = $statusTransaction;
    }



}