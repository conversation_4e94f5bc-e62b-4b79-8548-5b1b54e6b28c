<?php

namespace Open\LogBundle\Service;

class LogService
{
    private const CHANNEL_DEFAULT = 'analysed_logs';
    private const CHANNEL_CONSOLE = 'cron_analysed_logs';
    private Logger $logger;
    private string $loggerChannel;
    public const EVENT_CART_VALIDATION = 'EVENT_CART_VALIDATION';

    public function __construct(Logger $logger)
    {
        $this->logger = $logger;
        $this->loggerChannel = self::CHANNEL_DEFAULT;
    }

    public function setLogger(Logger $logger)
    {
        $this->logger = $logger;
    }

    public function useConsoleChannel()
    {
        $this->loggerChannel = self::CHANNEL_CONSOLE;
    }

    public function isConsoleChannelEnable(): bool
    {
        return ($this->loggerChannel === self::CHANNEL_CONSOLE);
    }

    public function info ($message, $eventName, $userName = null, $data = [])
    {
        $this->logger->info($message, $eventName, $userName, $data);
    }

    public function error($message, $eventName, $userName = null, $data = [])
    {
        $this->logger->error($message, $eventName, $userName, $data);
    }

    public function critical($message, $eventName, $userName = null, $data = [])
    {
        $this->logger->critical($message, $eventName, $userName, $data);
    }

    public function debug ($message, $eventName, $userName = null, $data = [])
    {
        $this->logger->debug($message, $eventName, $userName, $data);
    }
}
