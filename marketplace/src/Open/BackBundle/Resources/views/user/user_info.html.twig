{% extends '@OpenBack/base.html.twig' %}
{% trans_default_domain "AppBundle" %}
{% block stylesheets %}
    <link href="{{ asset('libs/select2/select2.min.css') }}" rel="stylesheet">
{% endblock %}
{% block body %}
{% include '@OpenBack/user/update-user-roles-popup.html.twig' with {user: user, form: updateUserRolesForm} %}
{% include '@OpenBack/user/update-user-marketplaces-popup.html.twig' with {user: user, form: updateUserMarketplacesForm} %}

    <div style="width:100%; padding: 0 0 20px 5px">
        <a href="{% if isAdmin %}{{ path('admin.admin.list', {'qualifier': 'all'}) }}{% else %}{{ path('admin.user.list', {'qualifier': 'all'}) }}{% endif %}" style="display:flex; align-items: center; color: #9600FF">
            <div style="width: 10px; height: 10px; border: solid; border-color: #9600FF; border-width: 0 2px 2px 0;transform: rotate(135deg); margin-right: 5px"></div>
            {{ 'back.back_to_list' | trans }}
        </a>
    </div>

    <div style="width:45%; float: left">
        <table class="table table-bordered">
            <tr>
                <td>{{ 'back.user.form.id' | trans }}</td>
                <td>{{ user.id }}</td>
            </tr>
            <tr>
                <td>{{ 'back.user.form.email' | trans }}</td>
                <td>
                    {% if user.email %}
                        {{ user.email }}
                    {% endif %}
                </td>
            </tr>
            <tr>
                <td>{{ 'back.user.form.lastname' | trans }}</td>
                <td>
                    {% if user.lastname %}
                        {{ user.lastname }}
                    {% endif %}
                </td>
            </tr>
            <tr>
                <td>{{ 'back.user.form.firstname' | trans }}</td>
                <td>
                    {% if user.firstname %}
                        {{ user.firstname }}
                    {% endif %}
                </td>
            </tr>
            <tr>
                <td>{{ 'back.user.form.phone1' | trans }}</td>
                <td>
                    {% if user.mainPhoneNumber %}
                        {{ user.mainPhoneNumber }}
                    {% endif %}
                </td>
            </tr>
            <tr>
                <td>{{ 'back.user.form.phone2' | trans }}</td>
                <td>
                    {% if user.optionalPhoneNumber %}
                        {{ user.optionalPhoneNumber }}
                    {% endif %}
                </td>
            </tr>
            {% if not isAdmin %}
                <tr>
                    <td>{{ 'back.user.form.company' | trans }}</td>
                    <td>
                        {% if user.company and user.company.name %}
                            <a style="color: #9600FF" href="{{ path('admin.company.generalInfo', { 'id' : user.company.id})}}">
                                {{ user.company.name }}
                            </a>
                        {% endif %}
                    </td>
                </tr>
            {% endif %}
            <tr>
                <td>{{ 'back.user.function' | trans }}</td>
                <td>
                    {% if user.function %}
                        {{ user.function }}
                    {% endif %}
                </td>
            </tr>

            <tr>
                <td>{{ 'back.user.form.role' | trans }}</td>
                <td class="d-flex justify-content-between" style="border: 1px;">
                    {% if user.roles %}
                        {% set cpt = 0 %}
                        {% for role in user.roles %}
                            {% if role != constant("AppBundle\\Entity\\User::ROLE_USER") %}
                                {{ ('user.roles.values.' ~ role) | trans }}
                                {% if cpt != (user.roles|length - 2) %}
                                    {{ ' / ' }}
                                {% endif %}
                                {% set cpt = cpt + 1 %}
                            {% endif %}
                        {% endfor %}
                    {% endif %}
                    <a style="cursor: pointer" data-toggle="modal" data-target="#update-user-roles-modal"><i class="fas fa-edit"></i></a>
                </td>
            </tr>
            <tr>
                <td>{{ 'Marketplace' | trans }}</td>
                <td class="d-flex justify-content-between">
                    {{ user.marketplace.Name }}
                    <a style="cursor: pointer" data-toggle="modal" data-target="#update-user-marketplaces-modal"><i class="fas fa-edit"></i></a>
                </td>
            </tr>
            <tr>
                <td>{{ 'back.user.form.language' | trans }}</td>
                <td>
                    {% if user.locale %}
                        {{ ('node.form.lang.' ~ user.locale) | trans }}
                    {% endif %}
                </td>
            </tr>
            {% if (user.roles |first) !=  'ROLE_BUYER'%}
                <tr>
                    <td>{{ 'back.user.form.sites' | trans }}</td>
                    <td>
                        {% for site in user.sites %}
                            {{ site }}
                        {% endfor %}
                    </td>
                </tr>
            {% endif %}

            {% if superiors %}
                <tr>
                    <td>{{ 'back.user.form.superior' | trans }}</td>
                    <td>
                        {% if superiors is not null %}
                           {{ superiors.firstname }} {{ superiors.lastname }}
                           <br>
                           {{ superiors.email }}
                        {% endif %}
                    </td>
                </tr>
            {% endif %}

            {% if superiors and delegates %}
                <tr>
                    <td>{{ 'back.user.form.delegates' | trans }}</td>
                    <td>
                        <ul>
                            {% for buyer in delegates %}
                                <li>
                                    {{ buyer.firstname }} {{ buyer.lastname }}
                                    <br>
                                    {{ buyer.email }}
                                </li>
                            {% endfor %}
                        </ul>
                    </td>
                </tr>
            {% endif %}
        </table>
    </div>

    <div style="width:45%; float: right">
        <div id="actionBox" style="display: flex;">
            {% if user.enabled %}
                {% if user.id != currentUser.id %}
                        {{ form_start(form_activate_user, {'action': path('admin.user.deactivate'), 'attr': {'id': 'deactivateUserForm'}}) }}
                            {{ form_widget(form_activate_user.id) }}
                            {{ form_widget(form_activate_user.validate, { 'label': 'back.user.form.deactivate' | trans, 'attr': {'class': 'btn btn-danger'} } ) }}
                        {{ form_end(form_activate_user) }}
                {% endif %}
            {% else %}
                {{ form_start(form_activate_user, {'action': path('admin.user.activate')}) }}
                    {{ form_widget(form_activate_user.id) }}
                    {{ form_widget(form_activate_user.validate, { 'label': 'back.user.form.activate' | trans, 'attr': {'class': 'btn btn-info'} } ) }}
                {{ form_end(form_activate_user) }}
            {% endif %}

            {% if (user.roles |first) !=  'ROLE_BUYER'%}
                <a href="{{ path('admin.user.edit', { 'id' : user.id})}}"
                   class="btn btn-info"
                   role="button" aria-pressed="true">{{ 'back.user.form.edit' | trans }}</a>

                {{ form_start(form_reset_password_user, {'action': path('send.mail.reset.password', {'id': user.id})}) }}
                    {{ form_widget(form_reset_password_user.id) }}
                    {{ form_widget(form_reset_password_user.validate, { 'label': 'back.user.form.resetPassword' | trans, 'attr': {'class': 'btn btn-info'} } ) }}
                {{ form_end(form_reset_password_user) }}
            {% endif %}
        </div>
    </div>

    <div style="clear: both">
    </div>

    <h2>{{ 'back.user.connection.title'|trans }}</h2>

    <table class="table table-hover">
        <thead>
            <tr>
                <th>{{ 'back.user.connection.date'|trans }}</th>
                <th>{{ 'back.user.connection.hour'|trans }} </th>
                <th>{{ 'back.user.connection.ip'|trans }} </th>
                <th>{{ 'back.user.connection.browser'|trans }} </th>
                <th>{{ 'back.user.connection.version'|trans }} </th>
                <th>{{ 'back.user.connection.os'|trans }} </th>
                <th>{{ 'back.user.connection.type'|trans }} </th>
            </tr>
        </thead>
        <tbody>
            {% for connection in pagination %}
                <tr>
                    <td>{{ connection.connectedAt | localizeddate('short', 'none', locale)}}</td>
                    <td>{{ connection.connectedAt | date('H:i:s')}}</td>
                    <td>{{ connection.ip }}</td>
                    <td>{{ connection.browser }}</td>
                    <td>{{ connection.browserVersion }}</td>
                    <td>{{ connection.platform }}</td>
                    <td>{{ connection.deviceType }}</td>
                </tr>

            {% endfor %}

        </tbody>

    </table>
    {{ knp_pagination_render(pagination) }}


    <h2>{{ 'back.user.history.title'|trans }}</h2>
    <table class="table table-hover">
        <thead>
        <tr>
            <th>{{ 'back.user.history.date'|trans }}</th>
            <th>{{ 'back.user.history.hour'|trans }} </th>
            <th> {{ 'back.user.history.id'|trans }}</th>
            <th>{{ 'back.user.history.role'|trans }} </th>
            <th>{{ 'back.user.history.modifications'|trans }} </th>
        </tr>
        </thead>
        <tbody>
        {% for history in actionPagination %}
            {% if history.changeSet is not empty %}
                <tr>
                    <td>{{ history.createdAt | localizeddate('short', 'none', locale)}}</td>
                    <td>{{ history.createdAt | date('H:i:s')}}</td>
                    <td>
                        {% if history.users %}
                        {{ history.users.0.firstname }} {{ history.users.0.lastname }}
                             {% endif %}
                    </td>
                    <td>  {% if history.users %}
                            {{ history.users.0.roles.0 }}
                        {% endif %}
                    </td>
                    {% if history.type == 'create' %}
                        <td>{{ history.changeSet|json_to_html|raw }}</td>
                    {% elseif history.type == 'update' %}
                        <td>
                            {{ history.changeSet|json_to_html|raw }}
                        </td>
                    {% else %}
                        <td>-</td>
                    {% endif %}
                </tr>
            {% endif %}
        {% endfor %}
        </tbody>
    </table>
    {{ knp_pagination_render(actionPagination) }}

{% endblock %}
{% block javascripts %}
    <script src="{{  asset('libs/select2/select2.full.min.js') }}" defer></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            $('#deactivateUser').on('click', function (ev) {
                var href = $(ev.currentTarget).attr('href');

                ev.preventDefault();

                window.UI.Modal.confirm('{{ 'back.user.deactivate.title'|trans({}, 'AppBundle') }}', '{{ 'back.user.deactivate.content'|trans({}, 'AppBundle') }}', function () {
                        window.UI.Modal.showLoading();
                        $('#deactivateUserForm').submit();
                    },
                    function () {
                        console.log('Confirmation canceled');
                    });

                return false;
            });
            $('.custom-select').select2();
        });
    </script>
{% endblock %}
