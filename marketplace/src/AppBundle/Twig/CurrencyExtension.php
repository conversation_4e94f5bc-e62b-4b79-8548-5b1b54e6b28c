<?php

namespace AppBundle\Twig;


use AppBundle\Services\CurrencyExchangeRateService;
use Twig\Extension\AbstractExtension;
use Twig\TwigFunction;

/**
 * Class CurrencyExtension
 * @package Open\FrontBundle\Twig
 */
class CurrencyExtension extends AbstractExtension
{
    public function getFunctions() {
        return array(
            new TwigFunction('currency', array($this, 'currency')),
        );
    }

    function currency($amount, $rate,$toCurrency,$fromCurrency) {
        return  CurrencyExchangeRateService::getAmountWithRate($amount,$rate,$toCurrency,$fromCurrency);


    }
}