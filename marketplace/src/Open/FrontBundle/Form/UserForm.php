<?php

namespace Open\FrontBundle\Form;

use AppBundle\Entity\User;

use AppBundle\Repository\SiteRepository;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\EmailType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Validator\Constraints\Count;
use Symfony\Component\Validator\Constraints\NotBlank;

class UserForm extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options)
    {
        $roles = [
            'form.user.role.buy'   => 'ROLE_BUYER',
        ];

        $builder
            ->add('firstname', TextType::class, ['label' => 'form.user.firstname'])
            ->add('lastname', TextType::class, ['label' => 'form.user.lastname'])
            ->add('function', TextType::class, ['label' => 'form.user.function'])
            ->add(
            	'email',
				EmailType::class,
				array(
					'label' => 'form.user.email'
				)
			);

        if ($options['is_new']) {
			$builder->add(
				'role',
				ChoiceType::class,
				[
					'label' => 'form.user.roles',
					'placeholder' => 'form.user.role.placeholder',
					'choices' => $roles,
					'choice_translation_domain' => null,
					'constraints' => array(new NotBlank()),
                ]
			);
		} else {
			$builder->add(
				'role',
				ChoiceType::class,
				[
					'label' => 'form.user.roles',
					'choices' => $roles,
					'choice_translation_domain' => null,
					'constraints' => array(new NotBlank()),
                ]
			);
		}

        $builder->add(
            'sites',
            EntityType::class,
            [
                'label' => 'form.user.sites.label',
                'class' => 'AppBundle:Site',
                'choice_label' => 'name',
                'multiple' => true,
                'expanded' => true,
                'choice_translation_domain' => null,
                'query_builder' => function (SiteRepository $er) use (&$options){
                    return $er->createQueryBuilder('s')
                        ->where("s.company = :userCompanyId")
                        ->orderBy('s.name', 'ASC')
                        ->setParameter("userCompanyId", $options['company_id']);
                },
            ]
        );


		$builder->setMethod('POST');
    }

    public function configureOptions(OptionsResolver $resolver)
    {
        $resolver->setDefaults([
            'validation_groups' => ['user_registration'],
            'data_class' => User::class,
            'company_id' => null,
			'is_new' => false,
			'company_type' => null,
			'translation_domain' => 'AppBundle',
        ]);
    }
}