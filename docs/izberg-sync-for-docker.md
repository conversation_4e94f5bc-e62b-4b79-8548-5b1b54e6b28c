### [< Back...](README.md)

# Izberg - Elasticsearch Synchronization for docker

#### 1 - Update consumer configuration

```
File location: offers-sync/consumer/configuration.yml
============================================================

logs_file_path: "/var/www/offers-sync/consumer/consumer.log"

rabbit_host: "rabbitmq"
rabbit_port: 5672
rabbit_user: "guest"
rabbit_password: "guest"
rabbit_queue: "offers"
rabbit_qos_nb_message: 10
 
es_host: "elasticsearch:9200"
es_certificate_path: "toto"
es_index: "offers"
es_ssl: false
```

#### 2 - Update producer configuration

```
File location: : offers-sync/producer/configuration.yml
===============================================================

logs_file_path: "/var/www/offers-sync/producer/producer.log"
 
rabbit_host: "rabbitmq"
rabbit_port: 5672
rabbit_user: "guest"
rabbit_password: "guest"
rabbit_queue: "offers"
```

#### 3 - Retrieve offers from izberg channel
```
docker exec -it offers-producer bash       
php -d memory_limit=-1 /usr/local/bin/composer install --no-scripts
php -d memory_limit=512M ProducerRunner.php 
```

#### 4 - Update elasticsearch index offers
```
docker exec -it offers-consumer bash
php -d memory_limit=-1 /usr/local/bin/composer install --no-scripts
php ConsumerRunner.php
```
