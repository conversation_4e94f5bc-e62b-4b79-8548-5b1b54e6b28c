<?php
/**
 * Created by PhpStorm.
 * User: CMA15450
 * Date: 08/03/2017
 * Time: 11:52
 */

namespace AppBundle\Entity;

use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Validator\Constraints as Assert;
use Doctrine\ORM\Mapping\UniqueConstraint;

/**
* @ORM\Entity
* @ORM\Table(name="`settings`",uniqueConstraints={@UniqueConstraint(name="unique_setting", columns={"domain", "name"})})
* @ORM\Entity(repositoryClass="AppBundle\Repository\SettingRepository")
*/
class Setting implements \JsonSerializable
{

    use TechnicalIdentifierTrait;

    /**
     * @ORM\Id
     * @ORM\Column(type="integer")
     * @ORM\GeneratedValue(strategy="AUTO")
     */
    private $id;

    /**
     * @ORM\Column(name="domain",type="string", length=20)
     */
    private $domain;

    /**
     * @ORM\Column(name="name",type="string", length=255)
     */
    private $name;

    /**
     * @ORM\Column(name="value_type",type="string", length=50)
     */
    private $value_type = "integer";

    /**
     * @ORM\Column(name="value",type="text", length=65535, nullable=true)
     */
    private $value;

    /**
     * @ORM\Column(name="required",type="boolean")
     */
    private $required = false;

    /**
     * @ORM\Column(name="tags",type="simple_array", nullable=true)
     */
    private $tags;


    /**
     * @return int|mixed
     */
    public function getValue() {
        if ($this->value_type == 'integer') {
            return intval($this->value);
        } else if ($this->value_type == 'array' ) {
            return unserialize($this->value);
        } else {
            return $this->value;
        }
    }

    /**
     * @param $value
     */
    public function setValue($value) {
        if ($this->value_type == 'array' ) {
            $this->value = serialize($value);
        } else {
            $this->value = $value;
        }
    }

    /**
     * @return mixed
     */
    public function getDomain() {
        return $this->domain;
    }

    /**
     * @param $domain
     */
    public function setDomain($domain) {
        $this->domain = $domain;
    }

    /**
     * @return mixed
     */
    public function getName() {
        return $this->name;
    }

    /**
     * @param $name
     */
    public function setName($name) {
        $this->name = $name;
    }

    /**
     * @return string
     */
    public function getValueType() {
        return $this->value_type;
    }

    /**
     * @param $value_type
     */
    public function setValueType($value_type) {
        $this->value_type = $value_type;
    }

    /**
     * @return mixed
     */
    public function getId(){
        return $this->id;
    }

    /**
     * @return bool
     */
    public function getRequired() {
        return $this->required;
    }

    /**
     * @param $boolean
     * @return bool
     */
    public function setRequired($boolean)
    {
        return $this->required = (bool) $boolean;
    }

    /**
     * @return string|array
     */
    public function getTags() {
        if ($this->tags == null) {
            return array();
        } else {
            return implode(',',$this->tags);
        }
    }

    /**
     * @param $tags
     */
    public function setTags($tags) {

        if (!is_array($tags)) {
            $this->tags = explode(',', $tags);
        } else {
            $this->tags = $tags;
        }
    }

    /**
     * @return string
     */
    public function getFullName() {
        return $this->domain . '.' . $this->name;
    }

    /**
     * @return string
     */
    public function __toString()
    {
        return $this->domain . '.' . $this->name;
    }

    public function jsonSerialize() {
        return [
            'name' => $this->name,
            'domain' => $this->domain,
            'value_type' => $this->value_type,
            'value' => $this->value,
            'required' => $this->required

        ];
    }

}