<?php
/**
 * Created by PhpStorm.
 * User: PPH04712
 * Date: 29/01/2018
 * Time: 10:07
 */

namespace Open\BackBundle\Form;


use AppBundle\Entity\NodeContent;
use Open\BackBundle\Form\Type\SlugType;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\HiddenType;
use Symfony\Component\Form\Extension\Core\Type\TextareaType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

class SliderContentForm extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options)
    {

        // only the value can be changed from this form
        $builder
            ->add('id', HiddenType::class)
            ->add('lang', HiddenType::class)
            ->add(
                'title',
                null,
                array(
                    'label' => 'back.notification.edit.title'
                )
            )
            ->add(
                'body',
                TextareaType::class,
                array(
                    'label' => 'back.notification.edit.body'
                )
            )
            ->add(
                'link',
                SlugType::class,
                array(
                    'label' => 'back.notification.edit.link',
                    'label_attr' => ['class' => 'linkSlug'],
                )
            )
            ->add(
                'linkText',
                TextType::class,
                array(
                    'label' => 'back.notification.edit.linkText',
                    'required' => false
                )
            );

    }

    public function configureOptions(OptionsResolver $resolver)
    {
        $resolver->setDefaults([
            'translation_domain' => 'AppBundle',
            'data_class' => NodeContent::class,
            'validation_groups' => 'page'
        ]);
    }

}
