<?php
/**
 * Created by PhpStorm.
 * User: CMA15450
 * Date: 10/01/2018
 * Time: 16:48.
 */

namespace AppBundle\Entity;

use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Bridge\Doctrine\Validator\Constraints\UniqueEntity;

/**
 * Quote.
 *
 * @ORM\Table(name="quote")
 * @ORM\Entity(repositoryClass="AppBundle\Repository\QuoteRepository")
 * @UniqueEntity(fields="id")
 * @ORM\HasLifecycleCallbacks()
 */
class Quote implements \JsonSerializable
{
    use TimestampedTrait;
    use TechnicalIdentifierTrait;

    public const STATUS_NEW = 'new';
    public const STATUS_DRAFT = 'draft';
    public const STATUS_REDRAFT = 'redraft';
    public const STATUS_SEND = 'send';
    public const STATUS_VALIDATED = 'validated';
    public const STATUS_CANCELLED = 'cancelled';
    public const STATUS_REFUSED = 'refused';

    // META BUYER
    public const META_STATUS_BUYER_DRAFT = 'buyer_draft';
    public const META_STATUS_BUYER_CANCELLED = 'buyer_cancelled';

    public const META_BUYER_STATUS_DEF = [
        self::META_STATUS_BUYER_DRAFT => [
            self::STATUS_NEW,
            self::STATUS_DRAFT,
            self::STATUS_REDRAFT,
            self::STATUS_SEND
        ],
        self::META_STATUS_BUYER_CANCELLED => [
            self::STATUS_CANCELLED,
            self::STATUS_REFUSED,
        ],
    ];

    //META VENDOR
    public const META_STATUS_VENDOR_DRAFT = 'vendor_draft';
    public const META_STATUS_VENDOR_CANCELLED = 'vendor_cancelled';

    public const META_VENDOR_STATUS_DEF = [
        self::META_STATUS_VENDOR_DRAFT => [
            self::STATUS_DRAFT,
            self::STATUS_REDRAFT,
        ],
        self::META_STATUS_VENDOR_CANCELLED => [
            self::STATUS_CANCELLED,
            self::STATUS_REFUSED,
        ],
    ];

    private const TRANSITION = [
        self::STATUS_NEW => [
            self::STATUS_DRAFT,
            self::STATUS_CANCELLED,
            self::STATUS_REFUSED,
        ],
        self::STATUS_DRAFT => [
            self::STATUS_SEND,
            self::STATUS_CANCELLED,
            self::STATUS_REFUSED,
        ],
        self::STATUS_SEND => [
            self::STATUS_VALIDATED,
            self::STATUS_REDRAFT,
            self::STATUS_CANCELLED,
        ],
        self::STATUS_REDRAFT => [
            self::STATUS_SEND,
            self::STATUS_REDRAFT,
        ],
        self::STATUS_VALIDATED => [
        ],
        self::STATUS_CANCELLED => [
        ],
        self::STATUS_REFUSED => [
        ],
    ];

    /**
     * @var int
     * @ORM\Column(name="id", type="integer")
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="AUTO")
     */
    private $quoteId;

    /**
     * @var User
     * @ORM\ManyToOne(targetEntity="AppBundle\Entity\User", inversedBy="quotes", cascade={})
     * @ORM\JoinColumn(name="buyer", referencedColumnName="id")
     */
    private $buyer;

    /**
     * @var \DateTime
     * @ORM\Column(name="validation_date", type="datetime", nullable=true)
     */
    private $validationDate;

    /**
     * @var \DateTime
     * @ORM\Column(name="cancel_date", type="datetime", nullable=true)
     */
    private $cancelDate;

    /**
     * @var String
     * @ORM\Column(name="init_offer_url", type="string",length=255, nullable=true)
     */
    private $initialOfferUrl;

    /**
     * @var String
     * @ORM\Column(name="init_offer_ref", type="string",length=255, nullable=false)
     */
    private $initialOfferReference;

    /**
     * @var String
     * @ORM\Column(name="init_offer_title", type="string",length=500, nullable=false)
     */
    private $initialOfferTitle;

    /**
     * @var String
     * @ORM\Column(name="init_offer_description", type="text", nullable=true)
     */
    private $initialOfferDescription;

    /**
     * @var
     * @ORM\OneToMany(targetEntity="AppBundle\Entity\QuoteItem", mappedBy="quote",  cascade={"persist", "remove", "merge"}, orphanRemoval=true)
     */
    private $quoteItems;

    /**
     * @var String
     * @ORM\Column(name="quoteNumber", type="string",length=20, nullable=true)
     */
    private $quoteNumber;

    /**
     * @var String
     * @ORM\Column(name="status", type="string",length=50, nullable=false)
     */
    private $status;

    /**
     * @var String
     * @ORM\Column(name="subject", type="string",length=700, nullable=true)
     */
    private $subject;

    /**
     * @var String
     * @ORM\Column(name="title", type="string",length=500, nullable=true)
     */
    private $title;

    /**
     * @var float
     * @ORM\Column(name="total_price", type="float", nullable=true)
     */
    private $totalPrice = 0.0;

    /**
     * @var Merchant
     * @ORM\ManyToOne(targetEntity="AppBundle\Entity\Merchant", inversedBy="quotes", cascade={})
     * @ORM\JoinColumn(name="vendor", referencedColumnName="id")
     */
    private $vendor;

    /**
     * @var String
     * @ORM\Column(name="message", type="text", nullable=true)
     */
    private $message;

    /**
     * @var int
     * @ORM\Column(name="version", type="integer", nullable=false)
     */
    private $version = 1;

    /**
     * @ORM\OneToMany(targetEntity="AppBundle\Entity\QuoteVersion", mappedBy="quote", cascade={"persist", "remove", "merge"}, orphanRemoval=true)
     */
    private $quoteVersions;

    /**
     * @var boolean
     * @ORM\Column(name="buyer_unread", type="boolean", nullable=false)
     */
    private $buyerUnread = false;

    /**
     * @var boolean
     * @ORM\Column(name="vendor_unread", type="boolean", nullable=false)
     */
    private $vendorUnread = true;

    /**
     * @var String
     * @ORM\Column(name="vat_group_name", type="string", nullable=true)
     */
    private $vatGroupName;

    /** @var int
     * @ORM\Column(name="izberg_thread_id", type="integer", nullable=true)
     */
    private $threadId;

    /**
     *  transitive info to handle cancel and negociate reason
     * @var String
     */
    private $reason;

    /**
     * @var float
     * @ORM\Column( type="decimal", name="currency_rate_country", precision=25, scale=15, nullable=false)
     */
    private $currencyRateCountryOfdelivery = 1;


    /**
     * Quote constructor.
     */
    public function __construct()
    {
        $this->status = self::STATUS_NEW;
        $this->quoteVersions = new ArrayCollection();
    }

    public function __toString()
    {
        return 'quote.' . $this->title;
    }

    /**
     * @return int
     */
    public function getQuoteId(): int
    {
        return $this->quoteId;
    }

    /**
     * @param int $quoteId
     */
    public function setQuoteId(int $quoteId): void
    {
        $this->quoteId = $quoteId;
    }

    /**
     * @return mixed
     */
    public function getBuyer(): User
    {
        return $this->buyer;
    }

    /**
     * @param mixed $buyer
     */
    public function setBuyer($buyer): void
    {
        $this->buyer = $buyer;
    }

    /**
     * @return \DateTime
     */
    public function getValidationDate(): ?\DateTime
    {
        return $this->validationDate;
    }

    /**
     * @param \DateTime $validationDate
     */
    public function setValidationDate(\DateTime $validationDate): void
    {
        $this->validationDate = $validationDate;
    }

    /**
     * @return \DateTime
     */
    public function getCancelDate(): ?\DateTime
    {
        return $this->cancelDate;
    }

    /**
     * @param \DateTime $cancelDate
     */
    public function setCancelDate(\DateTime $cancelDate): void
    {
        $this->cancelDate = $cancelDate;
    }

    /**
     * @return String
     */
    public function getInitialOfferUrl(): string
    {
        return $this->initialOfferUrl;
    }

    /**
     * @param string $initialOfferUrl
     */
    public function setInitialOfferUrl(string $initialOfferUrl): void
    {
        $this->initialOfferUrl = $initialOfferUrl;
    }

    /**
     * @return String
     */
    public function getInitialOfferReference(): string
    {
        return $this->initialOfferReference;
    }

    /**
     * @param string $initialOfferReference
     */
    public function setInitialOfferReference(string $initialOfferReference): void
    {
        $this->initialOfferReference = $initialOfferReference;
    }

    /**
     * @return String
     */
    public function getInitialOfferTitle(): string
    {
        return $this->initialOfferTitle;
    }

    /**
     * @param string $initialOfferTitle
     */
    public function setInitialOfferTitle(string $initialOfferTitle): void
    {
        $this->initialOfferTitle = $initialOfferTitle;
    }

    /**
     * @return String
     */
    public function getInitialOfferDescription(): string
    {
        return $this->initialOfferDescription;
    }

    /**
     * @param string $initialOfferDescription
     */
    public function setInitialOfferDescription(string $initialOfferDescription): void
    {
        $this->initialOfferDescription = $initialOfferDescription;
    }

    /**
     * @return mixed
     */
    public function getQuoteItems()
    {
        return $this->quoteItems;
    }

    /**
     * @param mixed $quoteItems
     */
    public function setQuoteItems($quoteItems): void
    {
        $this->quoteItems = $quoteItems;
    }

    /**
     * @return String
     */
    public function getQuoteNumber(): ?string
    {
        return $this->quoteNumber;
    }

    /**
     * @param string $quoteNumber
     */
    public function setQuoteNumber(?string $quoteNumber): void
    {
        $this->quoteNumber = $quoteNumber;
    }

    /**
     * @return String
     */
    public function getStatus(): string
    {
        return $this->status;
    }

    /**
     * @param string $status
     */
    public function setStatus(string $status): void
    {
        $this->status = $status;
    }

    /**
     * @return String
     */
    public function getSubject(): ?string
    {
        return $this->subject;
    }

    /**
     * @param string $subject
     */
    public function setSubject(string $subject): void
    {
        $this->subject = $subject;
    }

    /**
     * @return String
     */
    public function getTitle(): ?string
    {
        return $this->title;
    }

    /**
     * @param string $title
     */
    public function setTitle(?string $title): void
    {
        $this->title = $title;
    }

    /**
     * @return Merchant
     */
    public function getVendor(): Merchant
    {
        return $this->vendor;
    }

    /**
     * @param Merchant $vendor
     */
    public function setVendor(Merchant $vendor): void
    {
        $this->vendor = $vendor;
    }

    public function jsonSerialize(): mixed
    {
        return [
            'title' => $this->title,
        ];
    }

    public static function isValidStatus($status)
    {
        return in_array(
                $status,
                self::getValidStatuses()
            ) || in_array(
                $status,
                self::getMetaStatuses()
            );
    }

    public static function getValidStatuses(): array
    {
        return [
            self::STATUS_NEW,
            self::STATUS_DRAFT,
            self::STATUS_REDRAFT,
            self::STATUS_SEND,
            self::STATUS_VALIDATED,
            self::STATUS_CANCELLED,
            self::STATUS_REFUSED,
        ];
    }

    public static function getMetaStatuses(): array
    {
        return [
            self::META_STATUS_BUYER_CANCELLED,
            self::META_STATUS_BUYER_DRAFT,
        ];
    }

    /** get MetaStatus for Twig detail view */
    public function getMeta()
    {
        return self::getMetaStatusBuyer($this->getStatus());
    }

    public static function getMetaStatusBuyer($status)
    {
        foreach (self::META_BUYER_STATUS_DEF as $meta => $value) {
            if (in_array($status, $value)) {
                return $meta;
            }
        }

        return $status;
    }

    public static function getMetaStatusVendor($status)
    {
        foreach (self::META_VENDOR_STATUS_DEF as $meta => $value) {
            if (in_array($status, $value)) {
                return $meta;
            }
        }

        return $status;
    }

    /**
     * @return float
     */
    public function getTotalPrice(): ?float
    {
        if ($this->totalPrice) {
            return $this->totalPrice;
        }
        return 0.0;

    }

    /**
     * @param float $totalPrice
     */
    public function setTotalPrice(float $totalPrice): void
    {
        $this->totalPrice = $totalPrice;
    }

    /**
     * @return String
     */
    public function getMessage(): ?string
    {
        return $this->message;
    }

    /**
     * @param string $message
     */
    public function setMessage(?string $message): void
    {
        $this->message = $message;
    }

    /**
     * @return int
     */
    public function getVersion(): int
    {
        return $this->version;
    }

    /**
     * @param int $version
     */
    public function setVersion(int $version): void
    {
        $this->version = $version;
    }

    /**
     * Check if the status transition is authorized.
     *
     * @param $wantedStatus
     *
     * @return bool
     */
    public function checkStatusTransition($wantedStatus)
    {
        if (in_array($wantedStatus, self::TRANSITION[$this->status])) {
            return true;
        }

        return false;
    }

    /**
     * @return mixed
     */
    public function getQuoteVersions()
    {
        return $this->quoteVersions;
    }

    /**
     * @param mixed $quoteVersions
     */
    public function setQuoteVersions($quoteVersions): void
    {
        $this->quoteVersions = $quoteVersions;
    }

    public function addVersion(QuoteVersion $version)
    {
        $this->quoteVersions->add($version);
        $version->setQuote($this);

        return $this;
    }

    /**
     * @return bool
     */
    public function isBuyerUnread(): bool
    {
        return $this->buyerUnread;
    }

    /**
     * @param bool $buyerUnread
     */
    public function setBuyerUnread(bool $buyerUnread): void
    {
        $this->buyerUnread = $buyerUnread;
    }

    /**
     * @return bool
     */
    public function isVendorUnread(): bool
    {
        return $this->vendorUnread;
    }

    /**
     * @param bool $vendorUnread
     */
    public function setVendorUnread(bool $vendorUnread): void
    {
        $this->vendorUnread = $vendorUnread;
    }

    /**
     * @return String
     */
    public function getVatGroupName(): ?string
    {
        return $this->vatGroupName;
    }

    /**
     * @param string|null $vatGroupName
     */
    public function setVatGroupName(?string $vatGroupName): void
    {
        $this->vatGroupName = $vatGroupName;
    }

    /**
     * @return int
     */
    public function getThreadId(): int
    {
        return $this->threadId;
    }

    /**
     * @param int $threadId
     */
    public function setThreadId(int $threadId): void
    {
        $this->threadId = $threadId;
    }

    /**
     * @return String
     */
    public function getReason(): string
    {
        return $this->reason;
    }

    /**
     * @param String $reason
     */
    public function setReason(string $reason): void
    {
        $this->reason = $reason;
    }


    public function getPdfName()
    {

    }

    /**
     * @return float
     */
    public function getCurrencyRateCountryOfdelivery(): float
    {
        return $this->currencyRateCountryOfdelivery;
    }

    /**
     * @param float $currencyRateCountryOfdelivery
     */
    public function setCurrencyRateCountryOfdelivery(float $currencyRateCountryOfdelivery): void
    {
        $this->currencyRateCountryOfdelivery = $currencyRateCountryOfdelivery;
    }

    public function getQuoteItemBdd(?int $quoteItemId): ?QuoteItem
    {
        if ($quoteItemId === null) {
            return null;
        }
        foreach ($this->getQuoteItems() as $quoteItem) {
            if ($quoteItem->getId() === $quoteItemId) {
                return $quoteItem;
            }
        }
        return null;

    }


}
