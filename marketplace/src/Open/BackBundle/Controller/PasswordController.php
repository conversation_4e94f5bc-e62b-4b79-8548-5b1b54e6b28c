<?php

namespace Open\BackBundle\Controller;

use <PERSON><PERSON><PERSON><PERSON><PERSON>\Controller\MkoController;
use Illuminate\Encryption\Encrypter;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Security;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;

class PasswordController extends MkoController
{


    /**
     * @Route("/password", name="password.decrypt", options={"expose"=true}, methods={"GET"})
     * @Security("is_granted('ROLE_SUPER_ADMIN')")
     */
    public function getPassword(Request $request): \Symfony\Component\HttpFoundation\Response
    {

        $key = (string) $this->getParameter('crypt_key');

        $encrypter = new Encrypter($key);

        $password = $encrypter->decrypt($request->query->get('password'));

        return $this->render('@OpenBack/password.html.twig', ['password' => $password]);
    }
}