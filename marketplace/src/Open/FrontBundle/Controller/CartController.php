<?php

namespace Open\FrontBundle\Controller;

use AppBundle\Controller\MkoController;
use AppBundle\Entity\MerchantOrder;
use AppBundle\Entity\MetaCart;
use AppBundle\Entity\MetaCartCheckout;
use AppBundle\Entity\User;
use AppBundle\Factory\CartFactory;
use AppBundle\Form\BuyerAddressForm;
use AppBundle\Model\BuyerAddressModel;
use AppBundle\Model\Cart\Cart;
use AppBundle\Model\Cart\CartItem;
use AppBundle\Model\Cart\CartMerchant;
use AppBundle\Model\Offer;
use AppBundle\Repository\MerchantOrderRepository;
use AppBundle\Repository\MetaCartRepository;
use AppBundle\Services\AddressService;
use AppBundle\Services\BuyerService;
use AppBundle\Services\CartCheckoutService;
use AppBundle\Services\CartService;
use AppBundle\Services\CountryService;
use AppBundle\Services\CustomsService;
use AppBundle\Services\MailService;
use AppBundle\Services\MerchantService;
use AppBundle\Services\MetaCartMerchantService;
use AppBundle\Services\OfferService;
use Exception;
use Open\IzbergBundle\Api\ApiException;
use Open\IzbergBundle\Api\CartApi;
use Open\IzbergBundle\Api\IzbergUtils;
use Open\IzbergBundle\Api\ProductApi;
use Open\IzbergBundle\Service\CategoryService;
use Open\LogBundle\Service\LogService;
use Open\LogBundle\Utils\EventNameEnum;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Security;
use Symfony\Component\Form\Extension\Core\Type\CheckboxType;
use Symfony\Component\Form\Extension\Core\Type\CollectionType;
use Symfony\Component\Form\Extension\Core\Type\FormType;
use Symfony\Component\Form\Extension\Core\Type\HiddenType;
use Symfony\Component\Form\Extension\Core\Type\IntegerType;
use Symfony\Component\Form\Extension\Core\Type\SubmitType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormFactoryInterface;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Routing\Generator\UrlGeneratorInterface;
use Symfony\Component\Validator\Constraints\Length;
use Symfony\Component\Validator\Constraints\NotBlank;
use Symfony\Contracts\Translation\TranslatorInterface;


class CartController extends MkoController
{
    private LogService $logger;
    private MailService $mailService;

    /**
     * CartController constructor.
     *
     * @param LogService $logger
     */
    public function __construct(LogService $logger, MailService $mailService)
    {
        $this->logger = $logger;
        $this->mailService = $mailService;
    }

    /**
     * @Route("/cart", name="cart.details")
     * @Route("/cart/details", name="cart.details.before_buy")
     * @Security("is_granted('ROLE_BUYER')")
     * @param Request $request
     * @param CartService $cartService
     * @param BuyerService $buyerService
     * @param OfferService $offerService
     * @param CustomsService $customsService
     * @param ProductApi $productApi
     *
     * @return Response
     */
    public function currentCartDetailsAction(
        Request        $request,
        CartService    $cartService,
        BuyerService   $buyerService,
        OfferService   $offerService,
        CustomsService $customsService,
        ProductApi     $productApi
    ): Response
    {
        /** @var User $buyer */
        $buyer = $this->getUser();
        $cart = $cartService->loadCurrentBuyerCart($buyer);
        $locale = $request->getLocale();
        $risk = false;
        $merchantIds = [];
        $items = array_filter(
            array_map(static function (CartItem $offer) use ($buyer, $cart, $cartService, $offerService, $customsService, $productApi, $locale, &$risk, &$merchantIds) {
                $esOffer = $offerService->findOfferForCountryOfDelivery($offer->getOfferId(), $buyer->getCountryOfDelivery(), $locale, $buyer->getMarketPlace());
                if (!$esOffer) {
                    $cartService->removeItemFromCart($buyer, $cart->getId(), $offer->getId());
                    return null;
                }
                if (!$esOffer->getProduct()->getCategoriesIds()) {
                    $product = $productApi->getProduct($esOffer->getProduct()->getId(), $locale);
                    $categories = array_map(static function (array $category) {
                        return $category["id"];
                    }, $product["application_categories_dict"]);
                    $esOffer->getProduct()->setCategoriesIds($categories);
                }
                $offer->setRisk($customsService->categoriesIdsInRisk($esOffer->getProduct()->getCategoriesIds()));
                if ($offer->isRisk()) {
                    $risk = true;
                }
                $offer->setName($esOffer->getName());
                $offer->setImageUrl($esOffer->getPicture());
                $offer->setOfferStatus($esOffer->getStatus());
                $offer->setStock($esOffer->getStock());
                $merchantIds[$offer->getMerchantId()] = $offer->getMerchantId();
                return $offer;

            }, $cart->getItems())
        );
        $cart->setItems($items);
        $isMobile = (boolean)$request->query->get('isMobile', '0');

        return $this->cartDetailsHtmlResponse(
            $cart,
            $buyer,
            $isMobile,
            $request->isXmlHttpRequest(),
            $buyerService,
            $risk,
            count($merchantIds) > 1
        );
    }

    /**
     * @Route("/cart/{cartId}/details", name="front.cart.details")
     * @Security("is_granted('ROLE_BUYER')")
     * @param Request $request
     * @param int $cartId
     * @param CartService $cartService
     * @param BuyerService $buyerService
     *
     * @return Response
     */
    public function cartDetailsAction(
        Request      $request,
        int          $cartId,
        CartService  $cartService,
        BuyerService $buyerService
    ): Response
    {
        /** @var User $buyer */
        $buyer = $this->getUser();
        $cart = $cartService->loadBuyerCart($buyer, $cartId);
        $isMobile = (boolean)$request->query->get('isMobile', '0');

        if (!$cart) {
            return new Response('', Response::HTTP_NOT_FOUND);
        }

        return $this->cartDetailsHtmlResponse(
            $cart,
            $buyer,
            $isMobile,
            false,
            $buyerService,
            $cart->isRisk()
        );
    }

    /**
     * @param Cart $cart
     * @param User $buyer
     * @param bool $isMobile
     * @param bool $isAjaxRequest
     * @param BuyerService $buyerService
     * @param bool $risk
     * @param bool|null $multiMerchants
     * @return Response
     */
    private function cartDetailsHtmlResponse(
        Cart         $cart,
        User         $buyer,
        bool         $isMobile,
        bool         $isAjaxRequest,
        BuyerService $buyerService,
        bool         $risk,
        ?bool        $multiMerchants = false
    ): Response
    {
        $tpl = '@OpenFront/cart/cart_details.html.twig';
        $tplEmpty = '@OpenFront/cart/cart_empty.html.twig';

        if ($isAjaxRequest) {
            $tpl = '@OpenFront/cart/cart_tables.html.twig';
            $tplEmpty = '@OpenFront/cart/cart_empty_partial.html.twig';
        }

        if (!$cart->getItemsCount()) {
            return $this->render(
                $tplEmpty,
                [
                    'user' => $buyer,
                ]
            );
        }

        $buyerAddressModel = new BuyerAddressModel();
        $shippingAddress = $cart->getBuyerShippingAddress();

        if ($shippingAddress) {

            $buyerAddressModel->setTechnicalId($shippingAddress->getTechnicalId());
            $buyerAddressModel->setType($shippingAddress->getType());

            $buyerAddressModel->setName($shippingAddress->getName());

            $buyerAddressModel->setAddress($shippingAddress->getAddress());
            $buyerAddressModel->setAddress2($shippingAddress->getAddress2());
            $buyerAddressModel->setZipCode($shippingAddress->getZipCode());
            $buyerAddressModel->setCity($shippingAddress->getCity());
            $buyerAddressModel->setCountry($shippingAddress->getCountry());

            $buyerAddressModel->setContact($shippingAddress->getContact());
            $buyerAddressModel->setPhone($shippingAddress->getPhone());
            $buyerAddressModel->setComment($shippingAddress->getComment());
        }
        $buyerAddressForm = $this->createForm(BuyerAddressForm::class, $buyerAddressModel, [
            'marketplace_name' => $buyer->getMarketPlace() ? $buyer->getMarketPlace()->getName() : 'france'
        ]);
        $buyerManager = $buyerService->getBuyerSuperior($buyer);
        $cartQuotedElements = array_filter($cart->getItems(), function (CartItem $cartItem) {
            return ($cartItem->isQuote() === true);
        });

        $merchantsOrdersAmountNotEnough = false;
        foreach ($cart->getMerchants() as $merchant) {
            if ($merchant->getTotalWithoutDeliveryPrice() < $merchant->getMinimumOrderAmount()) {
                $merchantsOrdersAmountNotEnough = true;
                break;
            }
        }

        return $this->render($tpl, [
            'cart' => $cart,
            'user' => $buyer,
            'hasEnabledManager' => ($buyerManager && $buyerManager->isEnabled()),
            'isUserAuthorizeToEditCart ' => true,
            'sites' => [],
            'buyerAddress' => $buyerAddressModel,
            'buyerAddressForm' => $buyerAddressForm->createView(),
            'isMobile' => $isMobile,
            'cartContainsQuotedElements' => count($cartQuotedElements) > 0,
            'merchantsOrdersAmountNotEnough' => $merchantsOrdersAmountNotEnough,
            'risk' => $risk,
            'multiMerchants' => $multiMerchants
        ]);
    }

    /**
     * @Route("/cart/list", name="front.cart.list", methods="GET")
     * @Security("is_granted('ROLE_BUYER')")
     * @param CartService $cartService
     *
     * @return Response
     */
    public function cartListAction(CartService $cartService): Response
    {
        $buyer = $this->getUser();

        $carts = $cartService->loadBuyerCarts($buyer);

        return $this->render(
            '@OpenFront/cart/cart_list.html.twig',
            [
                'carts' => $carts,
                'user' => $buyer,
            ]
        );
    }

    /**
     * @Route("/cart/checkout", name="front.cart.checkout", methods="POST")
     * @Security("is_granted('ROLE_BUYER')")
     * @param Request $request
     * @param CartCheckoutService $cartCheckoutService
     * @param BuyerService $buyerService
     *
     * @return Response
     */
    public function cartCheckoutAction(Request $request, CartCheckoutService $cartCheckoutService, BuyerService $buyerService, CartService $cartService, TranslatorInterface $translator): Response
    {
        $buyer = $this->getUser();

        $form = $this->createFormBuilder(null, ['csrf_protection' => true])
            ->add('cartId', HiddenType::class, ['required' => true])
            ->add('checkoutCart', SubmitType::class)
            ->setMethod('POST')
            ->getForm();

        $form->handleRequest($request);
        if ($form->isSubmitted() && $form->isValid()) {
            $cartId = $form->get('cartId')->getData();

            $metaCart = $cartService->findCurrentBuyerMetaCart($buyer);
            $this->logger->info("Cart checkout:" . $cartId, EventNameEnum::CART_CHECKOUT, $buyer->getUsername(), []);

            $buyerManager = $buyerService->getBuyerSuperior($buyer);
            if ($buyerService->isValidationNeeded($buyer, $metaCart->getAmount())) {

                if ($buyerManager && $buyerManager->isEnabled()) {
                    // Only if you have a N+1 enabled

                    $metaCartCheckout = $cartCheckoutService->checkoutCart($buyer, $cartId);
                    $this->sendMailToManagerAndDelegate($buyerService, $cartService->buildCartFromMetaCart($metaCart), $buyer, $metaCartCheckout, $translator);

                    return $this->render(
                        '@OpenFront/cart/cart_pending_validation.html.twig',
                        [
                            'n1' => $metaCartCheckout->getManager(),
                            'user' => $buyer

                        ]
                    );
                }
            } else {
                $metaCartCheckout = $cartCheckoutService->checkoutCart($buyer, $cartId, $autoValidation = true);
                $acceptedCarts = $cartCheckoutService->acceptCart($buyerManager, $form->getData()['cartId']);
                $cart = $cartService->buildCartFromMetaCart($metaCart);
                $this->sendValidatedDirectlyMail($buyer, $cart, $metaCartCheckout);

                return $this->render('@OpenFront/cart/cart_direct_validation.html.twig', ['user' => $buyer]);

            }
        }

        return $this->render(
            '@OpenFront/cart/cart_checkout_error.html.twig',
            [
                'user' => $buyer,
            ]
        );
    }

    /**
     * @Route("/cart/empty", name="front.cart.empty", methods="POST")
     * @Security("is_granted('ROLE_BUYER')")
     * @param Request $request
     * @param CartService $cartService
     *
     * @return Response
     */
    public function emptyCartAction(Request $request, CartService $cartService): Response
    {
        $buyer = $this->getUser();

        $form = $this->createFormBuilder(null, ['csrf_protection' => true])
            ->add('cartId', HiddenType::class, ['required' => true])
            ->add('emptyCart', SubmitType::class)
            ->setMethod('POST')
            ->getForm();

        $form->handleRequest($request);
        if ($form->isSubmitted() && $form->isValid()) {
            $cartId = $form->get('cartId')->getData();
            $cart = $cartService->loadBuyerCart($buyer, $cartId);
            if ($cartService->emptyCart($buyer, $cart)) {
                return $this->redirectToRoute('cart.details');
            }

            return new Response('Forbidden', Response::HTTP_FORBIDDEN);
        }

        return new Response('Form Error', Response::HTTP_BAD_REQUEST);
    }

    /**
     * @Route("/cart/gift/enable", name="front.cart.gift-enable", methods="POST")
     * @Security("is_granted('ROLE_BUYER')")
     * @param Request $request
     * @param CartService $cartService
     *
     * @return Response
     */
    public function enableGiftAction(Request $request, CartService $cartService): Response
    {
        $buyer = $this->getUser();

        $form = $this->createFormBuilder(null, ['csrf_protection' => true])
            ->add('cartId', HiddenType::class, ['required' => true])
            ->add('enable', CheckboxType::class)
            ->setMethod('POST')
            ->getForm();

        $form->handleRequest($request);
        if ($form->isSubmitted() && $form->isValid()) {
            $cartId = $form->get('cartId')->getData();
            $enable = $form->get('enable')->getData();
            if ($cartService->enableGift($buyer, $cartId, $enable)) {
                return new Response('Succeed', Response::HTTP_OK);
            }

            return new Response('Forbidden', Response::HTTP_FORBIDDEN);
        }

        return new Response('Form Error', Response::HTTP_BAD_REQUEST);
    }

    /**
     * @Route("/cart/risk/enable", name="front.cart.risk-enable", methods="POST")
     * @Security("is_granted('ROLE_BUYER')")
     * @param Request $request
     * @param CartService $cartService
     *
     * @return Response
     */
    public function enableRiskAction(Request $request, CartService $cartService): Response
    {
        $buyer = $this->getUser();

        $form = $this->createFormBuilder(null, ['csrf_protection' => true])
            ->add('cartId', HiddenType::class, ['required' => true])
            ->add('enable', CheckboxType::class)
            ->setMethod('POST')
            ->getForm();

        $form->handleRequest($request);
        if ($form->isSubmitted() && $form->isValid()) {
            $cartId = $form->get('cartId')->getData();
            $enable = $form->get('enable')->getData();
            if ($cartService->enableRisk($buyer, $cartId, $enable)) {
                return new Response('Succeed', Response::HTTP_OK);
            }

            return new Response('Forbidden', Response::HTTP_FORBIDDEN);
        }

        return new Response('Form Error', Response::HTTP_BAD_REQUEST);
    }

    /**
     * @Route("/cart/item/gift/enable", name="front.cart.item-gift-enable", methods="POST")
     * @Security("is_granted('ROLE_BUYER')")
     * @param Request $request
     * @param CartService $cartService
     *
     * @return Response
     */
    public function enableItemGiftAction(Request $request, CartService $cartService): Response
    {
        $buyer = $this->getUser();

        $form = $this->createFormBuilder(null, ['csrf_protection' => true])
            ->add('cartId', HiddenType::class, ['required' => true])
            ->add('itemId', HiddenType::class, ['required' => true])
            ->add('enable', CheckboxType::class)
            ->setMethod('POST')
            ->getForm();

        $form->handleRequest($request);
        if ($form->isSubmitted() && $form->isValid()) {
            $cartId = $form->get('cartId')->getData();
            $itemId = $form->get('itemId')->getData();
            $enable = $form->get('enable')->getData();
            if ($cartService->enableCartItemGift($buyer, $cartId, $itemId, $enable)) {
                return new Response('Succeed', Response::HTTP_OK);
            }

            return new Response('Forbidden', Response::HTTP_FORBIDDEN);
        }

        return new Response('Form Error', Response::HTTP_BAD_REQUEST);
    }

    /**
     * @Route("/cart/merchant/comment", name="front.cart.merchant-comment", methods="POST")
     * @Security("is_granted('ROLE_BUYER')")
     * @param Request $request
     * @param CartService $cartService
     *
     * @return Response
     */
    public function writeMerchantCommentAction(Request $request, MetaCartMerchantService $metaCartMerchantService): Response
    {
        $buyer = $this->getUser();

        // todo validate the cart then a merchant order must be created with the comment
        // todo make sure merchant with mandatory comments got comments
        // todo récupérer placeholder

        $form = $this->createFormBuilder(null, ['csrf_protection' => true])
            ->add('cartId', HiddenType::class, ['required' => true])
            ->add('merchantId', HiddenType::class, ['required' => true])
            ->add('comment', HiddenType::class, ['required' => true])
            ->setMethod('POST')
            ->getForm();

        $form->handleRequest($request);
        if ($form->isSubmitted() && $form->isValid()) {
            $cartId = $form->get('cartId')->getData();
            $merchantId = $form->get('merchantId')->getData();
            $comment = $form->get('comment')->getData();
            if ($metaCartMerchantService->writeMerchantComment($buyer, $cartId, $merchantId, $comment)) {
                return new Response('Succeed', Response::HTTP_OK);
            }

            return new Response('Forbidden', Response::HTTP_FORBIDDEN);
        }

        return new Response('Form Error', Response::HTTP_BAD_REQUEST);
    }

    /**
     * @Route("/cart/offer/add", name="front.cart.offer.add", methods="POST")
     * @Security("is_granted('ROLE_BUYER')")
     * @param Request $request
     * @param OfferService $offerService
     * @param CartService $cartService
     *
     * @return Response
     */
    public function addOfferToCartAction(Request $request, OfferService $offerService, CartService $cartService, MerchantService $merchantService, TranslatorInterface $translator): Response
    {
        // is it an Ajax request?
        $isAjax = $request->isXmlHttpRequest();
        $locale = $request->getLocale();

        if (!$isAjax) {
            return new Response('bad request', Response::HTTP_BAD_REQUEST);
        }

        $response = ['amountIsNotEnough' => false, 'success' => true, 'error' => null, 'itemInCart' => 0];

        $buyer = $this->getUser();
        $offer = null;
        $form = $this->createFormBuilder(null, ['csrf_protection' => true])
            ->add('offerId', HiddenType::class, ['required' => true])
            ->add('quantity', IntegerType::class, ['required' => true, 'constraints' => new NotBlank()])
            ->add('addOfferToCart', SubmitType::class)
            ->setMethod('POST')
            ->getForm();
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid() && $buyer instanceof User) {
            $this->logger->info("buyer adding offer to cart", "ADD_OFFER_TO_CART", $buyer->getUsername(), ["offerId" => $form->getData()['offerId']]);
            try {
                $offer = $offerService->findOffer($form->getData()['offerId'], $locale, $buyer->getMarketPlace());

                if ($buyer->getMarketPlace()->getName() == "france") {
                    $offerMerchantBranches = $offer ? $offer->getMerchant()->getBranches() : [];
                    if (count($offerMerchantBranches) > 0 &&
                        !in_array(strtoupper($buyer->getBranch()), $offerMerchantBranches)
                    ) {
                        $response['success'] = false;
                        $response['error'] = $translator->trans('generic.unauthorized', [], 'AppBundle');
                        return new JsonResponse($response);
                    }
                }
                $cartService->loadCurrentBuyerCart($buyer);

                $numberOfItems = $cartService->addOfferToBuyerCurrentCart($offer, $buyer, $form->getData()['quantity']);
                $response['itemInCart'] = $numberOfItems;

                // refresh cart content and merchants items
                $cart = $cartService->loadCurrentBuyerCart($buyer);
                // set amount not enough response
                $offerMerchant = $cart->findMerchant($offer->getMerchant()->getId());
                $response['amountIsNotEnough'] = $offerMerchant && ($offerMerchant->getTotal() < $offerMerchant->getMinimumOrderAmount());

            } catch (ApiException $apiException) {
                $this->traceError($offer, $buyer, $merchantService, $apiException);
                if ($apiException->getIzbergCode() === 50101) {
                    $response['success'] = false;
                    $response['error'] = "L'offre n'a pas été ajoutée au panier car désormais elle n'est plus disponible";
                    return new JsonResponse($response, Response::HTTP_NOT_FOUND);
                }

                if ($apiException->getIzbergCode() === 50009) {
                    $response['success'] = false;
                    $response['error'] = $translator->trans('offer_detail.any_stock', [], 'AppBundle');
                    return new JsonResponse($response);
                }

                return new Response('generic izberg error', Response::HTTP_NOT_FOUND);

            } catch (Exception $exception) {
                $this->traceError($offer, $buyer, $merchantService, $exception);
                return new Response('generic izberg error', Response::HTTP_NOT_FOUND);
            }
        }

        return new JsonResponse($response);
    }

    /**
     * @Route("/cart/offer/add_array", name="front.cart.offer.add_array", methods="POST")
     * @Security("is_granted('ROLE_BUYER')")
     * @param Request $request
     * @param OfferService $offerService
     * @param CartService $cartService
     *
     * @return JsonResponse
     */
    public function addOffersToCartAction(Request $request, OfferService $offerService, CartService $cartService, MerchantService $merchantService): Response
    {
        // is it an Ajax request?
        $isAjax = $request->isXmlHttpRequest();
        $locale = $request->getLocale();

        if (!$isAjax) {
            return new JsonResponse([
                "success" => false,
                "msg" => 'bad request', Response::HTTP_BAD_REQUEST
            ]);
        }

        $response = ['amountIsNotEnough' => false, 'success' => true, 'error' => null, 'itemInCart' => 0];

        /** @var User $buyer */
        $buyer = $this->getUser();
        $offer = null;

        $offerIds = $request->request->get("offerIds");

        if (!is_array($offerIds)) {
            return new JsonResponse([
                "success" => false,
                "msg" => "bad request 'offerIds' array required", Response::HTTP_BAD_REQUEST
            ]);
        }

        $quantity = 1;
        $responses = [];
        foreach ($offerIds as $offerId) {
            $this->logger->info("buyer adding offer to cart", "ADD_OFFER_TO_CART", $buyer->getUsername(), ["offerId" => $offerId]);
            $response["offerId"] = $offerId;
            try {
                $offer = $offerService->findOffer($offerId, $locale, $buyer->getMarketPlace());

                if ($offer->isPriceGivenOnQuotation()) {
                    $response['success'] = false;
                    $response['error'] = "L'offre n'a pas été ajoutée au panier car désormais elle est par devis";
                    $responses[] = $response;
                    continue;
                }

                if ($buyer->getMarketPlace()->getName() == "france") {
                    $offerMerchantBranches = $offer->getMerchant()->getBranches();
                    if (count($offerMerchantBranches) > 0 &&
                        !in_array(strtoupper($buyer->getBranch()), $offerMerchantBranches)
                    ) {
                        $response['success'] = false;
                        $response['error'] = $this->get('translator')->trans('generic.unauthorized', [], 'AppBundle');
                        $responses[] = $response;
                        continue;
                    }
                }
                $cartService->loadCurrentBuyerCart($buyer);

                $numberOfItems = $cartService->addOfferToBuyerCurrentCart($offer, $buyer, $quantity);
                $response['itemInCart'] = $numberOfItems;

                // refresh cart content and merchants items
                $cart = $cartService->loadCurrentBuyerCart($buyer);
                // set amount not enough response
                $offerMerchant = $cart->findMerchant($offer->getMerchant()->getId());
                $response['amountIsNotEnough'] = $offerMerchant && ($offerMerchant->getTotal() < $offerMerchant->getMinimumOrderAmount());
                $responses[] = $response;
                continue;
            } catch (ApiException $apiException) {
                $this->traceError($offer, $buyer, $merchantService, $apiException);
                if ($apiException->getIzbergCode() === 50101) {
                    $response['success'] = false;
                    $response['error'] = "L'offre n'a pas été ajoutée au panier car désormais elle n'est plus disponible";
                    $responses[] = $response;
                    continue;
                }

                if ($apiException->getIzbergCode() === 50009) {
                    $response['success'] = false;
                    $response['error'] = $this->get('translator')->trans('offer_detail.any_stock', [], 'AppBundle');
                    $responses[] = $response;
                    continue;
                }
                $response['success'] = false;
                $response['error'] = 'generic izberg error';
                $responses[] = $response;
                continue;
            } catch (Exception $exception) {
                $this->traceError($offer, $buyer, $merchantService, $exception);
                $response["success"] = false;
                $response['error'] = 'generic izberg error';
                $responses[] = $response;
            }
        }

        return new JsonResponse($responses);
    }

    /**
     * @Route("/cart/item/remove", name="cart.details.remove_item", methods="DELETE")
     * @Security("is_granted('ROLE_BUYER')")
     */
    public function removeCartItemAction(Request $request, CartService $cartService): Response
    {
        $buyer = $this->getUser();

        // is it an Ajax request?
        $isAjax = $request->isXmlHttpRequest();

        if (!$isAjax) {
            return new Response('bad request', Response::HTTP_BAD_REQUEST);
        }

        $form = $this->createFormBuilder(null, ['csrf_protection' => true])
            ->add('cartId', IntegerType::class, ['required' => true, 'constraints' => new NotBlank()])
            ->add('itemId', IntegerType::class, ['required' => true, 'constraints' => new NotBlank()])
            ->setMethod('DELETE')
            ->getForm();

        $form->handleRequest($request);
        if ($form->isSubmitted() && $form->isValid()) {
            $cartId = $form->get('cartId')->getData();
            $itemId = $form->get('itemId')->getData();

            $cartService->removeItemFromCart($buyer, $cartId, $itemId);

            return new Response(sprintf('Cart item %d remove from current cart.', $itemId));
        }

        return new Response('Form Error', Response::HTTP_BAD_REQUEST);
    }

    /**
     * @Route("/cart/item/quantity/update", name="front.cart.addFromCart.withCartId", methods="POST")
     * @Security("is_granted('ROLE_BUYER')")
     * @param Request $request
     * @param CartService $cartService
     *
     * @return Response
     */
    public function updateCartItemQuantityAction(Request $request, CartService $cartService): Response
    {
        // is it an Ajax request?
        $isAjax = $request->isXmlHttpRequest();

        if (!$isAjax) {
            return new Response('bad request', Response::HTTP_BAD_REQUEST);
        }
        $form = $this->createFormBuilder(null, ['csrf_protection' => true])
            ->add('itemId', IntegerType::class, ['required' => true, 'constraints' => new NotBlank()])
            ->add('quantity', IntegerType::class, ['required' => true, 'constraints' => new NotBlank()])
            ->getForm();

        $form->handleRequest($request);
        if ($form->isSubmitted() && $form->isValid()) {
            $itemId = $form->get('itemId')->getData();
            $quantity = $form->get('quantity')->getData();

            $buyer = $this->getUser();
            $metaCart = $cartService->findCurrentBuyerMetaCart($buyer);

            $cartService->updateItemFromCart(
                $metaCart,
                $itemId,
                $quantity
            );

            return new Response(
                sprintf('Cart item %d update quantity to %d.',
                    $itemId,
                    $quantity
                ));
        }

        return new Response('Form Error', Response::HTTP_BAD_REQUEST);
    }

    /**
     * @Route("/cart/shipping", name="front.cart.shipping", methods="POST")
     * @Security("is_granted('ROLE_BUYER')")
     */
    public function selectCartShippingModeAction(Request $request, CartService $cartService, FormFactoryInterface $formFactory): Response
    {
        $isAjax = $request->isXmlHttpRequest();

        if (!$isAjax) {
            return new Response('bad request', Response::HTTP_BAD_REQUEST);
        }

        $buyer = $this->getUser();
        $cart = $cartService->loadCurrentBuyerCart($buyer);

        $form = $formFactory->createNamedBuilder('', FormType::class, null, ['csrf_token_id' => 'cart_shipping'])
            ->add('merchantId', HiddenType::class, ['required' => true])
            ->add('shippingMode', TextType::class, ['required' => true, 'constraints' => new NotBlank()])
            ->setMethod('POST')
            ->getForm();

        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $merchantId = $form->getData()['merchantId'];
            $shippingMode = $form->getData()['shippingMode'];
            if ($cartService->changeCartShippingModeForMerchantId($cart, $shippingMode, $merchantId)) {
                return new Response(sprintf('Shipping mode: %s, selected for merchant %d', $shippingMode, $merchantId));
            }
        }

        return new Response('Form Error', Response::HTTP_BAD_REQUEST);
    }

    /**
     * @Route("/cart/reject", name="cart.details.reject", methods="POST")
     * @param Request $request
     * @param CartCheckoutService $cartCheckoutService
     * @param FormFactoryInterface $formFactory
     * @param MetaCartRepository $metaCartRepository
     * @param BuyerService $buyerService
     *
     * @return Response
     */
    public function rejectCartAction(Request $request, CartCheckoutService $cartCheckoutService, FormFactoryInterface $formFactory, MetaCartRepository $metaCartRepository, MailService $emailService): Response
    {
        /** @var User $manager */
        $manager = $this->getUser();

        $form = $formFactory->createNamedBuilder('', FormType::class, null, ['csrf_token_id' => 'cart_reject'])
            ->add('cartId', HiddenType::class, ['required' => true])
            ->add('reason', TextType::class, ['required' => true, 'constraints' => new NotBlank()])
            ->add('rejectCart', SubmitType::class)
            ->setMethod('POST')
            ->getForm();

        $form->handleRequest($request);
        if ($form->isSubmitted() && $form->isValid()) {
            if ($cartCheckoutService->rejectCart($manager, $form->getData()['cartId'], $form->getData()['reason'])) {
                /** @var int */
                $cartId = (int)$form->getData()['cartId'];

                /** @var MetaCart $metaCart */
                $metaCart = $metaCartRepository->findOneBy(['id' => $cartId]);
                $buyer = $metaCart->getBuyer();

                $manager = $this->getUser();

                if ($manager instanceof User) {


                    $emailService->sendEmailMessage(
                        MailService::CART_REJECTED_TO_BUYER,
                        $buyer->getLocale(),
                        $buyer->getEmail(),
                        [
                            'firstName' => $buyer->getFirstname(),
                            'lastName' => $buyer->getLastname(),
                            'firstNameValidator' => $manager->getFirstname(),
                            'lastNameValidator' => $manager->getLastname(),
                            'cartNumber' => $cartId,
                            'reason' => $form->getData()['reason'],
                        ]
                    );
                }

                // commented because waiting for business decision https://jira.open-groupe.com/browse/TOTALMP-928
                //$this->sendToDelegates($buyer, $buyerService, $metaCart, $emailService, $cartId, MailService::CART_REJECTED_TO_MANAGER_OR_DELEGATE);

                return new Response(sprintf('cart %d has been rejected by %s', $form->getData()['cartId'], $manager->getUsername()));
            }
            // Check if metacart have been validated by other autorized manager or delegate
            $rejectedByManagerOrDelegate = $cartCheckoutService->hasCartRejectedByDelegate($manager, $form->getData()['cartId']);

            if ($rejectedByManagerOrDelegate) {
                return new Response(sprintf('cart %d has always been rejected', $form->getData()['cartId']));
            }

            return new Response('Forbidden', Response::HTTP_FORBIDDEN);
        }

        return new Response('form error', Response::HTTP_BAD_REQUEST);
    }

    /**
     * @Route("/cart/cancel", name="cart.details.cancel", methods="POST")
     * @Security("is_granted('ROLE_BUYER')")
     * @throws ApiException
     */
    public function orderCancelAction(
        Request $request, CartCheckoutService $cartCheckoutService, FormFactoryInterface $formFactory, MerchantOrderRepository $merchantOrderRepository): Response
    {

        /** @var User $user */
        $user = $this->getUser();

        $form = $formFactory->createNamedBuilder('', FormType::class, null, ['csrf_token_id' => 'cart_cancel'])
            ->add('cartId', HiddenType::class, ['required' => true])
            ->add('orderId', HiddenType::class, ['required' => false])
            ->add('reason', TextType::class, ['required' => true, 'constraints' => new NotBlank()])
            ->add('merchantOrderStatus', hiddenType::class, ['required' => true])
            ->add('rejectCart', SubmitType::class)
            ->setMethod('POST')
            ->getForm();

        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $orderId = $form->getData()['orderId'];
            $cartId = $form->getData()['cartId'];

            /** @var MerchantOrder $merchantOrder * */
            $merchantOrder = $merchantOrderRepository->find($orderId);

            if ($form->getData()['merchantOrderStatus'] != $merchantOrder->getStatus()) {
                //status has changed before user action, (manager validation or supplier validation)
                return new JsonResponse(sprintf('cart action conflict'), Response::HTTP_CONFLICT);
            }

            if ($merchantOrder->getStatus() == MerchantOrder::STATUS_PENDING_MANAGER_VALIDATION) {
                if ($cartCheckoutService->cancelCart($user, $cartId, $form->getData()['reason'])) {
                    return new JsonResponse(sprintf('cart %d has been cancelled', $cartId));
                }
            } else {
                if ($merchantOrder->getStatus() == MerchantOrder::STATUS_PENDING_SUPPLIER_VALIDATION) {
                    if ($cartCheckoutService->cancelOrder($user, $cartId, $merchantOrder, $form->getData()['reason'])) {
                        return new JsonResponse(sprintf('order %d has been cancelled', $merchantOrder->getId()));
                    }
                }
            }

        }

        return new JsonResponse('form error', Response::HTTP_BAD_REQUEST);
    }

    /**
     * @Route("/cart/accept", name="cart.details.accept", methods="POST")
     */
    public function acceptCartAction(
        Request              $request,
        CartCheckoutService  $cartCheckoutService,
        FormFactoryInterface $formFactory,
        MetaCartRepository   $metaCartRepository,
        MailService          $emailService
    ): Response
    {
        /** @var User $manager */
        $manager = $this->getUser();
        $form = $formFactory->createNamedBuilder('', FormType::class, null, ['csrf_token_id' => 'cart_accept'])
            ->add('cartId', HiddenType::class, ['required' => true])
            ->add('acceptCart', SubmitType::class)
            ->setMethod('POST')
            ->getForm();

        $form->handleRequest($request);
        if ($form->isSubmitted() && $form->isValid()) {
            $acceptedCarts = $cartCheckoutService->acceptCart($manager, $form->getData()['cartId']);

            if ($acceptedCarts) {
                $cartId = (int)$form->getData()['cartId'];

                /** @var MetaCart $metaCart */
                $metaCart = $metaCartRepository->findOneBy(['id' => $cartId]);
                $buyer = $metaCart->getBuyer();

                $emailService->sendEmailMessage(
                    MailService::CART_CONFIRMED_TO_BUYER,
                    $buyer->getLocale(),
                    $buyer->getEmail(),
                    [
                        'firstName' => $buyer->getFirstname(),
                        'lastName' => $buyer->getLastname(),
                        'firstNameValidator' => $manager->getFirstname(),
                        'lastNameValidator' => $manager->getLastname(),
                        'cartNumber' => $cartId
                    ]
                );

                // commented because waiting for business decision https://jira.open-groupe.com/browse/TOTALMP-928
                //$this->sendToDelegates($buyer, $buyerService, $metaCart, $emailService, $cartId, MailService::CART_CONFIRMED_TO_MANAGER_OR_DELEGATE);

                return new Response(sprintf('cart %d has been accepted by %s', $form->getData()['cartId'], $manager->getUsername()));
            }

            // Check if metacart have been validated by other autorized manager or delegate
            $acceptedByManagerOrDelegate = $cartCheckoutService->hasCartAcceptedByDelegate($manager, $form->getData()['cartId']);

            if ($acceptedByManagerOrDelegate) {
                return new Response(sprintf('cart %d has always been accepted', $form->getData()['cartId']));
            }

            return new Response('Forbidden', Response::HTTP_FORBIDDEN);
        }

        return new Response('form error', Response::HTTP_BAD_REQUEST);
    }

    /**
     * @Route("/cart/shipping-address/update", name="front.cart.shippingAddress.update", methods="POST")
     * @Security("is_granted('ROLE_BUYER')")
     */
    public function updateShippingAddressAction(
        Request        $request,
        CartService    $cartService,
        AddressService $addressService,
        CountryService $countryService,
        BuyerService   $buyerService
    ): Response
    {
        // is it an Ajax request?
        $isAjax = $request->isXmlHttpRequest();
        if (!$isAjax) {
            return new Response('bad request', Response::HTTP_BAD_REQUEST);
        }

        $form = $this->createFormBuilder(null, ['csrf_protection' => true, 'csrf_token_id' => 'cart_shipping_address_form'])
            ->add('technical_id', TextType::class, ['constraints' => new Length(['max' => 255])])
            ->add('type', TextType::class, ['constraints' => new Length(['max' => 10])])
            ->add('name', TextType::class, ['constraints' => new Length(['max' => 50])])
            ->add('address', TextType::class, ['constraints' => new Length(['max' => 100])])
            ->add('address2', TextType::class, ['constraints' => new Length(['max' => 100])])
            ->add('zipcode', TextType::class, ['constraints' => new Length(['max' => 10])])
            ->add('city', TextType::class, ['constraints' => new Length(['max' => 50])])
            ->add('country_code', TextType::class, ['constraints' => [new Length(['max' => 5]), new NotBlank()]])
            ->add('contact', TextType::class, ['constraints' => [new Length(['max' => 255]), new NotBlank()]])
            ->add('phone', TextType::class, ['constraints' => new Length(['max' => 50])])
            ->add('comment', TextType::class, ['constraints' => new Length(['max' => 255])])
            ->getForm();

        $form->handleRequest($request);
        if ($form->isSubmitted() && $form->isValid()) {
            /** @var BuyerAddressModel */
            $data = $form->getData();
            if (is_array($data)) {


                /** @var \AppBundle\Entity\Country $country */
                $country = $countryService->getCountryByIzbergCode($data['country_code']);

                $buyerAddressModel = new BuyerAddressModel();

                $buyerAddressModel->setName($data['name']);
                $buyerAddressModel->setAddress($data['address']);
                $buyerAddressModel->setAddress2($data['address2']);
                $buyerAddressModel->setZipCode($data['zipcode']);
                $buyerAddressModel->setCity($data['city']);

                $buyerAddressModel->setCountry($country);

                $buyerAddressModel->setContact($data['contact']);
                $buyerAddressModel->setPhone($data['phone']);
                $buyerAddressModel->setComment($data['comment']);

                if ($data['technical_id']) {
                    $addressService->setTotalBuyerAddressInactive($this->getUser(), $data['technical_id']);
                }
                $buyerAddress = $addressService->getBuyerAddressAndCreateItIfNotExists(
                    $this->getUser(),
                    $data['type'],
                    $buyerAddressModel
                );
                $buyerService->buyerSelectAdressInCart($this->getUser());

                if ($buyerAddress && $cartService->updateBuyerShippingAddress($this->getUser(), $buyerAddress)) {
                    $country = $buyerAddress->getCountry();

                    $item = ['data' => [
                        'technical_id' => $buyerAddress->getTechnicalId(),
                        'type' => $buyerAddress->getType(),

                        'name' => $buyerAddress->getName(),
                        'address' => $buyerAddress->getAddress(),
                        'address2' => $buyerAddress->getAddress2(),
                        'city' => $buyerAddress->getCity(),
                        'zipcode' => $buyerAddress->getZipCode(),
                        'country' => $country ? $country->getCode() : '',
                        'country_code' => $country ? $country->getIzbergCode() : '',

                        'recipient' => $buyerAddress->getContact(),
                        'recipient_phone' => $buyerAddress->getPhone(),
                        'recipient_comment' => $buyerAddress->getComment()
                    ]];

                    return new JsonResponse($item, Response::HTTP_ACCEPTED);
                }
            }

            return new Response('error', Response::HTTP_NOT_FOUND);
        }
        return new Response('form error', Response::HTTP_BAD_REQUEST);
    }

    private function sendToDelegates(User $buyer, BuyerService $buyerService, MetaCart $metaCart, $emailService, int $cartId, string $mailTemplate): void
    {
        // for metaCart, validated by $validator (manager or delegate)
        // We notify by email all $UsersToNotify (delegates and manager)
        // of validation for account of $orderManager (manager)

        $orderManager = $buyerService->getBuyerSuperior($metaCart->getBuyer());

        $buyerDelegates = [];
        if ($orderManager) {
            $buyerDelegates = $buyerService->getBuyerDelegates($orderManager);
        }

        if ($orderManager && !empty($buyerDelegates)) {
            // Notified only if manager have some delegate

            $UsersToNotify = array_merge(
                [$orderManager],
                $buyerDelegates
            );

            /** @var User $user */
            foreach ($UsersToNotify as $user) {
                $emailService->sendEmailMessage(
                    $mailTemplate,
                    $user->getLocale(),
                    $user->getEmail(),
                    [
                        'firstName' => $user->getFirstname(),
                        'lastName' => $user->getLastname(),
                        'firstNameBuyer' => $buyer->getFirstname(),
                        'lastNameBuyer' => $buyer->getLastname(),
                        'cartNumber' => $cartId,
                        'orderUrl' => $this->generateUrl(
                            'front.subordinatesOrder.detail',
                            ['orderId' => $metaCart->getId()],
                            UrlGeneratorInterface::ABSOLUTE_URL
                        ),
                    ]
                );
            }
        }
    }

    /**
     *  Check that the order is not yet validated
     *
     * @param integer $orderId
     *
     * @return boolean
     */
    private function checkValidatedByMerchant($orderId)
    {

        $merchantOrderRepository = $this->getDoctrine()->getRepository(MerchantOrder::class);
        $merchantOrder = $merchantOrderRepository->find($orderId);
        if ($merchantOrder->getStatus() == MerchantOrder::STATUS_CONFIRMED_BY_SUPPLIER) {
            return true;
        }
        return false;
    }

    private function getOrders(MetaCartCheckout $metaCartCheckout)
    {
        $merchantOrders = $metaCartCheckout
            ->getMetaCart()
            ->getMerchantOrders()
            ->toArray();

        $orders = array_reduce(
            $merchantOrders,
            static function (?string $orderList, MerchantOrder $merchantOrder) {
                return (empty($orderList) ? '' : $orderList . ', ') . $merchantOrder->getId();
            }
        );
        return $orders;
    }

    private function getOrderItems(MetaCartCheckout $metaCartCheckout)
    {
        $merchantOrders = $metaCartCheckout
            ->getMetaCart()
            ->getMerchantOrders()
            ->toArray();
        $orderItems = array_map(
            static function (MerchantOrder $merchantOrder) {
                return ['orderNumber' => $merchantOrder->getId()];
            },
            $merchantOrders
        );
        return $orderItems;
    }

    private function sendValidatedDirectlyMail(User $buyer, Cart $cart, MetaCartCheckout $metaCartCheckout)
    {
        if ($metaCartCheckout->getState() === MetaCartCheckout::STATE_FINISHED) {
            $this->mailService->sendEmailMessage(
                MailService::CART_CONFIRMED_DIRECTLY_TO_BUYER,
                $buyer->getLocale(),
                $buyer->getEmail(),
                [
                    'firstName' => $buyer->getFirstname(),
                    'lastName' => $buyer->getLastname(),
                    'cartNumber' => $metaCartCheckout->getMetaCart()->getId(),
                    'items' => array_reduce(
                        $cart->getMerchants(),
                        static function (array $items, CartMerchant $cartMerchant) {
                            $merchantItems = array_map(
                                function (CartItem $cartItem) use ($cartMerchant) {
                                    return (new \AppBundle\Model\Mail\CartItem())
                                        ->setName($cartItem->getName())
                                        ->setQuantity($cartItem->getQuantity())
                                        ->setUnitPrice($cartItem->getUnitPrice())
                                        ->setCurrency($cartItem->getCurrency())
                                        ->setMerchantName($cartMerchant->getName())
                                        ->setTotalPrice($cartItem->getQuantity() * $cartItem->getUnitPrice());
                                },
                                $cartMerchant->getItems()
                            );

                            return array_merge($items, $merchantItems);
                        },
                        []
                    )
                ]
            );
        }
    }


    private function sendMailToManagerAndDelegate(BuyerService $buyerService, Cart $cart, User $buyer, MetaCartCheckout $metaCartCheckout, TranslatorInterface $translator)
    {

        if ($metaCartCheckout->getState() === MetaCartCheckout::STATE_FINISHED) {
            $user = $this->getUser();

            if ($user instanceof User) {
                $manager = $buyerService->getBuyerSuperior($user);

                $this->mailService->sendEmailMessage(
                    MailService::CART_CHECKOUT_CONFIRMATION_TO_BUYER,
                    $user->getLocale(),
                    $user->getEmail(),
                    [
                        'firstName' => $user->getFirstname(),
                        'lastName' => $user->getLastname(),
                        'cartNumber' => $metaCartCheckout->getMetaCart()->getId(),
                        'ordersNumber' => $this->getOrders($metaCartCheckout),
                        'orderItems' => $this->getOrderItems($metaCartCheckout)
                    ]
                );

                if ($manager) {
                    //$cart = $cartService->loadBuyerCart($buyer, $metaCartCheckout->getMetaCart()->getId());

                    $usersToNotify = $buyerService->getBuyerDelegates($manager);

                    if (!count($usersToNotify)) {
                        $usersToNotify = [$manager];
                    }


                    /** @var User $managerOrDelegate */
                    foreach ($usersToNotify as $managerOrDelegate) {
                        if (!($managerOrDelegate instanceof User)) {
                            continue;
                        }
                        $this->mailService->sendEmailMessage(
                            MailService::CART_PENDING_VALIDATION_TO_MANAGER_OR_DELEGATE,
                            $managerOrDelegate->getLocale(),
                            $managerOrDelegate->getEmail(),
                            [
                                'firstName' => $managerOrDelegate->getFirstname(),
                                'lastName' => $managerOrDelegate->getLastname(),
                                'firstNameBuyer' => $user->getFirstname(),
                                'lastNameBuyer' => $user->getLastname(),
                                'cartNumber' => $metaCartCheckout->getMetaCart()->getId(),
                                'validationUrl' => $this->generateUrl(
                                    'front.subordinatesOrder.detail',
                                    ['orderId' => $metaCartCheckout->getMetaCart()->getId()],
                                    UrlGeneratorInterface::ABSOLUTE_URL
                                ),
                                'shippingAmountHt' => $cart->getTotal() - $cart->getTotalWithoutDelivery(),
                                'msgIfRiskCart' => $cart->isRisk() ? $translator->trans('generic.mailRiskCartMsg', [], 'AppBundle', $managerOrDelegate->getLocale()) : "",
                                'items' => array_reduce(
                                    $cart->getMerchants(),
                                    static function (array $items, CartMerchant $cartMerchant) {
                                        $merchantItems = array_map(
                                            function (CartItem $cartItem) use ($cartMerchant) {
                                                return (new \AppBundle\Model\Mail\CartItem())
                                                    ->setName($cartItem->getName())
                                                    ->setQuantity($cartItem->getQuantity())
                                                    ->setUnitPrice($cartItem->getUnitPrice())
                                                    ->setCurrency($cartItem->getCurrency())
                                                    ->setMerchantName($cartMerchant->getName())
                                                    ->setTotalPrice($cartItem->getQuantity() * $cartItem->getUnitPrice());
                                            },
                                            $cartMerchant->getItems()
                                        );

                                        return array_merge($items, $merchantItems);
                                    },
                                    []
                                )
                            ]
                        );
                    }
                }
            }


        }

    }

    private function traceError(?Offer $offer, ?User $buyer, MerchantService $merchantService, Exception $exception)
    {
        $offerId = null;
        $merchantIzbId = null;
        $merchantId = null;
        $merchantCountryOfDelivery = null;
        if (!is_null($offer)) {
            $offerId = $offer->getId();
            $merchantM = $offer->getMerchant();
            $merchantIzbId = $merchantM->getId();
            $merchant = $merchantService->findMerchantEntityByIzbergId($merchantIzbId);
            if (!is_null($merchant)) {
                $merchantId = $merchant->getId();
                if (!is_null($merchant->getCountry())) {
                    $merchantCountryOfDelivery = $merchant->getCountry()->getId();
                }
            }
        }
        $userCountryOfDelivery = null;
        if (!is_null($buyer->getCountryOfDelivery())) {
            $userCountryOfDelivery = $buyer->getCountryOfDelivery()->getId();
        }
        $this->logger->info("problem adding to cart", "ADD_OFFER_TO_CART", $buyer->getUsername(),
            [
                "offer" => $offerId,
                "merchant_izb_id" => $merchantIzbId,
                "merchant_id" => $merchantId,
                "merchant_country_of_delivery" => $merchantCountryOfDelivery,
                "User_country_of_delivery" => $userCountryOfDelivery,
                "message" => $exception->getMessage(),
                "exception" => $exception->getTraceAsString()
            ]);
    }
}
