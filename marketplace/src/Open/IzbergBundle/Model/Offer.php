<?php
/**
 * Created by PhpStorm.
 * User: PCH07650
 * Date: 16/05/2018
 * Time: 17:09
 */

namespace Open\IzbergBundle\Model;

use Symfony\Component\Validator\Constraints as Assert;
use JMS\Serializer\Annotation\Type;
use <PERSON><PERSON>\Serializer\Annotation as JSM;

/**
 * Entity to map an offer object
 * @JSM\ExclusionPolicy("none")
 */
class Offer
{
    /**
     * @Type("integer")
     * @Assert\NotBlank()
     */
    private $id = 0;

    /**
     * @return mixed
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * @param mixed $id
     */
    public function setId($id): void
    {
        $this->id = $id;
    }
}