<?php

namespace AppBundle\Twig;

use Twig\Extension\AbstractExtension;
use Twig\TwigFunction;

class RandomLengthExtension extends AbstractExtension
{
    public function getFunctions()
    {
        return array(
            new TwigFunction('randomLength', array($this, 'randomLength')),
        );
    }

    public function randomLength()
    {
        $random = $this->generateRandomString(50);
        return substr(
                base64_encode($random),
                0,
                ord($random[24]) % 32
            );
    }

    private function generateRandomString($length = 10) {
        $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $charactersLength = strlen($characters);
        $randomString = '';
        for ($i = 0; $i < $length; $i++) {
            $randomString .= $characters[rand(0, $charactersLength - 1)];
        }
        return $randomString;
    }

    public function getName()
    {
        return 'random_length.extension';
    }
}