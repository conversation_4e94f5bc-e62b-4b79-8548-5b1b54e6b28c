<?php

namespace Open\BackBundle\Events;


use Symfony\Contracts\EventDispatcher\Event;

class UpdateSettingsEvent extends Event
{

    /**
     * @Event("Open\BackBundle\Events\UpdateSettingsEvent")
     */
    public const EVENT_NAME = "UPDATE_SETTINGS_EVENT";

    private string $domain;
    private string $group;


    public function getGroup(): string
    {
        return $this->group;
    }

    public function setGroup($group): void
    {
        $this->group = $group;
    }

    public function getDomain(): string
    {
        return $this->domain;
    }

    public function setDomain(string $domain): void
    {
        $this->domain = $domain;
    }
}