<?php

namespace Open\FrontBundle\Service;

use AppBundle\Entity\User;
use AppBundle\Model\Search\Filter\SearchFilterQuery;
use AppBundle\Services\IzbergCustomAttributes;
use AppBundle\Services\SearchService;

class SuggestionService
{
    public const CUSTOM_ANALYZER = "custom_analyzer_with_min";
    private SearchService $searchService;
    private ?User $user = null;
    private IzbergCustomAttributes $izbergCustomAttributes;
    private array $suggestionFields;

    public function __construct(SearchService $searchService, IzbergCustomAttributes $izbergCustomAttributes, array $suggestionFields)
    {
        $this->searchService = $searchService;
        $this->izbergCustomAttributes = $izbergCustomAttributes;
        $this->suggestionFields = $suggestionFields;
    }

    public function setUser(?User $user): self
    {
        $this->user = $user;

        if ($user) {
            $this->searchService->setBranch($user->getBranch());
        }

        return $this;
    }

    public function findSuggestions(
        ?string $query,
        string  $locale = 'FR'

    ):array {

        $result = $this->findSuggestionsByCategory($query, $locale);
        $suggestions = [];
        foreach ($result->getCategories() as $suggestionCategory) {


            foreach ($suggestionCategory->getSuggestionResults() as $suggestionResult){

                if($suggestionCategory->getCategoryName()===SuggestionCategory::TYPE_PRODUCT){
                    if($this->isMatchedCriteria([], $suggestionResult->getTerm(), $query)) {
                        $suggestions[] = $suggestionResult->getTerm();
                    }
                }else{
                    $suggestions [] = $suggestionResult->getTerm();
                }


            }
        }
        return $suggestions;
    }

    private function findSuggestionsByCategory(
        ?string $query,
        string  $locale = 'FR'

    ): SuggestionSearchResult
    {
        $buyer = $this->user;
        $params = [
            'index' => sprintf('%s_%s', $buyer->getMarketPlace()->getElasticSearchIndex(), strtolower($locale) ?: '*'),
            'type' => '_doc'
        ];

        $filterQuery = new SearchFilterQuery();

        $this->searchService->enableActiveOfferFilters($filterQuery);
        $this->searchService->enableCountryOfDeliveryFilter($filterQuery, $buyer->getCountryOfDelivery());


        if (!is_null($this->user)) {
            $this->searchService->addMustBeEqualsIfExist($filterQuery, 'merchant.branches.keyword', $this->user->getBranch());
            $this->searchService->enableFilterEquipment($filterQuery, $this->user);
        }

        $this->searchService->disableSecondaryQuoteOffers($filterQuery);

        $offersSearch = $this->buildSuggestionQuery($query, $this->suggestionFields);
        $offersSearch['query']['bool']['filter'] = $filterQuery->query();
        //always normal offer before Quote offer, and origin Quote offer before primary  (made by the marketplace).
        $offersSearch["sort"] = [
            'attributes.' . $this->izbergCustomAttributes->getPriceOnQuotation() . '.keyword' => ["missing" => "No", "order" => "asc"],
            'attributes.' . $this->izbergCustomAttributes->getQuoteSecondaryOffer() . '.keyword' => ["missing" => "AA", "order" => "asc", "unmapped_type" => "string"],
        ];
        $offersSearch["size"] = 20;

        $merchantFilterQuery = new SearchFilterQuery();
        $this->searchService->enableActiveOfferFilters($merchantFilterQuery);
        $merchantSearch = [];
        $merchantSearch['query']['bool']['filter'] = $merchantFilterQuery->query();
        $aggs = [];
        $aggs['merchant'] = $this->buildSuggestionAggregation(
            "merchant.name.text",
            "merchant.id",
            "merchant.name",
            $query
        );
        $merchantSearch["aggs"] = $aggs;

        $brandSearch = [];
        $aggs_b = [];
        $aggs_b['brand'] = $this->buildSuggestionAggregation(
            "product.brand.name.text",
            "product.brand.id",
            "product.brand.name.keyword",
            $query
        );
        $brandSearch["aggs"] = $aggs_b;

        $body = [
            "body" =>
                [$params, $offersSearch, $params, $merchantSearch, $params, $brandSearch]
        ];
        $result = $this->searchService->findSuggestions($body);



        $categories = SuggestionMapper::elasticToDomain(
            $result["responses"][0]["hits"]["hits"],
            $query,
            strtolower($locale)
        );

        $categories[] = SuggestionMapper::elasticAggToDomain(
            $result["responses"][1]["aggregations"]["merchant"],
            SuggestionCategory::TYPE_MERCHANT,
            "merchant.id",
            "merchant.name"
        );
        $categories[] = SuggestionMapper::elasticAggToDomain(
            $result["responses"][2]["aggregations"]["brand"],
            SuggestionCategory::TYPE_BRAND,
            "product.brand.id",
            "product.brand.name"
        );

        $searchResult = new SuggestionSearchResult();
        $searchResult->setCategories($categories);
        return $searchResult;
    }

    private function buildSuggestionQuery(?string $query, array $fields): array
    {
        $search = [];
        $search['query']['bool'] = [
            "must" => [
                'simple_query_string' => [
                    'analyzer' => self::CUSTOM_ANALYZER,
                    'default_operator' => 'and',
                    'fields' => $fields,
                    'query' => $query
                ]
            ]
        ];
        return $search;
    }

    private function buildSuggestionAggregation(
        string  $filterField,
        string  $idAggregField,
        string  $nameAggregField,
        ?string $term
    ): array
    {
        $returned =
            [
                "filter" => [
                    "match" => [
                        $filterField => [
                            "query" => $term,
                            "analyzer" => "standard"
                        ]
                    ]
                ],
                "aggs" => [
                    $idAggregField => [
                        "terms" => [
                            "field" => $idAggregField,
                            "min_doc_count" => 1,
                            "size" => 10
                        ],
                        "aggs" => [
                            $nameAggregField => [
                                "terms" => [
                                    "field" => $nameAggregField,
                                    "min_doc_count" => 1,
                                    "size" => 10
                                ],

                            ]
                        ]
                    ]
                ]
            ];
        return $returned;
    }

    private function isMatchedCriteria(array $suggestions_unaccent, ?string $criteria, string $query): bool
    {
        $criteria = $this->trimTolowerRemoveAccent($criteria);
        $query = $this->trimTolowerRemoveAccent($query);

        return (
            !empty($criteria) &&
            str_contains($criteria, $query) &&
            !in_array($criteria, $suggestions_unaccent)
        );
    }

    private function removeAccents(string $str): string
    {
        $a = array('À', 'Á', 'Â', 'Ã', 'Ä', 'Å', 'Æ', 'Ç', 'È', 'É', 'Ê', 'Ë', 'Ì', 'Í', 'Î', 'Ï', 'Ð', 'Ñ', 'Ò', 'Ó', 'Ô', 'Õ', 'Ö', 'Ø', 'Ù', 'Ú', 'Û', 'Ü', 'Ý', 'ß', 'à', 'á', 'â', 'ã', 'ä', 'å', 'æ', 'ç', 'è', 'é', 'ê', 'ë', 'ì', 'í', 'î', 'ï', 'ñ', 'ò', 'ó', 'ô', 'õ', 'ö', 'ø', 'ù', 'ú', 'û', 'ü', 'ý', 'ÿ', 'Ā', 'ā', 'Ă', 'ă', 'Ą', 'ą', 'Ć', 'ć', 'Ĉ', 'ĉ', 'Ċ', 'ċ', 'Č', 'č', 'Ď', 'ď', 'Đ', 'đ', 'Ē', 'ē', 'Ĕ', 'ĕ', 'Ė', 'ė', 'Ę', 'ę', 'Ě', 'ě', 'Ĝ', 'ĝ', 'Ğ', 'ğ', 'Ġ', 'ġ', 'Ģ', 'ģ', 'Ĥ', 'ĥ', 'Ħ', 'ħ', 'Ĩ', 'ĩ', 'Ī', 'ī', 'Ĭ', 'ĭ', 'Į', 'į', 'İ', 'ı', 'Ĳ', 'ĳ', 'Ĵ', 'ĵ', 'Ķ', 'ķ', 'Ĺ', 'ĺ', 'Ļ', 'ļ', 'Ľ', 'ľ', 'Ŀ', 'ŀ', 'Ł', 'ł', 'Ń', 'ń', 'Ņ', 'ņ', 'Ň', 'ň', 'ŉ', 'Ō', 'ō', 'Ŏ', 'ŏ', 'Ő', 'ő', 'Œ', 'œ', 'Ŕ', 'ŕ', 'Ŗ', 'ŗ', 'Ř', 'ř', 'Ś', 'ś', 'Ŝ', 'ŝ', 'Ş', 'ş', 'Š', 'š', 'Ţ', 'ţ', 'Ť', 'ť', 'Ŧ', 'ŧ', 'Ũ', 'ũ', 'Ū', 'ū', 'Ŭ', 'ŭ', 'Ů', 'ů', 'Ű', 'ű', 'Ų', 'ų', 'Ŵ', 'ŵ', 'Ŷ', 'ŷ', 'Ÿ', 'Ź', 'ź', 'Ż', 'ż', 'Ž', 'ž', 'ſ', 'ƒ', 'Ơ', 'ơ', 'Ư', 'ư', 'Ǎ', 'ǎ', 'Ǐ', 'ǐ', 'Ǒ', 'ǒ', 'Ǔ', 'ǔ', 'Ǖ', 'ǖ', 'Ǘ', 'ǘ', 'Ǚ', 'ǚ', 'Ǜ', 'ǜ', 'Ǻ', 'ǻ', 'Ǽ', 'ǽ', 'Ǿ', 'ǿ', 'Ά', 'ά', 'Έ', 'έ', 'Ό', 'ό', 'Ώ', 'ώ', 'Ί', 'ί', 'ϊ', 'ΐ', 'Ύ', 'ύ', 'ϋ', 'ΰ', 'Ή', 'ή');
        $b = array('A', 'A', 'A', 'A', 'A', 'A', 'AE', 'C', 'E', 'E', 'E', 'E', 'I', 'I', 'I', 'I', 'D', 'N', 'O', 'O', 'O', 'O', 'O', 'O', 'U', 'U', 'U', 'U', 'Y', 's', 'a', 'a', 'a', 'a', 'a', 'a', 'ae', 'c', 'e', 'e', 'e', 'e', 'i', 'i', 'i', 'i', 'n', 'o', 'o', 'o', 'o', 'o', 'o', 'u', 'u', 'u', 'u', 'y', 'y', 'A', 'a', 'A', 'a', 'A', 'a', 'C', 'c', 'C', 'c', 'C', 'c', 'C', 'c', 'D', 'd', 'D', 'd', 'E', 'e', 'E', 'e', 'E', 'e', 'E', 'e', 'E', 'e', 'G', 'g', 'G', 'g', 'G', 'g', 'G', 'g', 'H', 'h', 'H', 'h', 'I', 'i', 'I', 'i', 'I', 'i', 'I', 'i', 'I', 'i', 'IJ', 'ij', 'J', 'j', 'K', 'k', 'L', 'l', 'L', 'l', 'L', 'l', 'L', 'l', 'l', 'l', 'N', 'n', 'N', 'n', 'N', 'n', 'n', 'O', 'o', 'O', 'o', 'O', 'o', 'OE', 'oe', 'R', 'r', 'R', 'r', 'R', 'r', 'S', 's', 'S', 's', 'S', 's', 'S', 's', 'T', 't', 'T', 't', 'T', 't', 'U', 'u', 'U', 'u', 'U', 'u', 'U', 'u', 'U', 'u', 'U', 'u', 'W', 'w', 'Y', 'y', 'Y', 'Z', 'z', 'Z', 'z', 'Z', 'z', 's', 'f', 'O', 'o', 'U', 'u', 'A', 'a', 'I', 'i', 'O', 'o', 'U', 'u', 'U', 'u', 'U', 'u', 'U', 'u', 'U', 'u', 'A', 'a', 'AE', 'ae', 'O', 'o', 'Α', 'α', 'Ε', 'ε', 'Ο', 'ο', 'Ω', 'ω', 'Ι', 'ι', 'ι', 'ι', 'Υ', 'υ', 'υ', 'υ', 'Η', 'η');
        return str_replace($a, $b, $str);
    }

    private function trimTolowerRemoveAccent($criteria) : string {
        return $this->removeAccents(trim(strtolower($criteria)));
    }

}