<?php

namespace Open\BackBundle\Form;

use AppBundle\Entity\Node;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\CollectionType;
use Symfony\Component\Form\Extension\Core\Type\FileType;
use Symfony\Component\Form\Extension\Core\Type\HiddenType;
use Symfony\Component\Form\Extension\Core\Type\NumberType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

class SliderForm extends AbstractType
{

    const LABEL = 'label';

    public function buildForm(FormBuilderInterface $builder, array $options)
    {

        // only the value can be changed from this form
        $builder
            ->add('id', HiddenType::class)
            ->add(
                'slug',
                TextType::class,
                array(
                    'label' => 'back.slider.commons.title',
                )
            )->add(
                'content',
                CollectionType::class,
                array(
                    'entry_type' => SliderContentForm::class,
                    'by_reference' => false
                )
            )->add(
                'backgroundImage',
                FileType::class,
                array(
                    'data_class' => null,
                    'label_attr' => ['class' => 'btn btn-primary'],
                    'label' => 'back.slider.commons.backgroundImage',
                    'multiple' => false,
                )
            )->add(
                'orderNode',
                NumberType::class,
                array(
                    'label' => 'back.slider.commons.order',
                    'required' => false
                )
            );
    }

    public function configureOptions(OptionsResolver $resolver)
    {
        $resolver->setDefaults([
            'translation_domain' => 'AppBundle',
            'data_class' => Node::class,
        ]);
    }



}