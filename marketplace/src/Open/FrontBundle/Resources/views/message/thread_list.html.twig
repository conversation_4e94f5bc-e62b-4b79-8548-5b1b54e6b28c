{% extends '@OpenFront/base.html.twig' %}
{% trans_default_domain 'AppBundle' %}
{% set defaultParams =  app.request.query.all() | merge({'page':1}) %}

{% block title %}
    {{ 'ticket.list.title.standard' | trans }}
{% endblock %}

{% block stylesheets %}
    <style>
        div.dataTables_wrapper div.dataTables_processing{
            padding: 0.5em 0;
        }
    </style>
{% endblock %}

{% block body %}
    {% block searchbar %}
        {% if formSearchBar is defined %}
            {% include('@OpenFront/component/search-bar.html.twig') %}
        {% else %}
            {{ render(controller("Open\\FrontBundle\\Controller\\SearchBarController::formAction")) }}
        {% endif %}
    {% endblock %}

    <div class="Page-inner">
        <div class="Page-content">
            <div style="text-align:center" >
                <section class="account-header account-title">{{ 'ticket.list.title.standard' | trans }}</section>
                <button id="ask-for-merchant" type="submit" onclick="
                        window.UI.Modal.askForMerchant(
                        '{{ path('message.new') }}',
                        '{{ 'message.modal.empty_subject'|trans({}, 'AppBundle')|escape('js') }}',
                        '{{ 'message.modal.empty_merchant'|trans({}, 'AppBundle')|escape('js') }}',
                        '{{ 'message.modal.empty'|trans({}, 'AppBundle')|escape('js') }}',
                        '{{ 'contactMerchant.form.success'|trans({}, 'AppBundle')|escape('js') }}',
                        '{{ 'message.add.error'|trans({}, 'AppBundle')|escape('js') }}'
                        )">{{ 'product.modal.askmerchant_button'|trans([],'AppBundle') }}</button>
            </div>

            <div>
                {% include '@OpenFront/message/thread_list_detail.html.twig' with {'suffix': 'mobile'} %}
            </div>

        </div>

    </div>
    <script type="text/template" id="js-ask-for-merchant-modal-tpl">
        <div class="Modal-header">
            <button type="button" class="close js-cancel-button" data-dismiss="modal" aria-label="Close">
                <span >×</span>
            </button>
            <h5 class="Modal-title">{{ 'product.modal.askmerchant_button'|trans([], 'AppBundle') }}</h5>
        </div>
        <form id="askForMerchantForm" method="post" name="askForMerchant" enctype="multipart/form-data">
            <div class="Modal-body">
                <div class="form-group">
                    <div class="Search-bar">
                        <div class="flex-div">
                            <div class="element input-text">
                                <input type="text" name="merchantName" id="merchantName" autocomplete="off" placeholder="{{ 'product.modal.merchant_name'|trans([],'AppBundle') }}" />
                                <div class="autocomplete-results hide">

                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <input type="hidden" name="merchant" id="merchant" value="{{ '' }}"  />
                <input type="hidden" name="_token" value="{{ csrf_token('message_new') }}"/>
                <div class="form-group">
                    <input type="text" name="subject" id="subject" placeholder="{{ 'product.modal.askmerchant_subject'|trans([],'AppBundle') }}" />
                </div>
                <div class="form-group">
                    <textarea placeholder="{{ 'product.modal.askmerchant_message'|trans([], 'AppBundle') }}" rows="2" cols="5" id="message-to-merchant" name="message"></textarea>
                </div>
                <section id="chosen-files-message" class="italic">
                    {{ 'message.attachment_limit'|trans({'%limit%' : 9}) }}
                    <div>{{ 'message.authorized_types'|trans }}</div>
                    <div>{{ 'message.tips'|trans }}</div>
                </section>
                <section id="file-size-validation-message" style="color: #ED0000; visibility: hidden">{{ 'message.file_too_big'|trans({'%size-limit%' : '5 MB'}) }}</section>
                <section id="file-type-validation-message" style="color: #ED0000; visibility: hidden">{{ 'message.file_type_incorrect'|trans }}</section>
                <div id="add-file-min">
                    <section id="chosen-files">
                        <div id="add-file-btn-min" class="pointer tooltip-total">
                            +
                            <div class="tooltiptext">
                                {{ 'message.labels.add_file' | trans | upper }}
                            </div>
                        </div>
                    </section>
                </div>
                <input id='file-input-0'
                       type='file'
                       hidden="hidden"
                       name="attachments[]"
                       accept="image/png, image/jpeg, application/pdf, image/gif, image/png, image/tiff, application/zip, application/msword, application/vnd.openxmlformats-officedocument.wordprocessingml.document, application/vnd.ms-excel, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                />
                <button id="add-file-btn" type="button" class="btn-white" >{{ 'message.labels.add_file' | trans | upper }} <span>+</span></button>
            </div>
            <div class="Modal-footer">
                <button id="js-ask-merchant-send-button" type="button" class="btn btn-primary buttonModal" name="form[askForMerchant]">{{ 'modal.send'|trans({}, 'AppBundle')|upper }}</button>
            </div>
        </form>
    </script>
{% endblock %}
{% block javascripts %}
    <script type="text/javascript">
        $('.threads-table.treads-list').DataTable({
            ordering: false,
            "processing": true,
            "serverSide": true,
            "ajax": "{{ path('thread.list.json') }}",
            "dom": '<"top"i>rt<"bottom"pl><"clear">',
            "columns": [
                {"data": "id", responsivePriority: -1},
                {"data": "creation_date", responsivePriority: -1},
                {"data": "last_thread", responsivePriority: -1},
                {"data": "supplier_name", responsivePriority: -1},
                {"data": "subject", responsivePriority: -1},
                {"data": "threads_count", responsivePriority: -1}
            ],
            "lengthMenu": [[10, 25, 50, 100], ["{{ 'table.perPage'|trans({'%num%':10}, 'AppBundle') }}", "{{ 'table.perPage'|trans({'%num%':25}, 'AppBundle') }}", "{{ 'table.perPage'|trans({'%num%':50}, 'AppBundle') }}", "{{ 'table.perPage'|trans({'%num%':100}, 'AppBundle') }}"]],
            "language": {
                "info": "",
                "search": "",
                "lengthMenu": "_MENU_",
                searchPlaceholder: "{{ 'form.invoice.search'|trans([], 'AppBundle') }}",
                "paginate": {
                    "next": "<img src='/images/shape_right.svg'>",
                    "previous": "<img src='/images/shape_left.svg'>"
                }
            },
            fnDrawCallback: function (settings) {
                console.log(settings.fnRecordsTotal());
                if (settings.fnRecordsTotal() > 0) {
                    $(".orders-list-empty").hide();
                } else {
                    $(".orders-list-empty").show();
                    $(".treads-list-container").hide();
                }
            },
            rowCallback: function (row, data, index) {
                if (data.unread) {
                    $(row).addClass("bold");
                }
                $(row).click(function (){
                    let link = $('.threads-table').data("link");
                    link = link.replace('threadId', data.id);
                    window.location.href = link;
                })
            }
        });

        function autocomplete(suggestions, target) {
            let autocompleteList = target.nextElementSibling;
            if(Object.keys(suggestions).length > 0) {
                autocompleteList.className = "autocomplete-results show";
                let content = '';
                $.each(suggestions, function (key, item) {
                    content += '<p class="autocomplete-item text-left" data="'+key+'">'+item+'</p>';
                });
                autocompleteList.innerHTML = content;
                $(autocompleteList).show()
                $('#merchant').val('');


            } else {
                autocompleteList.className = "autocomplete-results hide";
                $('#merchant').val('');
            }
        }

        $(document).on('keyup','#merchantName', function (event) {
            if(xhr && xhr.readyState != 4) {
                // 4 = request complete and response ready
                xhr.abort();
                console.log('ajax aborted....');
            }

            let query = $(this).val().trim();
            let target = event.target;
            if(query.length > 2) {
                xhr = $.ajax({
                    url: '{{ path('front.suppliers.list') }}',
                    method: "GET",
                    data: {search : query},
                    cache: false,
                    success: function (suggestions) {
                        autocomplete(suggestions, target);
                    }
                });
            } else {
                target.nextElementSibling.className = "autocomplete-results hide";
            }
        });

        $(document).on('click','.autocomplete-item', function (event) {
            let idMerchant = $(this).attr('data');
            let merchantName = $(this).text();
            $('#merchantName').val(merchantName)
            $('#merchant').val(idMerchant)
            $(this).parent().hide()
        });
    </script>

{% endblock %}
