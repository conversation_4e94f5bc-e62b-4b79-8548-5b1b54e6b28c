### [< Back...](docs/docker-install.md)

# Requirements

* _`Important`_ : Windows 10 version `1909` or `higher`

# Installation

#### 1 - Enable wsl functionality in windows

```
dism.exe /online /enable-feature /featurename:Microsoft-Windows-Subsystem-Linux /all /norestart
```

#### 2 - Enable vm functionality in windows

```
dism.exe /online /enable-feature /featurename:VirtualMachinePlatform /all /norestart
```

#### 3 - Download and install linux kernel updates

* [Linux kernel updates package installer](https://wslstorestorage.blob.core.windows.net/wslblob/wsl_update_x64.msi)

#### 4 - Define WSL2 as default

```
wsl --set-default-version 2
```
