<?php

namespace Open\FrontBundle\Controller;

use AppBundle\Controller\MkoController;
use AppBundle\Entity\Quote;
use AppBundle\Entity\QuoteVersion;
use AppBundle\Entity\User;
use AppBundle\Exception\ForbiddenActionException;
use AppBundle\Exception\InvalidActionException;
use AppBundle\Services\MerchantService;
use AppBundle\Services\OfferService;
use AppBundle\Services\QuoteService;
use AppBundle\Services\SerializerService;
use Exception;
use Mpdf\MpdfException;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Security;
use Symfony\Component\Form\Extension\Core\Type\FileType;
use Symfony\Component\Form\Extension\Core\Type\FormType;
use Symfony\Component\Form\Extension\Core\Type\HiddenType;
use Symfony\Component\Form\Extension\Core\Type\SubmitType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormFactoryInterface;
use Symfony\Component\HttpFoundation\File\UploadedFile;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Open\LogBundle\Service\LogService;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\HttpKernel\KernelInterface;
use Symfony\Component\Validator\Constraints\Callback;
use Symfony\Component\Validator\Constraints\Count;
use Symfony\Component\Validator\Constraints\NotBlank;
use Symfony\Component\Validator\Context\ExecutionContextInterface;
use Symfony\Contracts\Translation\TranslatorInterface;
use Twig\Error\Error;

class QuoteController extends MkoController
{
    private LogService $logger;

    /**
     * OrderController constructor.
     * @param LogService $logger
     */
    public function __construct(LogService $logger)
    {
        $this->logger = $logger;
    }

    /**
     * @Route("/message/ask/quotation", name="message.ask.quotation", methods="POST")
     * @Security("is_granted('ROLE_BUYER')")
     *
     * @param Request              $request
     * @param FormFactoryInterface $formFactory
     * @param QuoteService         $quoteService
     * @param OfferService         $offerService
     * @param MerchantService      $merchantService
     *
     * @return Response
     */
    public function messageAskQuotationAction(Request $request, FormFactoryInterface $formFactory, QuoteService $quoteService, OfferService $offerService, MerchantService $merchantService, TranslatorInterface $translator)
    {
        /** @var User $buyer */
        $buyer = $this->getUser();
        $locale = $request->getLocale();

        $authorizedFilesExtensions = ['.pdf', '.jpeg', '.gif', '.png', '.tiif', '.zip', '.doc', '.docx', '.xls', '.xlsx'];
        $authorizedFilesTypes = [
            'application/pdf', # .pdf
            'image/jpeg', # .jpeg
            'image/gif', # .gif
            'image/png', # .png
            'image/tiff', # .tiif
            'application/zip', # .zip
            'application/msword', # .doc
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document', # .docx
            'application/vnd.ms-excel', # .xls
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', # .xlsx
        ];

        $form = $formFactory->createNamedBuilder('', FormType::class, null, ['csrf_token_id' => 'message_offer_send'])
            ->add('offer', HiddenType::class, ['required' => true])
            ->add('merchant', HiddenType::class, ['required' => true])
            ->add('message', TextType::class, ['required' => true, 'constraints' => new NotBlank()])
            ->add(
                'attachments',
                FileType::class,
                [
                    'multiple' => true,
                    'constraints' => [
                        new Count(['min' => 0, 'max' => 10]),
                        new Callback(['callback' => function ($files, ExecutionContextInterface $executionContext) use ($authorizedFilesTypes, $authorizedFilesExtensions) {
                            $sizeLimitMo = 5;
                            $sizeLimit = $sizeLimitMo * 1024 * 1024; // in MB
                            $totalSize = 0;
                            /** @var UploadedFile $file */
                            foreach ($files as $file) {
                                $totalSize += $file->getSize();

                                if (!in_array($file->getMimeType(), $authorizedFilesTypes)) {
                                    $executionContext->addViolation('files.types.authorized', [
                                        '%types%' => implode(', ', $authorizedFilesExtensions)
                                    ]);
                                }
                            }

                            if ($totalSize > $sizeLimit) {
                                $executionContext->addViolation('files.size.limited', [
                                    '%max_size%' => $sizeLimitMo
                                ]);
                            }
                        }]),
                    ],
                ]
            )
            ->add('send', SubmitType::class)
            ->setMethod('POST')
            ->getForm();

        $form->handleRequest($request);
        if ($form->isSubmitted() && $form->isValid()) {
            $merchant = $merchantService->findMerchantEntityByIzbergId($form->getData()['merchant']);

            if (!$merchant) {
                return new Response('Merchant does not exists', Response::HTTP_BAD_REQUEST);
            }

            $offer = $offerService->findOfferForCountryOfDeliveryIzberg($form->getData()['offer'], $locale);

            if (!$offer) {
                return new JsonResponse([$translator->trans('quote.add.not_found', [], 'AppBundle')], Response::HTTP_NOT_FOUND);
            }

            if ($offer->getStatus() != 'active') {
                return new JsonResponse([$translator->trans('quote.add.not_found', [], 'AppBundle')], Response::HTTP_NOT_FOUND);
            }

            $quoteService->askQuote($buyer, $merchant, $offer, $form->getData()['message'], ...$form->getData()['attachments']);

            return new Response('', Response::HTTP_CREATED);
        }

        $errors = [];
        foreach ($form->getErrors(true) as $error) {
            $errors[] = $error->getMessage();
        }

        return new JsonResponse($errors, Response::HTTP_BAD_REQUEST);
    }

    /**
     * @Route("/quotes", name="front.quotes.list")
     * @Route("/quotes/{status}", name="front.quotes.list.status")
     * @Security("is_granted('ROLE_BUYER')")
     */
    public function orderListAction(
        Request              $request,
        FormFactoryInterface $formFactory,
        QuoteService         $quoteService,
        ?string              $status
    ): Response
    {
        if (!$status) {
            $status = Quote::META_STATUS_BUYER_DRAFT;
        }

        if (!Quote::isValidStatus($status)) {
            return new Response('bad request', Response::HTTP_BAD_REQUEST);
        }

        $buyer = $this->getUser();
        $form = $formFactory->createNamedBuilder('', FormType::class, null, [
            'csrf_protection' => false,
            'allow_extra_fields' => true,
        ])
            ->setMethod('GET')
            ->getForm();

        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            //TODO : Mobile ?
        }

        $tabs = array_map(
            static function (array $tab) use ($status) {
                if ($status === $tab['status']) {
                    $tab['isActive'] = true;
                }

                return $tab;
            },
            [
                ['status' => Quote::META_STATUS_BUYER_DRAFT, 'label' => 'quote.status.buyer_draft', 'isActive' => false],
                ['status' => Quote::STATUS_VALIDATED, 'label' => 'quote.status.validated', 'isActive' => false],
                ['status' => Quote:: META_STATUS_BUYER_CANCELLED, 'label' => 'quote.status.buyer_cancelled', 'isActive' => false],
            ]
        );

        $unreads = $quoteService->countUnreadBuyerQuote($buyer);

        return $this->render('@OpenFront/quote/quotes_list.html.twig', [
            'user' => $buyer,
            'status' => $status,
            'tabs' => $tabs,
            'unreads' => $unreads,
        ]);
    }

    /**
     * @Route("/quotes/json/{status}", name="front.quotes.invitation.list.json")
     * @Security("is_granted('ROLE_BUYER')")
     */
    public function quoteJSONList(
        Request           $request,
        QuoteService      $quoteService,
        SerializerService $serializerService,
        ?string           $status
    ): Response
    {
        $filter = [];
        $start = (int) $request->query->get('start');
        $length = (int) $request->query->get('length');
        $draw = $request->query->get('draw');
        $page = $start / $length + 1;
        $search = (array) $request->query->get('search');
        if (array_key_exists('value', $search) && !empty($search['value'])) {
            $filter['search'] = $search['value'];
        }
        /** @var User $buyer */
        $buyer = $this->getUser();
        $paginator = $quoteService->getQuoteByBuyerPaginated($buyer, $filter, $status, $page, $length);
        $responseData = $quoteService->convertToDatatableResponse($draw, $paginator, $buyer->getLocale());

        return new Response(
            $serializerService->serialize($responseData),
            Response::HTTP_OK,
            ['Content-type' => 'application/json']
        );
    }

    /**
     * @Route("/quote/{id}", name="front.quote.detail")
     * @Security("is_granted('ROLE_BUYER')")
     */
    public function quoteDetailAction(
        QuoteService $quoteService,
        int          $id
    ): Response
    {
        /** @var User $buyer */
        $buyer = $this->getUser();
        try {
            $quote = $quoteService->readQuoteBuyer($id, $buyer);

            return $this->render('@OpenFront/quote/quote_detail.html.twig', [
                'user' => $this->getUser(),
                'quote' => $quote,
                'status' => $quote->getStatus(),
            ]);
        } catch (ForbiddenActionException $ex) {
            return new Response ('', Response::HTTP_UNAUTHORIZED);
        }
    }

    /**
     * @Route("/quote/action/add_to_cart", name="front.quote.action.add_to_cart", methods="POST")
     */
    public function addToCart(Request $request, QuoteService $quoteService, FormFactoryInterface $formFactory): Response
    {
        try {
            $quote = $this->handleAction($request, $quoteService, $formFactory, 'quote_add_to_cart', false);
            $quoteService->addToCart($quote);

            return new Response(sprintf('quote %d has been added to cart by %s', $quote->getQuoteId(), $this->getUsername()));
        } catch (ForbiddenActionException $ex) {
            $this->logger->debug("Add to Cart fail : ".$ex->getMessage(), "QUOTE_ADD_TO_CART", $this->getUsername(), ["quoteId" => $quote->getQuoteId()]);;
            return new Response('Forbidden', Response::HTTP_FORBIDDEN);
        } catch (InvalidActionException $ex) {
            $this->logger->debug("Add to Cart fail : ".$ex->getMessage(), "QUOTE_ADD_TO_CART", $this->getUsername(), ["quoteId" => $quote->getQuoteId()]);;
            return new Response('form error', Response::HTTP_BAD_REQUEST);
        } catch (Exception $ex) {
            $this->logger->debug("Add to Cart fail : ".$ex->getMessage(), "QUOTE_ADD_TO_CART", $this->getUsername(), ["quoteId" => $quote->getQuoteId()]);;
            return new Response(sprintf('exception:%s', $ex->getMessage()));
        }
    }

    /**
     * @Route("/quote/action/negociate", name="front.quote.action.negociate", methods="POST")
     * @throws Exception
     */
    public function negociate(Request $request, QuoteService $quoteService, FormFactoryInterface $formFactory): Response
    {
        try {
            $quote = $this->handleAction($request, $quoteService, $formFactory, 'quote_negociate');
            $quoteService->negociate($quote);

            return new Response(sprintf('quote %d has been added to cart by %s', $quote->getQuoteId(), $this->getUsername()));
        } catch (ForbiddenActionException $ex) {
            $this->logger->debug("Negociation fail : ".$ex->getMessage(), "QUOTE_NEGOCIATE", $this->getUsername(), ["quoteId" => $quote->getQuoteId()]);;
            return new Response('Forbidden', Response::HTTP_FORBIDDEN);
        } catch (InvalidActionException $ex) {
            $this->logger->debug("Negociation fail : ".$ex->getMessage(), "QUOTE_NEGOCIATE", $this->getUsername(), ["quoteId" => $quote->getQuoteId()]);;
            return new Response('form error', Response::HTTP_BAD_REQUEST);
        } catch(\Exception $ex) {
            $this->logger->debug("Negociation fail : ".$ex->getMessage(), "QUOTE_NEGOCIATE", $this->getUsername(), ["quoteId" => $quote->getQuoteId()]);;
            throw $ex;
        }
    }

    /**
     * @Route("/quote/action/cancel", name="front.quote.action.cancel", methods="POST")
     * @throws Exception
     */
    public function cancel(Request $request, QuoteService $quoteService, FormFactoryInterface $formFactory): Response
    {
        try {
            $quote = $this->handleAction($request, $quoteService, $formFactory, 'quote_cancel');
            $quoteService->cancel($quote);

            return new Response(sprintf('quote %d has been added to cart by %s', $quote->getQuoteId(), $this->getUsername()));
        } catch (ForbiddenActionException $ex) {
            $this->logger->debug("Cancel fail : ".$ex->getMessage(), "QUOTE_CANCEL", $this->getUsername(), ["quoteId" => $quote->getQuoteId()]);;
            return new Response('Forbidden', Response::HTTP_FORBIDDEN);
        } catch (InvalidActionException $ex) {
            $this->logger->debug("Cancel fail : ".$ex->getMessage(), "QUOTE_CANCEL", $this->getUsername(), ["quoteId" => $quote->getQuoteId()]);;
            return new Response('form error', Response::HTTP_BAD_REQUEST);
        } catch(\Exception $ex) {
            $this->logger->debug("Cancel fail : ".$ex->getMessage(), "QUOTE_CANCEL", $this->getUsername(), ["quoteId" => $quote->getQuoteId()]);;
            throw $ex;
        }
    }

    /**
     * @Route("/quote/quote_version/{quoteVersionId}/pdf", name="front.quote.version.pdf", methods="GET")
     * @throws Error
     * @throws MpdfException
     */
    public function showQuoteVersionPdf(string $quoteVersionId, QuoteService $quoteService, KernelInterface $appKernel): Response
    {
        /** @var QuoteVersion $quoteVersion */
        $quoteVersion = $quoteService->getQuoteVersion(intval($quoteVersionId));
        $customFontDir = $appKernel->getProjectDir() . '/public/font/Helvetica neue/';
        $pdfBin = $quoteService->generateQuoteVersionPdf($quoteVersion, $customFontDir);
        return new Response($pdfBin, 200, [
            'Content-Type' => 'application/pdf',
            'Content-Disposition' => 'inline; filename="' . $quoteVersion->getPdfName() . '"',
        ]);
    }

    /**
     * @Route("/quote/{quoteId}/pdf", name="front.quote.pdf", methods="GET")
     * @throws Error
     * @throws MpdfException
     */
    public function showQuotePdf(string $quoteId, QuoteService $quoteService, KernelInterface $appKernel): Response
    {
        /** @var Quote $quote */
        $quote = $quoteService->getQuote(intval($quoteId));
        $customFontDir = $appKernel->getProjectDir() . '/public/font/Helvetica neue/';
        $pdfBin = $quoteService->generateQuotePdf($quote, $customFontDir);

        return new Response($pdfBin, 200, [
            'Content-Type' => 'application/pdf',
            'Content-Disposition' => 'inline; filename="' . $quoteService->getPdfName($quote) . '"',
        ]);
    }

    /**
     * @throws ForbiddenActionException|InvalidActionException
     */
    private function handleAction(Request $request, QuoteService $quoteService, FormFactoryInterface $formFactory, $csrf_token, $with_reason = true): Quote
    {
        $formBuilder = $formFactory->createNamedBuilder('', FormType::class, null, ['csrf_token_id' => $csrf_token])
            ->add('cartId', HiddenType::class, ['required' => true]); // here it's quoteId..
        if ($with_reason) {
            $formBuilder->add('reason', TextType::class, ['required' => true, 'constraints' => new NotBlank()]);
        }
        $form = $formBuilder
            ->setMethod('POST')
            ->getForm();

        $form->handleRequest($request);
        if ($form->isSubmitted() && $form->isValid()) {
            /** @var User $buyer */
            $buyer = $this->getUser();
            $quote = $quoteService->getQuote($form->getData()['cartId']);
            if ($with_reason) {
                $quote->setReason($form->getData()['reason']);
            }
            if ($quote->getBuyer()->getId() == $buyer->getId()) {
                return $quote;
            }

            throw new ForbiddenActionException();
        }

        throw new InvalidActionException();
    }

}
