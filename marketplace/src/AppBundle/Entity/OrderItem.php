<?php

namespace AppBundle\Entity;

use DateTimeImmutable;
use Doctrine\ORM\Mapping as ORM;

/**
 * @ORM\Table(name="order_item")
 * @ORM\Entity(repositoryClass="AppBundle\Repository\OrderItemRepository")
 */
class OrderItem
{
    /**
     * @var int $id
     * @ORM\Column(name="id", type="integer")
     * @ORM\Id
     */
    private $id;

    /**
     * @var int $merchantOrderId
     * @ORM\Column(name="merchant_order_id", type="integer")
     * @ORM\Id
     */
    private $merchantOrderId;

    /**
     * @var int $offerId
     * @ORM\Column(name="offer_id", type="integer", nullable=true)
     */
    private $offerId;

    /**
     * Order item category id
     *
     * @var int|null $category
     * @ORM\Column(name="category_id", type="integer", nullable=true)
     */
    private $categoryId;

    /**
     * Order item root category id
     *
     * @var int|null $category
     * @ORM\Column(name="root_category_id", type="integer", nullable=true)
     */
    private $rootCategoryId;

    /**
     * Category name in fr user locale
     *
     * @var string|null $defaultCategoryName
     * @ORM\Column(name="default_category_name", type="string", length=250, nullable=true)
     */
    private $defaultCategoryName;

    /**
     * Category name in current user locale
     *
     * @var string|null $currentCategoryName
     * @ORM\Column(name="current_category_name", type="string", length=250, nullable=true)
     */
    private $currentCategoryName;

    /**
     * Category name in current user locale
     *
     * @var string|null $rootCategoryName
     * @ORM\Column(name="root_category_name", type="string", length=250, nullable=true)
     */
    private $rootCategoryName;

    /**
     * @var string $name
     * @ORM\Column(name="name", type="text")
     */
    private $name;

    /**
     * @var float $price
     * @ORM\Column(name="price", type="float")
     */
    private $price;

    /**
     * @var int $quantity
     * @ORM\Column(name="quantity", type="integer")
     */
    private $quantity;

    /**
     * @var float $total
     * @ORM\Column(name="total", type="float")
     */
    private $total;

    /**
     * @var string $supplier
     * @ORM\Column(name="supplier", type="string", nullable=true)
     */
    private $supplier;

    /**
     * @var DateTimeImmutable|null $orderDate
     * @ORM\Column(name="order_date", type="datetime_immutable", nullable=true)
     */
    private $orderDate;

    /**
     * @var int|null
     * @ORM\Column(name="cart_reference", type="integer", nullable=true)
     */
    private $cartReference;

    /**
     * @var string|null $site
     * @ORM\Column(name="site", type="string",length=254, nullable=true)
     */
    private $site;

    /**
     * @var string|null $beneficiary
     * @ORM\Column(name="beneficiary", type="string",length=180, nullable=true)
     */
    private $beneficiary;

    /**
     * @var string|null $beneficiaryName
     * @ORM\Column(name="beneficiary_name", type="string",length=180, nullable=true)
     */
    private $beneficiaryName;

    /**
     * @var string|null $beneficiaryInvoiceEntity
     * @ORM\Column(name="beneficiary_invoice_entity", type="string", nullable=true)
     */
    private $beneficiaryInvoiceEntity;

    /**
     * @var string $currency
     * @ORM\Column(name="currency", type="string",length=20, nullable=true)
     */
    private $currency;

    /**
     * @var bool
     *
     * @ORM\Column(name="gift", type="boolean", nullable=false)
     */
    private $gift;

    /**
     * @var bool
     *
     * @ORM\Column(name="quote", type="boolean", nullable=false)
     */
    private $quote;

    /**
     * @var string|null
     *
     * @ORM\Column(name="marketplace_name", type="string", nullable=true)
     */
    private $marketplaceName;

    /**
     * @var bool
     *
     * @ORM\Column(name="risk", type="boolean", nullable=true, options={"default":null})
     */
    private $risk;

    /**
     * @return int
     */
    public function getId(): int
    {
        return $this->id;
    }

    /**
     * @param int $id
     * @return OrderItem
     */
    public function setId(int $id): self
    {
        $this->id = $id;
        return $this;
    }

    /**
     * @return int
     */
    public function getMerchantOrderId(): int
    {
        return $this->merchantOrderId;
    }

    /**
     * @param int $merchantOrderId
     * @return OrderItem
     */
    public function setMerchantOrderId(int $merchantOrderId): self
    {
        $this->merchantOrderId = $merchantOrderId;
        return $this;
    }

    /**
     * @return int
     */
    public function getOfferId(): ?int
    {
        return $this->offerId;
    }

    /**
     * @param int $offerId
     * @return OrderItem
     */
    public function setOfferId(?int $offerId): self
    {
        $this->offerId = $offerId;
        return $this;
    }

    /**
     * @return int|null
     */
    public function getCategoryId(): ?int
    {
        return $this->categoryId;
    }

    /**
     * @param int|null $categoryId
     * @return OrderItem
     */
    public function setCategoryId(?int $categoryId): self
    {
        $this->categoryId = $categoryId;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getDefaultCategoryName(): ?string
    {
        return $this->defaultCategoryName;
    }

    /**
     * @param string|null $defaultCategoryName
     * @return OrderItem
     */
    public function setDefaultCategoryName(?string $defaultCategoryName): self
    {
        $this->defaultCategoryName = $defaultCategoryName;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getCurrentCategoryName(): ?string
    {
        return $this->currentCategoryName;
    }

    /**
     * @param string|null $currentCategoryName
     * @return OrderItem
     */
    public function setCurrentCategoryName(?string $currentCategoryName): self
    {
        $this->currentCategoryName = $currentCategoryName;
        return $this;
    }

    /**
     * @return string
     */
    public function getName(): string
    {
        return $this->name;
    }

    /**
     * @param string $name
     * @return OrderItem
     */
    public function setName(string $name): self
    {
        $this->name = $name;
        return $this;
    }

    /**
     * @return float
     */
    public function getPrice(): float
    {
        return $this->price;
    }

    /**
     * @param float $price
     * @return OrderItem
     */
    public function setPrice(float $price): self
    {
        $this->price = $price;
        return $this;
    }

    /**
     * @return int
     */
    public function getQuantity(): int
    {
        return $this->quantity;
    }

    /**
     * @param int $quantity
     * @return OrderItem
     */
    public function setQuantity(int $quantity): self
    {
        $this->quantity = $quantity;
        return $this;
    }

    /**
     * @return float
     */
    public function getTotal(): float
    {
        return $this->total;
    }

    /**
     * @param float $total
     * @return OrderItem
     */
    public function setTotal(float $total): self
    {
        $this->total = $total;
        return $this;
    }

    /**
     * @return string
     */
    public function getSupplier(): string
    {
        return $this->supplier;
    }

    /**
     * @param string $supplier
     * @return OrderItem
     */
    public function setSupplier(string $supplier): self
    {
        $this->supplier = $supplier;
        return $this;
    }

    /**
     * @return DateTimeImmutable|null
     */
    public function getOrderDate(): ?DateTimeImmutable
    {
        return $this->orderDate;
    }

    /**
     * @param DateTimeImmutable|null $orderDate
     * @return OrderItem
     */
    public function setOrderDate(?DateTimeImmutable $orderDate): self
    {
        $this->orderDate = $orderDate;
        return $this;
    }

    /**
     * @return int|null
     */
    public function getCartReference(): ?int
    {
        return $this->cartReference;
    }

    /**
     * @param int|null $cartReference
     * @return OrderItem
     */
    public function setCartReference(?int $cartReference): self
    {
        $this->cartReference = $cartReference;
        return $this;
    }

    /**
     * @return string
     */
    public function getSite(): ?string
    {
        return $this->site;
    }

    /**
     * @param string $site
     * @return OrderItem
     */
    public function setSite(?string $site): self
    {
        $this->site = $site;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getBeneficiary(): ?string
    {
        return $this->beneficiary;
    }

    /**
     * @param string|null $beneficiary
     * @return OrderItem
     */
    public function setBeneficiary(?string $beneficiary): self
    {
        $this->beneficiary = $beneficiary;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getBeneficiaryName(): ?string
    {
        return $this->beneficiaryName;
    }

    /**
     * @param string|null $beneficiaryName
     * @return OrderItem
     */
    public function setBeneficiaryName(?string $beneficiaryName): self
    {
        $this->beneficiaryName = $beneficiaryName;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getBeneficiaryInvoiceEntity(): ?string
    {
        return $this->beneficiaryInvoiceEntity;
    }

    /**
     * @param string|null $beneficiaryInvoiceEntity
     * @return OrderItem
     */
    public function setBeneficiaryInvoiceEntity(?string $beneficiaryInvoiceEntity): self
    {
        $this->beneficiaryInvoiceEntity = $beneficiaryInvoiceEntity;
        return $this;
    }

    /**
     * @return string
     */
    public function getCurrency(): string
    {
        return $this->currency;
    }

    /**
     * @param string $currency
     * @return OrderItem
     */
    public function setCurrency(string $currency): self
    {
        $this->currency = $currency;
        return $this;
    }

    public function isGift(): bool
    {
        return $this->gift;
    }

    public function setGift(bool $gift): self
    {
        $this->gift = $gift;
        return $this;
    }

    /**
     * @return bool
     */
    public function isQuote(): bool
    {
        return $this->quote;
    }

    /**
     * @param bool $quote
     * @return self
     */
    public function setQuote(bool $quote): self
    {
        $this->quote = $quote;
        return $this;
    }

    /**
     * @return int|null
     */
    public function getRootCategoryId(): ?int
    {
        return $this->rootCategoryId;
    }

    /**
     * @param int|null $rootCategoryId
     * @return self
     */
    public function setRootCategoryId(?int $rootCategoryId): self
    {
        $this->rootCategoryId = $rootCategoryId;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getRootCategoryName(): ?string
    {
        return $this->rootCategoryName;
    }

    /**
     * @param string|null $rootCategoryName
     * @return self
     */
    public function setRootCategoryName(?string $rootCategoryName): self
    {
        $this->rootCategoryName = $rootCategoryName;
        return $this;
    }

    public function getMarketplaceName(): ?string
    {
        return $this->marketplaceName;
    }

    public function setMarketplaceName(?string $marketplaceName): self
    {
        $this->marketplaceName = $marketplaceName;
        return $this;
    }

    /**
     * @return bool|null
     */
    public function isRisk(): ?bool
    {
        return $this->risk;
    }

    /**
     * @param bool|null $risk
     * @return $this
     */
    public function setRisk(?bool $risk): self
    {
        $this->risk = $risk;
        return $this;
    }
}
