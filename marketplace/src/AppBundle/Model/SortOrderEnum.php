<?php
/**
 * Created by PhpStorm.
 * User: PPH04712
 * Date: 04/07/2019
 * Time: 14:48
 */

namespace AppBundle\Model;


class SortOrderEnum
{

    /***
     * @var string value of the enum
     */
    private $value;

    /**
     * SortOrderEnum constructor.
     * @param string $value
     */
    protected function __construct(string $value)
    {
        $this->value = $value;
    }


    public static function asc(){
        return new SortOrderEnum("asc");
    }

    public static function desc(){
        return new SortOrderEnum("desc");
    }

    /**
     * @return string
     */
    public function getValue(): string
    {
        return $this->value;
    }





}
