{% form_theme form ':Form:appli_layout.html.twig' %}
{% trans_default_domain 'AppBundle' %}

{% block body %}
    <div class="Page-inner">

        <div class="Form UserForm">

            {{ form_start(form, {'name': 'front_user_form-' ~ user_id, 'attr':{'id':'js-user-form-' ~ user_id, 'action': path('front.user.edit', {'id': user_id, 'ajax': 'true'}),'novalidate':'true'}}) }}
            
            <div class="flex-container">
                {{ form_row(form.lastname, {'id':'front_user_form_lastname-' ~ user_id}) }}
                {{ form_row(form.firstname, {'id':'front_user_form_firstname-' ~ user_id}) }}
                {{ form_row(form.email, {'id':'front_user_form_email-' ~ user_id}) }}
                {{ form_row(form.function, {'id':'front_user_form_function-' ~ user_id}) }}

                <div class="form-row">
                    <div class="js-select-wrapper select-wrapper has-text">
                        {{ form_widget(form.role, {'id':'front_user_form_role-' ~ user_id}) }}
                        {{ form_label(form.role) }}
                    </div>
                    <small class="error">{{ form_errors(form.role) }}</small>
                </div>

            </div>

            {% set cbsStyles = '' %}
            {% if form.role.vars.value == admin_role %}
                {% set cbsStyles = 'display:none' %}
            {% endif %}

            <div id="js-sites-cbs-{{ user_id }}" class="js-checkbox-container form--cb UserForm-sites" style="{{ cbsStyles }}">
                {{ form_label(form.sites) }}
                <div id="js-checkboxes-{{ user_id }}" class="UserForm-checkboxes">
                    {% for child in form.sites.children %}
                        {{  form_row(child, {'id':'front_user_form_sites_' ~ child.vars.value ~ '-' ~ user_id}) }}
                    {% endfor %}
                </div>
                <div class="UserForm-sitesError js-cb-errors">
                    <small id="front_user_form-{{ user_id }}[sites][]-error" class="error" for="front_user_form-{{ user_id }}[sites][]">
                        {{ form_errors(form.sites) }}
                    </small>
                </div>
            </div>

            {% if not isDisabled %}
                <div class="Form-group Buttons-group">
                    {{ form_row(form.save, {'id':'front_user_form_save_' ~ user_id}) }}
                </div>
            {% endif %}

            {{ form_row(form._token, {'id':'front_user_form__token-' ~ user_id}) }}

            {{ form_end(form) }}

            <script>
                'use strict';
                var modalLoading;
                window.UI.Select.init();

                $('#{{ 'js-user-form-' ~ user_id }}').submit(function(event) {
                    event.preventDefault();
                    modalLoading = window.UI.Modal.showLoading();
                    var $form = $(this);
                    var url = $form.attr('action');
                    var data = new FormData($form.get(0));

                    $.ajax({
                        url: url,
                        data: data,
                        cache: false,
                        processData: false,
                        contentType: false,
                        type: 'POST',
                        success: function (htmlResponse) {

                            if (htmlResponse === 'success') {
                                window.location = '{{ path('front.company.users') }}';
                            } else {
                                modalLoading.close();
                                $('#edit-user-block-{{ user_id }} div.site-row').html(htmlResponse);
                            }
                        }
                    });
                });
            </script>
        </div>
    </div>
{% endblock %}