<?php

namespace AppBundle\Model;

class DetailedOffer
{
    /**
     * DetailedOffer constructor.
     */
    public function __construct()
    {
        $this->relatedProducts = [];
        $this->similarProducts = [];
        $this->technicalAttributes = [];
        $this->logisticAttributes = [];
    }

    /**
     * @var array $technicalAttributes list of attributes to be displayed in "Technical details" tab
     */
    private $technicalAttributes;

    /**
     * @var array $logisticAttributes list of attributes to be displayed in "Logistic Information" tab
     */
    private $logisticAttributes;


    /**
     * @var array $similarProducts list of offer to be displayed in "Product you may like" section
     */
    private $similarProducts;

    /**
     * @var array $similarProducts list of offer to be displayed in "Related products" section
     */
    private $relatedProducts;


    /**
     * @return array
     */
    public function getTechnicalAttributes(): array
    {
        return $this->technicalAttributes;
    }

    /**
     * @param array $technicalAttributes
     */
    public function setTechnicalAttributes(array $technicalAttributes): void
    {
        $this->technicalAttributes = $technicalAttributes;
    }

    /**
     * @return array
     */
    public function getLogisticAttributes(): array
    {
        return $this->logisticAttributes;
    }

    /**
     * @param array $logisticAttributes
     */
    public function setLogisticAttributes(array $logisticAttributes): void
    {
        $this->logisticAttributes = $logisticAttributes;
    }

    /**
     * @return array
     */
    public function getSimilarProducts(): array
    {
        return $this->similarProducts;
    }

    /**
     * @param array $similarProducts
     */
    public function setSimilarProducts(array $similarProducts): void
    {
        $this->similarProducts = $similarProducts;
    }

    /**
     * @return array
     */
    public function getRelatedProducts(): array
    {
        return $this->relatedProducts;
    }

    /**
     * @param array $relatedProducts
     */
    public function setRelatedProducts(array $relatedProducts): void
    {
        $this->relatedProducts = $relatedProducts;
    }



}