@import "variables";

@import "components/Icon";
@import "components/Modal";
@import "components/Messages";
@import "components/Menu";
@import "components/Form";
@import "components/Checkbox";
@import "components/Table";
@import "components/Tabs";
@import "components/Product";
@import "components/ProductList";
@import "components/Searchbar";
@import "components/Dropdown";
@import "components/Select";
@import "components/TabInfos";
@import "components/HeaderNavbar";
@import "components/Pagination";
@import "components/Accordion";

@import "components/Header";
@import "modules/Footer";
@import "modules/Carte";

@import "modules/Faq";

@import "modules/Disclaimer";

@import "pages/Account";
@import "pages/Homepage";
@import "pages/Buyer";
@import "pages/Invitation";
@import "pages/Quote";
@import "pages/SearchResult";
@import "pages/OfferDetails";
@import "pages/Signin";
@import "pages/Register";
@import "pages/Profile";
@import "pages/Company";
@import "pages/User";
@import "pages/Site";
@import "pages/Document";
@import "pages/Site";
@import "pages/Ticket";
@import "pages/CGU";
@import "pages/Personae";
@import "pages/Offers";
@import "pages/OfferDetail";
@import "pages/Search";
@import "pages/Cart";
@import "pages/Order";
@import "pages/Comparison";
@import "pages/Dispute";
@import "pages/Invoice";
@import "pages/PaymentMode";
@import "pages/Stats";
@import "pages/WishList";
@import "pages/Static";
@import "pages/ShippingAddresses";
@import "pages/Thread";
@import "pages/Claim";
@import "pages/Delegation";
@import "pages/Report";
@import "pages/QuoteDetails";
@import "pages/Favourite";

@font-face {
    font-family: "Roboto";
    src: url("../fonts/Roboto/Roboto-Thin.ttf") format("truetype");
    font-weight: 100;
}

@font-face {
    font-family: "Roboto";
    src: url("../fonts/Roboto/Roboto-ThinItalic.ttf") format("truetype");
    font-weight: 100;
    font-style: italic;
}

@font-face {
    font-family: "Roboto";
    src: url("../fonts/Roboto/Roboto-Light.ttf") format("truetype");
    font-weight: 300;
}

@font-face {
    font-family: "Roboto";
    src: url("../fonts/Roboto/Roboto-LightItalic.ttf") format("truetype");
    font-weight: 300;
    font-style: italic;
}

@font-face {
    font-family: "Roboto";
    src: url("../fonts/Roboto/Roboto-Regular.ttf") format("truetype");
    font-weight: 400;
}

@font-face {
    font-family: "Roboto";
    src: url("../fonts/Roboto/Roboto-Regular.ttf") format("truetype");
    font-weight: 400;
    font-style: italic;
}

@font-face {
    font-family: "Roboto";
    src: url("../fonts/Roboto/Roboto-Medium.ttf") format("truetype");
    font-weight: 500;
}

@font-face {
    font-family: "Roboto";
    src: url("../fonts/Roboto/Roboto-MediumItalic.ttf") format("truetype");
    font-weight: 500;
    font-style: italic;
}

@font-face {
    font-family: "Roboto";
    src: url("../fonts/Roboto/Roboto-Bold.ttf") format("truetype");
    font-weight: 500;
}

@font-face {
    font-family: "Roboto";
    src: url("../fonts/Roboto/Roboto-BoldItalic.ttf") format("truetype");
    font-weight: 500;
    font-style: italic;
}

@font-face {
    font-family: "Roboto";
    src: url("../fonts/Roboto/Roboto-Black.ttf") format("truetype");
    font-weight: 500;
}

@font-face {
    font-family: "Roboto";
    src: url("../fonts/Roboto/Roboto-BlackItalic.ttf") format("truetype");
    font-weight: 500;
    font-style: italic;
}

* {
    margin: 0;
}

html,
body {
    height: 100%;
}

body {
    font-size: 14px;
    margin: 0;
    font-family: 'Roboto', sans-serif !important;
    font-weight: 400;
    line-height: 1.5;
    color: #212529;
    text-align: left;
    background-color: #fff;
}

a {
    &:hover {
        color: #007bff;
    }
}

h1,
h2,
h3,
h4 {
    font-family: 'Roboto', sans-serif;
    font-weight: 400;
    letter-spacing: 6px;
}

button {
    height: 45px !important;
    padding: 0 25px !important;
    font-size: 14px;
}

button:hover {
    background-color: #fff;
    border: 1px solid $TOTAL_RED;
    color: $TOTAL_RED;
    text-decoration: none;
}

.container {
    padding-left: 0 !important;
    padding-right: 0 !important;
}

.page-wrap {
    width: 100%;
}

.page-wrap:after {
    content: "";
    display: block;
}

h2 {
    color: $PRIMARY_COLOR;
    font-weight: bold;
    font-size: 1.5rem;
}

h4 {
    color: $APPLI_GREY6;
    font-size: 1rem;
}

main.container {
    margin: 0 auto;
    min-height: calc(100vh - 100px);
    width: 90%;
    max-width: 1400px;

    .container-inner {
        margin-top: 20px;
    }

    @include breakpoint($BREAKPOINT_DESKTOP) {
        padding-top: $NAV_HEIGHT_WITH_SEARCHBAR;
    }

    @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
        padding: $NAV_HEIGHT_WITH_SEARCHBAR_MOBILE 0 0 0;
        min-height: calc(100vh - 230px);
    }

    @include breakpoint($BREAKPOINT_MOBILE_MAX) {
        .Messages {
            margin: 0;
        }
    }
}

html.menu--isActive {
    overflow: hidden;
}

/** #13605 - Fix IE11 issue when removing images **/
 
  /** https://stackoverflow.com/questions/29932780/what-would-cause-click-events-to-stop-working-in-internet-explorer-11 **/
use {
    pointer-events: none;
}

.total-subtitle {
    font-size: 14px;
    font-weight: bold;
    color: $TOTAL_DARKEST;

    &-2 {
        font-size: 12px;
        font-weight: bold;
        color: $TOTAL_DARKEST;
    }
}

.total-text {
    font-size: 16px;
    font-weight: normal;
    color: $TOTAL_DARK_GREY3;
}

.title-1 {
    text-transform: uppercase;
    color: $TOTAL_DARK_GREY3;
    margin: auto;
    font-size: 25px;
    display: flex;
    justify-content: center;
    font-weight: 300;
    width: 100%;

    @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
        font-size: 22px;
    }
}

/** Selects */
.select-type {
    select {
        -webkit-appearance: none;
        -moz-appearance: none;
        appearance: none;
        border: 0;
    }

    /* IE11 */
    select::-ms-expand {
        display: none;
    }

    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    border-radius: 30px;
    border: 1px solid #d7d7d7;
    height: 30px;
    overflow: hidden;
    min-height: 0 !important;
    width: 200px;
    position: relative;

    &:after {
        content: "";
        position: absolute;
        margin-top: -3px;
        display: block;
        width: 0;
        height: 0;
        border-color: transparent;
        border-top-color: $TOTAL_RED;
        border-width: 6px;
        border-style: solid;
        pointer-events: none;
        top: 50%;
        right: 14px;
    }
}

.Page-content {
    max-width: 1400px;
    margin: auto;
}

.container-fluid-total {
    width: 99.5vw;
    position: relative;
    left: 50%;
    right: 50%;
    margin-left: -50vw;
    margin-right: -50vw;
}

.pointer {
    cursor: pointer !important;
}

.uppercase {
    text-transform: uppercase;
}

.talign-r {
    text-align: right;
}

.w-90 {
    width: 90%;
    margin: auto;
}

.w-60 {
    width: 60%;
}

.w-48 {
    width: 48%;
}

.w-mc {
    width: max-content;
}

.max-1400 {
    max-width: 1400px;
}

.m-l-20 {
    margin-left: 20px;
}

.ml-10 {
    margin-left: 10px;
}

.mt-10 {
    margin-top: 10px;
}

.mb-15 {
    margin-bottom: 15px;
}

.j-c-right {
    justify-content: right;

    .btn-white {
        margin-bottom: 10px;
    }
}

.j-c-left {
    justify-content: right;
}

.t-195 {
    top: 195px !important;
}

.t-325 {
    top: 325px !important;
}

.t-moins-30 {
    top: -30px;
}

.l-25 {
    left: 25px;
}

.l-208 {
    left: 208px !important;
}

.min-w-90 {
    min-width: 90px;
}

.highlighted-solid-red {
    border-top: 3px solid $TOTAL_RED;
}

.underline-dashed {
    border-bottom: 1px dashed $TOTAL_GREY4;
}

.highlighted-dashed {
    border-top: 1px dashed $TOTAL_GREY4;
}

.normal {
    font-weight: normal !important;
}

.bold {
    font-weight: bold;
}

.italic {
    font-style: italic;
}

.black {
    color: $TOTAL_DARK_PURPLE !important;
}

.red {
    color: $TOTAL_RED !important;
}

.breadcrumb-container + main.container {
    padding-top: 0;
}

.hide {
    display: none !important;
}

.h-line {
    background-color: $TOTAL_LIGHT_GREY;
    width: 100%;
    height: 1px;
}

.v-line {
    background-color: $GRAY_DARKEST;
    width: 1px;
}

.dotted-line {
    height: 10px;
    margin: 5px 0;
    background: repeating-linear-gradient(
                    to right,
                    $TOTAL_GREY4 0,
                    $TOTAL_GREY4 10px,
                    transparent 10px,
                    transparent 12px
    ) bottom;
    background-size: 100% 2px;
    background-repeat: no-repeat;
}

.bg-blue {
    background-color: $TOTAL_BLUE;
}

.bg-grey {
    background-color: $APPLI_GREY7 !important;
}

.bd-none {
    background: none !important;
}

.no-right-bordered {
    border-right: none !important;
}

.mobile-align {
    @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
        width: 100% !important;
        padding: 0 15px 0 20px;
    }
}

.arrow-up,
.arrow-down {
    width: 0;
    height: 0;
    border-left: 5px solid transparent;
    border-right: 5px solid transparent;
    border-top: 5px solid $TOTAL_RED;
    border-bottom: none;
    padding: 0 !important;
}

.arrow {
    border-width: 0 2px 2px 0;
    border-color: $TOTAL_DARK_GREY;

    &.right {
        transform: rotate(-45deg);
        -webkit-transform: rotate(-45deg);
    }

    &.left {
        transform: rotate(135deg);
        -webkit-transform: rotate(135deg);
    }

    &.up {
        transform: rotate(-135deg);
        -webkit-transform: rotate(-135deg);
    }
}

.btn-primary {
    background-color: $PRIMARY_COLOR;
    color: $WHITE;
    border: solid 2px $PRIMARY_COLOR;

    &:hover {
        background-color: $WHITE;
        color: $PRIMARY_COLOR;
        border: solid 2px $PRIMARY_COLOR;
    }
}

.btn-secondary {
    background-color: $SECONDARY_COLOR;
    color: $WHITE;
    border: solid 2px $SECONDARY_COLOR;

    &:hover {
        background-color: $WHITE;
        color: $SECONDARY_COLOR;
        border: solid 2px $SECONDARY_COLOR;
    }
}

.btn-total-cancel {
    padding: 0 25px !important;
    display: flex;
    align-items: end;
    text-transform: lowercase;
}

.btn-primary.inverse {
    background-color: $WHITE;
    color: $PRIMARY_COLOR;
    border: solid 2px $PRIMARY_COLOR;

    &:hover {
        background-color: $PRIMARY_COLOR;
        color: $WHITE;
        border: solid 2px $PRIMARY_COLOR;
    }
}

.btn-total-primary {
    background: none !important;
    color: $TOTAL_GREY !important;
    border: none !important;
    vertical-align: bottom;

    &:hover {
        background: none !important;
        color: $TOTAL_GREY !important;
        border: none !important;
    }
}

a.btn-red,
.btn-red {
    background-color: $TOTAL_RED !important;
    color: $WHITE;
    border: solid 2px $TOTAL_RED !important;
    border-radius: 5px !important;
    text-transform: uppercase !important;
    padding: 8px 30px 8px 30px;
    line-height: 2;
    font-weight: bold;

    &:hover {
        background-color: #fff !important;
        border: 1px solid $TOTAL_RED !important;
        color: $TOTAL_RED !important;
        text-decoration: none;
    }
}

.btn-white {
    background: none !important;
    color: $TOTAL_DARK_GREY2;
    border: 1px solid $TOTAL_GREY6;
    border-radius: 5px;
    padding: 0.625rem 0.9375rem;
    font-weight: bold;
    display: inline-block;
    text-align: center;
    min-width: 9rem;

    &:hover {
        cursor: pointer;
    }
}

.btn-grey {
    background-color: $TOTAL_GREY;
    color: $WHITE;
    border: solid 2px $TOTAL_GREY;

    &:hover {
        background-color: $WHITE;
        color: $TOTAL_GREY;
        border: solid 2px $TOTAL_GREY;
    }
}

.btn-disabled {
    background-color: $TOTAL_GREY;
    color: $WHITE;
    border: solid 2px $TOTAL_GREY;
    cursor: default !important;

    &:hover {
        background-color: $TOTAL_GREY;
        color: $WHITE;
        border: solid 2px $TOTAL_GREY;
    }
}

.desktop-only {
    @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
        display: none !important;
    }
}

.desktop-tablet-only {
    @include breakpoint($BREAKPOINT_MOBILE_MAX) {
        display: none !important;
    }
}

.tablet-only {
    @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
        @include breakpoint($BREAKPOINT_MOBILE_MAX) {
            display: none !important;
        }
    }
    @include breakpoint($BREAKPOINT_DESKTOP) {
        display: none !important;
    }
}

.mobile-only {
    @include breakpoint($BREAKPOINT_DESKTOP) {
        display: none !important;
    }
}

.mobile-max-only {
    @include breakpoint($BREAKPOINT_TABLET) {
        display: none !important;
    }
}

.background-primary {
    background-color: $PRIMARY_COLOR;
}

.background-secondary {
    background-color: $SECONDARY_COLOR;
}

.color-primary {
    color: $PRIMARY_COLOR;
}

.pointer {
    cursor: pointer;
}

/* Tooltip text */
.tooltip-total {
    position: relative;
    display: inline-block;
    cursor: default;
    width: fit-content;

    &:hover {
        .tooltiptext {
            visibility: visible;
            opacity: 1;
        }
    }
    &.risk {
        .tooltiptext {
            background-color: #ED0000;
        }
    }

    .tooltiptext {
        visibility: hidden;
        width: 190px;
        word-wrap: break-word;
        background-color: $PRIMARY_COLOR;
        color: #fff;
        text-align: center;
        padding: 5px;
        border-radius: 6px;
        font-size: 10px;
        line-height: normal;

        position: absolute;
        z-index: 1;
        left: 50%;

        /* Fade in tooltip */
        opacity: 0;
        transition: opacity 0.3s;

        transform: translateX(-50%);

        .info {
            font-weight: 700;
        }

        &::after {
            content: "";
            position: absolute;
            bottom: 100%;
            left: 50%;
            margin-left: -5px;
            border-width: 5px;
            border-style: solid;
            border-color: transparent transparent $PRIMARY_COLOR transparent;
        }

        p {
            margin-bottom: 0.3rem !important;
        }
    }
}

/* Facet Tooltip text */
.facetTooltip {
    position: relative;
    display: inline-block;
    cursor: default;

    &:hover {
        .tooltiptext {
            visibility: visible;
            opacity: 1;
        }
    }

    .tooltiptext {
        visibility: hidden;
        width: 150px;
        word-wrap: break-word;
        background-color: $PRIMARY_COLOR;
        color: #fff;
        text-align: center;
        padding: 5px;
        border-radius: 6px;
        font-size: 10px;
        line-height: normal;

        position: absolute;
        z-index: 1;
        top: 135%;
        left: 50%;
        margin-left: -60px;

        /* Fade in tooltip */
        opacity: 0;
        transition: opacity 0.3s;

        .info {
            font-weight: 700;
        }

        &::after {
            content: "";
            position: absolute;
            bottom: 100%;
            left: 50%;
            margin-left: -5px;
            border-width: 5px;
            border-style: solid;
            border-color: transparent transparent $PRIMARY_COLOR transparent;
        }
    }
}

.pb-50 {
    padding-bottom: 50px;
}

.pr-0 {
    padding-right: 0;
}

.mt-negative-50 {
    margin-top: -50px;
}

.pt-6 {
    padding-top: 6px;
}

.pt-20 {
    padding-top: 20px;
}

@include breakpoint($BREAKPOINT_DESKTOP) {
    .mt-100 {
        margin-top: 100px !important;
    }
}

/*
 offset anchor for the fixed header
 */
:target:before {
    content: "";
    display: block;
    height: $NAV_HEIGHT_WITH_SEARCHBAR;
    margin: -$NAV_HEIGHT_WITH_SEARCHBAR 0 0;
}

use {
    pointer-events: none;
}

/* For Firefox */
input[type="number"] {
    -moz-appearance: textfield;
}

/* Webkit browsers like Safari and Chrome */
input[type="number"]::-webkit-inner-spin-button,
input[type="number"]::-webkit-outer-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

.counter-bubble-container {
    display: flex !important;
    justify-content: space-between;
    align-items: center;
    position: absolute;
    top: -14px;
    right: -10px;
    z-index: 2;

    .counter-bubble {
        width: 20px;
        height: 20px;
        font-size: 0.7rem;
        border-radius: 50%;
        background-color: $PRIMARY_COLOR;
        -webkit-box-shadow: 0px 0px 8px $TOTAL_DARK_GREY;
        -moz-box-shadow: 0px 0px 8px $TOTAL_DARK_GREY;
        box-shadow: 0px 0px 8px $TOTAL_DARK_GREY;
        display: flex;
        justify-content: center;
        align-items: center;
        color: $WHITE;
        font-weight: 400;
    }
}

.cookiebarcontainer {
    position: fixed;
    transition: all 0.3s ease;
    width: 100%;
    border-top: none;
    line-height: 20px;
    font-family: "OpenSans", "Poppins", sans-serif;
    letter-spacing: 1px;
    background-color: $TOTAL_BROWN;
    color: #fff;
    font-size: 14px;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    z-index: 2147483647;
    bottom: 0;
    padding: 10px 0;
    opacity: 0;
    transform: translateY(100%);

    .button {
        background-color: $WHITE;
        color: $TOTAL_BROWN !important;
    }

    &.is-visible {
        transform: translateY(0);
        opacity: 1;
    }

    .cbmessage {
        width: 100%;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0 20px;

        p {
            margin-bottom: 0;
        }

        @include breakpoint($BREAKPOINT_MOBILE_MAX) {
            flex-direction: column;

            p {
                margin-bottom: 10px;
            }
        }
    }

    .cbhide {
        width: 100px;
        border: 1px solid $WHITE;
        font-size: 1.2em;
        font-weight: 400;
        line-height: 1em;
        color: $WHITE;
        display: block;
        cursor: pointer;
        padding: 10px;
        margin: 0;

        &:hover {
            color: $TOTAL_BROWN !important;
        }
    }
}

/* FLEX */
.flex {
    display: flex;
}

.f-dc {
    flex-direction: column;
}

.f-ai-fe {
    align-items: flex-end;

    @include breakpoint($BREAKPOINT_MOBILE_MAX) {
        align-items: flex-start;
    }
}

.jc-sb {
    justify-content: space-between;
}

.jc-fe {
    justify-content: flex-end;
}

.jc-fs {
    justify-content: flex-start !important;
}

.ai-c {
    align-items: center;
}

.ta-c {
    text-align: center;
}

.no-margin {
    margin: 0 !important;
}

.margin-1 {
    margin: 1em;
}

.account-title {
    padding: 0 35px 0 35px;

    @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
        margin-top: 30px;
    }
}

body:not(.user-is-tabbing) button:focus,
body:not(.user-is-tabbing) input:focus,
body:not(.user-is-tabbing) select:focus,
body:not(.user-is-tabbing) textarea:focus {
    outline: none;
}

.no_hover {
    &:hover {
        text-decoration: none !important;
    }
}

$starrrColor: #ffd119 !default;
.fa {
    display: inline-block;
    font: normal normal normal 14px/1 FontAwesome;
}

.fa-star::before {
    font-family: "Font Awesome 5 Free";
    font-weight: 900;
    content: "\f006";
}

.rate-select-layer span {
    color: $starrrColor;
}

.starrr {
    display: inline-block;

    a {
        font-size: 16px;
        padding: 0 1px;
        cursor: pointer;
        color: $starrrColor;
        text-decoration: none;
    }
}

.qtyComponent {
    display: flex;
    font-weight: 300;
    align-items: center;
    justify-content: flex-end;

    .qtyComponentBtn {
        display: flex;
        align-items: center;
        flex-direction: row;
    }

    .qtyModif {
        background-color: #353535;
        height: 14px !important;
        width: 19px;
        padding: 0 !important;
        margin: 0;
        border-radius: 0;
        border: none;
        position: relative;

        &::after {
            content: "";
            display: block;
            height: 2px;
            width: 8px;
            background: #fff;
            top: 50%;
            left: 50%;
            margin-top: -1px;
            margin-left: -4px;
            position: absolute;
        }

        &.plus::before {
            content: "";
            display: block;
            height: 8px;
            width: 2px;
            background: #fff;
            top: 50%;
            left: 50%;
            margin-top: -4px;
            margin-left: -1px;
            position: absolute;
        }

        &:disabled {
            opacity: 0.3;
            cursor: not-allowed;
        }
    }

    .qtyInput {
        margin: 0 10px;
        border-radius: 0;
        padding: 0 10px;
        text-align: center;
    }
}

.product-link {
    display: inline-block;
    margin-bottom: 15px;
    object-fit: contain;
    font-size: 12px;
    font-weight: bold;
    font-style: normal;
    font-stretch: normal;
    line-height: 2.5;
    letter-spacing: normal;
    text-align: center;
    text-decoration: underline;
    color: #353535;
}

/**** Logout page  *****/

body[class*="Page--logout_info"], body[class*="Page--nl_logout_info"] {
    .logout-page {
        display: flex;
        margin: auto;
        min-height: calc(100vh - 320px);
        flex-direction: column;
        width: 90%;
        max-width: 1400px;
        margin-top: 130px;

        @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
            flex-direction: column;
            width: 85%;
            justify-content: space-around;
            margin-top: 0;
        }

        a {
            font-size: 18px;
            text-transform: none;
            color: $TOTAL_RED !important;

            &:hover {
                text-decoration: none !important;
            }

            img {
                margin-right: 10px;
            }
        }

        &-text {
            font-size: 18px;
            margin-bottom: 50px;

            @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
                margin-bottom: 0;
            }
        }

        &-link {
            border-bottom: 2px solid $TOTAL_RED;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            font-size: 18px;
            font-weight: normal;
            text-decoration: none;
            padding-bottom: 10px;

            section {
                padding-bottom: 15px;
            }

            &:hover {
                cursor: pointer;
                text-decoration: none;
            }
        }
    }
}

/****** Technical support page *******/

.technical-support-page {
    display: flex;
    margin: 70px 0 10px 0;
    justify-content: space-between;
    min-height: calc(100vh - 250px);

    @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
        flex-direction: column;
        justify-content: space-around;
        margin: 0 0 10px 0;
    }
}

.technical-support {
    &-text {
        width: 45%;
        margin-top: 15px;
        line-height: 1.8rem;

        @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
            width: 100%;
        }

        section {
            font-size: 16px;
            padding-bottom: 20px;
        }
    }

    &-img {
        margin-bottom: 30px;
        width: 45%;

        @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
            width: 100%;
            text-align: center;

            img {
                width: 90%;
            }
        }
    }
}

.Modal-wrapper {
    height: 100%;
    position: fixed;
    top: 0;
    right: 0;
    left: 0;
    bottom: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10000;

    .Modal-content {
        transform: none;
        top: initial;
        left: initial;
        overflow: auto;
    }
}


body[class*="Page-unlogged"], body[class*="Page-nl_unlogged"] {
    .logo--home {
        width: 25%;
        justify-content: flex-end;
        @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
            width: 100%;
            justify-content: center;
            margin-top: 15px;
        }
    }

    @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
        .Header {
            .title-home,
            .Header-hamburgerIcon.supplier-only {
                display: none !important;
            }
        }
    }
}


.action-btn-container {
    @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
        justify-content: space-around;
        align-items: flex-end;
        display: flex;
        width: 100%
    }
}

.merchant-minimum-order-amount {
    font-size: 14px;
    font-weight: normal !important;
    color: $TOTAL_RED !important;
}
