<?php

namespace Open\IzbergBundle\Api;

use Open\IzbergBundle\Api;
use Unirest;
use Doctrine\Common\Collections\ArrayCollection;
use Open\IzbergBundle\Model\Category;

/**
 * Wrapper for the Category API
 * Class CategoryApi
 * @package Open\IzbergBundle\Api
 */
class CategoryApi extends Api
{
    const APPLICATION_CATEGORY_PATH = 'application_category/';

    public function  getUri(){
        return 'application_category';
    }

    public function getItemClass(){
        return Category::class;
    }

    /**
     * Helper to get categories
     * @param string $locale
     *
     * @private
     * @return mixed
     */
    private function getCategories($locale)
    {
        $query = [
            'application' => $this->getApplicationId(),
            'language' => $locale,
        ];

        $headers = [
            Api::HEADER_ACCEPT_LANGUAGE => $locale,
        ];

        $requestUrl = $this->getApiUrl() . self::APPLICATION_CATEGORY_PATH . '?' . http_build_query($query);
        $categoriesArray = [];

        do {
            $response = $this->sendApiRequest(
                self::HTTP_GET_OPERATION,
                $requestUrl,
                [],
                $headers,
                [
                    self::OPTION_IS_OPERATOR => true,
                ]
            );

            // For all addresses in the json serialize to an Address entity and add it to the collection
            foreach ($response->body->objects as $category_json) {
                /** @var Category $category */
                $category = $this->serializer->deserialize(json_encode($category_json), Category::class, 'json');

                $categoriesArray[] = $category;
            }
            $categories = new ArrayCollection($categoriesArray);

            $requestUrl = $response->body->meta->next;

        } while($requestUrl !== null);

        return $categories;
    }

    /**
     * Return the list of all categories
     * @param string $locale
     * @link https://documenter.getpostman.com/view/653426/izberg-api-official-collection/Jsix#aadd6d8-24b2-58ad-7df0-6c82bcff6575
     * @return mixed
     */
    public function getAllCategories($locale)
    {
        return $this->getCategories($locale);
    }
}
