<?php

namespace AppBundle\Model\Cart;

class CartItemShippingGroup
{
    private const SHIPPING_MODE_STANDARD = 'shipping-mode-standard';
    private const SHIPPING_MODE_FAST = 'shipping-mode-fast';

    /** @var int */
    private $shippingId;

    /** @var int */
    private $deliveryTime;

    /** @var float */
    private $deliveryPrice;

    /** @var array */
    private $cartItemIds;

    /** @var int */
    private $order;

    /** @var int */
    private $cartId;

    /** @var string */
    private $mode;

    public function getShippingId(): int
    {
        return $this->shippingId;
    }

    public function setShippingId(int $shippingId): self
    {
        $this->shippingId = $shippingId;
        return $this;
    }

    public function getDeliveryTime(): int
    {
        return $this->deliveryTime;
    }

    public function setDeliveryTime(int $deliveryTime): self
    {
        $this->deliveryTime = $deliveryTime;
        return $this;
    }

    public function getDeliveryPrice(): float
    {
        return $this->deliveryPrice;
    }

    public function setDeliveryPrice(float $deliveryPrice): self
    {
        $this->deliveryPrice = $deliveryPrice;
        return $this;
    }

    public function getCartItemIds(): array
    {
        return $this->cartItemIds;
    }

    public function setCartItemIds(array $cartItemIds): self
    {
        $this->cartItemIds = $cartItemIds;
        return $this;
    }

    public function flagStandardMode(): self
    {
        $this->mode = self::SHIPPING_MODE_STANDARD;
        return $this;
    }

    public function flagFastMode(): self
    {
        $this->mode = self::SHIPPING_MODE_FAST;
        return $this;
    }

    public function isStandardMode(): bool
    {
        return ($this->mode === self::SHIPPING_MODE_STANDARD);
    }

    public function isFastMode(): bool
    {
        return ($this->mode === self::SHIPPING_MODE_FAST);
    }

    public function getCartId(): int
    {
        return $this->cartId;
    }

    public function setCartId(int $cartId): self
    {
        $this->cartId = $cartId;
        return $this;
    }

    public function getOrder(): int
    {
        return $this->order;
    }

    public function setOrder(int $order): self
    {
        $this->order = $order;
        return $this;
    }
}
