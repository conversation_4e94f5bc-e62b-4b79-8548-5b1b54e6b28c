<?php

namespace AppBundle\Model\Search\Filter;

final class SearchFilterPrefix implements SearchFilterInterface
{
    /**
     * @var String
     */
    private $property;

    /**
     * @var String
     */
    private $value;

    public function __construct(string $property, $value)
    {
        $this->property = $property;
        $this->value = $value;
    }

    public function query(): array
    {
        return [
            'prefix' => [
                $this->property => [
                    "value" => $this->value
                ]
            ]
        ];
    }
}
