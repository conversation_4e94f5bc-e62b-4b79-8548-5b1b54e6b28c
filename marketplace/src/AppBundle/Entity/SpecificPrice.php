<?php

namespace AppBundle\Entity;

use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\ORM\Mapping as ORM;
use Doctrine\ORM\Mapping\Index;

/**
 * SpecificPrice
 *
 * @ORM\Table(name="specific_prices" ,indexes={
 *   @Index(name="companyIdentification_idx", columns={"company_ident"}),
 *   @Index(name="IZB_merchant_id_idx", columns={"IZB_merchant_id"}),
 *   @Index(name="vendor_reference_idx", columns={"vendor_reference"})})
 * @ORM\Entity(repositoryClass="AppBundle\Repository\SpecificPriceRepository")
 * @ORM\HasLifecycleCallbacks()
 *
 */
class SpecificPrice
{
    /**
     * @var int
     *
     * @ORM\Column(name="id", type="integer")
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="AUTO")
     */
    private $id;

    /**
     * @ORM\Column(name="company_ident", type="string", length=255, nullable=true)
     * @var string
     */
    private $companyIdentification;

    /**
     * @ORM\Column(name="threshold1", type="integer", nullable=true)
     * @var integer
     */
    private $threshold1;

    /**
     * @ORM\Column(name="price1", type="float", nullable=true)
     * @var float
     */
    private $price1;

    /**
     * @ORM\Column(name="threshold2", type="integer", nullable=true)
     * @var integer
     */
    private $threshold2;

    /**
     * @ORM\Column(name="price2", type="float", nullable=true)
     * @var float
     */
    private $price2;

    /**
     * @ORM\Column(name="threshold3", type="integer", nullable=true)
     * @var integer
     */
    private $threshold3;

    /**
     * @ORM\Column(name="price3", type="float", nullable=true)
     * @var float
     */
    private $price3;

    /**
     * @ORM\Column(name="threshold4", type="integer", nullable=true)
     * @var integer
     */
    private $threshold4;

    /**
     * @ORM\Column(name="price4", type="float", nullable=true)
     * @var float
     */
    private $price4;

    /**
     * @ORM\Column(name="IZB_merchant_id", type="integer", nullable=false)
     */
    private $IZBmerchantId;

    /**
     * @ORM\Column(name="moq", type="integer", nullable=true)
     */
    private $moq;

    /**
     * @ORM\Column(name="basic_price", type="float", nullable=true)
     * @var float
     */
    private $basicPrice;

    /**
     * @ORM\Column(name="vendor_reference", type="string", length=255, nullable=true)
     * @var string
     */
    private $vendorReference;

    /**
     * @ORM\Column(name="incoterm", type="string", length=255, nullable=true)
     * @var string
     */
    private $incoterm;

    /**
     * @ORM\Column(name="country", type="string", length=255, nullable=true)
     * @var string
     */
    private $country;

    public function getId(): int
    {
        return $this->id;
    }

    public function getVendorReference(): string
    {
        return $this->vendorReference;
    }

    public function setVendorReference(string $vendorReference): void
    {
        $this->vendorReference = $vendorReference;
    }

    public function setBasicPrice(float $basicPrice){
        $this->basicPrice = $basicPrice;
    }

    public function getBasicPrice(): ?float
    {
        return $this->basicPrice;
    }

    public function setMoq(int $moq)
    {
        $this->moq = $moq;
    }

    public function getMoq(): ?int
    {
        return $this->moq;
    }

    public function setIZBmerchantId(int $IZBmerchantId)
    {
        $this->IZBmerchantId = $IZBmerchantId;
    }

    public function getIZBmerchantId(): int
    {
        return $this->IZBmerchantId;
    }

    public function getCompanyIdentification(): string
    {
      return $this->companyIdentification;
    }

    public function setCompanyIdentification(string $companyIdentification): void
    {
      $this->companyIdentification = $companyIdentification;
    }

    public function getThreshold1(): ?int
    {
        return $this->threshold1;
    }

    public function setThreshold1(int $threshold1): void
    {
        $this->threshold1 = $threshold1;
    }

    public function getPrice1(): ?float
    {
        return $this->price1;
    }

    public function setPrice1(float $price1): void
    {
        $this->price1 = $price1;
    }

    public function getThreshold2(): ?int
    {
        return $this->threshold2;
    }

    public function setThreshold2(int $threshold2): void
    {
        $this->threshold2 = $threshold2;
    }

    public function getPrice2(): ?float
    {
        return $this->price2;
    }

    public function setPrice2(float $price2): void
    {
        $this->price2 = $price2;
    }

    public function getThreshold3(): ?int
    {
        return $this->threshold3;
    }

    public function setThreshold3(int $threshold3): void
    {
        $this->threshold3 = $threshold3;
    }

    public function getPrice3(): ?float
    {
        return $this->price3;
    }

    public function setPrice3(float $price3): void
    {
        $this->price3 = $price3;
    }

    public function getThreshold4(): int
    {
        return $this->threshold4;
    }

    public function setThreshold4(int $threshold4): void
    {
        $this->threshold4 = $threshold4;
    }

    public function getPrice4(): float
    {
        return $this->price4;
    }

    public function setPrice4(float $price4): void
    {
        $this->price4 = $price4;
    }

    public function getIncoterm(): string
    {
        return $this->incoterm;
    }

    public function setIncoterm(string $incoterm)
    {
        $this->incoterm = $incoterm;
    }

    public function getCountry(): string
    {
        return $this->country;
    }

    public function setCountry(string $country)
    {
        $this->country = $country;
    }

    public function hasThreshold(): bool
    {
        return count($this->getThresholdsAndPrices())>0;
    }

    /**
     * return an array of threshold and price associated
     * example: [
     *      50 => 150.00,
     *      60 => 140.50,
     *      80 => 160.70,
     * ]
     * This method filter only the couple threshold/price not null
     *
     * @return array
     */
    public function getThresholdsAndPrices(): array
    {
        $thresholds = [$this->getThreshold1(), $this->getThreshold2(), $this->getThreshold3(), $this->getThreshold4(),];
        $prices = [$this->getPrice1(), $this->getPrice2(), $this->getPrice3(), $this->getPrice4(),];

        return array_filter(array_map(function($threshold, $price) {
            if ($threshold && $price) {
                return [$threshold, $price];
            }
        }, $thresholds, $prices));
    }
}
