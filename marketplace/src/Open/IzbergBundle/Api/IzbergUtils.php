<?php

namespace Open\IzbergBundle\Api;

use AppBundle\Entity\User;
use AppBundle\Model\Cart\Cart;
use AppBundle\Model\Cart\CartItem;
use AppBundle\Services\CustomsService;
use AppBundle\Services\OfferService;
use Open\IzbergBundle\Model\Item;
use Open\IzbergBundle\Service\CategoryService;

class IzbergUtils
{
    public static function buildQueryUriFromFilters (array $filters)
    {
        if (count($filters)) {
            return '?' . implode('&', array_map(
                    function ($filter, $value) {
                        return $filter . '=' . $value;
                    },
                    array_keys($filters),
                    array_values($filters)
                ));
        }

        return "";
    }

    /**
     * parse an izberg resource and return its id
     * @param string $resource
     * @return null|string
     */
    public static function parseIzbergResourceAndGetId (string $resource = null)
    {
        if (!$resource) {
            return null;
        }

        $result = substr($resource, 0, -1);
        return substr($result, strrpos($result, '/') + 1);
    }

    /**
     * validate and parse a date from izberg
     */
    public static function parseIzbergDate(?string $dateString, String $datePattern): ?\DateTime
    {
        if (!$dateString) {
            return null;
        }

        return \DateTime::createFromFormat($datePattern, $dateString);
    }

    /**
     *
     * @param array $items
     * @return array
     */
    public static function getAttributeItemForMail (array $items){
        $emailItemsFactory = function (Item $item) {
            return [
                'name' => $item->getName(),
                'quantity' => $item->getQuantity(),
                'amount' => $item->getAmount(),
                'unitPrice' => $item->getAmount(),
                'currency' => $item->getCurrency(),
                'totalPrice' => $item->getQuantity() * $item->getAmount()
            ];
        };

       return  array_map($emailItemsFactory, $items);

    }

    /**
     *
     * @param array $items
     * @return array
     */
    public static function getAttributeProductForMail (array $items){
        $emailItemsFactory = function (Item $item) {
            return [
                'name' => $item->getName(),
                'sku' => $item->getSku(),
                'quantity' => $item->getQuantity(),
                'price_ht' => $item->getAmount()
            ];
        };

        return  array_map($emailItemsFactory, $items);

    }

    /**
     *
     * @param Cart $cart
     * @param User $buyer
     * @param OfferService $offerService
     * @param CategoryService $categoryService
     * @return array
     */
    public static function getAttributeItemsForRiskMail (Cart $cart, User $buyer, OfferService $offerService, CategoryService $categoryService, CustomsService $customsService): array
    {
        $emailItemsFactory = function (CartItem $item) use ( $buyer, $offerService, $categoryService, $cart, $customsService) {
            if ($item->isRisk()) {
                $offer = $offerService->findOffer($item->getOfferId(),$buyer->getLocale(),$buyer->getMarketPlace()) ??
                    $offerService->findOfferForCountryOfDeliveryIzberg($item->getOfferId(), $buyer->getLocale());
                $cats = $offer->getProduct()->getCategoriesIds() ??
                    array_filter(array_map(function ($categories){
                        return reset($categories)->getId();
                    },$offerService->getProductcategories($offer->getProduct()->getId(), $buyer->getMarketPlace())));
                $riskCategoryId = $customsService->categoriesIdsGetRisk($cats);
                $category = $riskCategoryId?$categoryService->find($riskCategoryId)->getName():'';
                return [
                    'itemName' => $item->getName(),
                    'supplier' => $offer->getMerchant()->getName(),
                    'price' => $item->getUnitPrice(),
                    'category'  => $category
                ];
            }
            return null;
        };
        return  array_filter(array_map($emailItemsFactory, $cart->getItems()));
    }

    public static function isIdIdentity (string $id):bool {
        return (bool) preg_match("/^.+-.+$/",$id);
    }
}
