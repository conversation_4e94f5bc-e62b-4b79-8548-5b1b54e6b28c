{% trans_default_domain 'AppBundle' %}
<!-- Modal -->
<div class="modal fade" id="invoiceEntityThresholdModal" tabindex="-1" role="dialog" aria-labelledby="invoiceEntityThresholdModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="invoiceEntityThresholdModalLabel">{{ 'back.invoice_entity.modal.title' | trans }}</h5>
                <a href="" class="close" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </a>
            </div>
            <div class="modal-body">
                <p id="threshold-modal-text">{{ 'back.invoice_entity.modal.text.thresholds' | trans }}</p>
            </div>
            <div class="modal-footer">
                <a class="btn btn-secondary" href="">{{ 'back.invoice_entity.modal.actions.cancel' | trans }}</a>

                <form method="post" action="{{ path('admin.invoice_entity.thresholds') }}">
                    <input type="hidden" name="token" value="{{ csrf_token('invoice_entity_threshold_update') }}" />
                    <input id="invoice-entity" type="hidden" name="invoice_entity" value="0"/>
                    <input id="invoice-entity-threshold" type="hidden" name="invoice_entity_threshold" value="0"/>
                    <input id="invoice-entity-status" type="hidden" name="invoice_entity_threshold_status" value="0"/>

                    <button type="submit" class="btn btn-primary">
                        {{ 'back.invoice_entity.modal.actions.submit' | trans }}
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>
