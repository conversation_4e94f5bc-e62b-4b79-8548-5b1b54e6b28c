@import "../variables";
@import "~breakpoint-sass/stylesheets/breakpoint";

.tab-infos {

    .tab-content {
        &:not(.show) {
            display: none;

            @media print {
                display: block;
            }
        }

        &.show {
            display: block;
        }
    }

    .content-text-image {
        display: flex;
        flex-direction: row;

        .image-container {
            display: flex;
            flex-direction: column;

            .img {
                max-width: 100px;
                width: 100px;
                height: 100px;
                overflow: hidden;
                display: flex;
                align-items: center;

                img {
                    position: relative;
                    width: auto;
                    height: auto;
                    max-height: 100px;
                    left: 50%;
                    transform: translateX(-50%);
                }
            }

            .all-products {
                color: $TOTAL_GREY;
                margin-top: 1rem;
                text-decoration: underline;
                white-space: nowrap;
            }
        }

        .text-container {
            display: flex;
            flex-direction: column;
            margin-left: 3rem;

            .seller-name {
                font-size: 1.5rem;
            }

            .label-decription {
                color: $TOTAL_GREY;
            }

            .description {
                color: $TOTAL_DARK_GREY;
            }
        }
    }

    .about-block {
        padding-bottom: 70px;
    }

    .about-seller-container {
        display: flex;
        padding-top: 20px;
        margin-bottom: 10px;

        .about-seller-container-img {
            margin-right: 50px;
            overflow: hidden;
            flex-basis: 40%;

            img {
                width: 100%;
            }
        }

        .seller-info {
            flex-basis: 100%;
        }

        h3 {
            font-size: 1.625rem;
            font-weight: 700;
        }

        .seller-text-title {
            color: $TOTAL_GREY;
            font-size: 1rem;
        }

        .seller-text {
            font-size: 1rem;
            color: $TOTAL_DARK_GREY;
            text-align: justify;
            margin-top: 5px;
        }
    }

    .link-product-seller {
        color: $TOTAL_GREY;
        font-weight: 300;
        margin-top: 15px;
        text-decoration: underline;
        font-size: 0.9375rem;
    }

    @include breakpoint($BREAKPOINT_DESKTOP) {
        padding: 10px 20px;

        .tab-header {
            display: flex;
            flex-direction: row;

            .tab-title {

                margin-right: 70px;
                cursor: pointer;

                &.active {
                    color: $TOTAL_BLUE;
                }

                .tab-h4 {
                    color: $TOTAL_GREY;
                    height: 100%;
                    font-weight: 400;
                    font-size: 1.1rem;
                    padding: 15px 0 0;

                    &.active {
                        color: $BLACK;
                        border-bottom: 2px solid $TOTAL_BLUE;
                    }
                }
            }

            .datasheet {
                display: flex;
                flex-grow: 1;
                justify-content: flex-end;

                .Icon.stroke {
                    fill: $WHITE;
                    width: 17px;
                    height: 17px;
                    margin-right: 10px;
                }

                a {
                    display: flex;
                    align-items: center;
                    padding: 10px 15px;
                    max-width: 200px;
                    height: 40px;
                    margin: 0;

                    &:hover {
                        .Icon.stroke {
                            fill: $PRIMARY_COLOR;
                        }
                    }
                }
            }
        }

        .tab-content {

            margin-top: 20px;

            .content-grid {
                display: flex;
                flex-flow: row wrap;
                justify-content: flex-start;


                .attribute {
                    display: flex;
                    flex-direction: column;
                    padding: 5px 20px 10px 0;
                    flex: 0 1 25%;
                    font-size: 0.9375rem;

                    .title {
                        font-weight: 300;
                        color: $TOTAL_GREY;
                        margin-bottom: 0;
                    }

                    .content {
                        color: $TOTAL_DARK_GREY;

                        .Icon {
                            width: 23px;
                            height: 23px;
                            margin-left: 0;
                        }

                        a {
                            color: $PRIMARY_COLOR;

                            &:hover {
                                color: $PRIMARY_COLOR;
                            }
                        }
                    }
                }
            }
        }
    }

    @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
        padding: 40px 20px;

        .tab-header {
            padding: 20px 0;

            h4 {
                color: $BLACK;
                font-size: 1.125rem;
                font-weight: 500;
                margin: 0;
                display: flex;
                justify-content: space-between;
                align-items: center;

                .symbol {
                    color: $TOTAL_DARK_GREY;
                    font-weight: 300;
                    font-size: 24px;
                    font-family: 'Open Sans', Verdana, Arial, sans-serif;
                }

                .symbol-plus {
                    display: block;
                }

                .symbol-minus {
                    display: none;
                }

                &.active {
                    .symbol-plus {
                        display: none;
                    }

                    .symbol-minus {
                        display: block;
                    }
                }
            }
        }

        .tab-content {
            .attribute {
                display: flex;
                justify-content: space-between;

                .title {
                    color: $TOTAL_GREY;
                }

                .content {
                    color: $TOTAL_DARK_GREY;
                }
            }

            .content-text-image {
                display: flex;
                flex-direction: column;

                .seller-name {
                    color: $TOTAL_DARK_GREY;
                }

                .img {
                    display: flex;
                    justify-content: center;
                    max-width: 100%;
                    width: 100%;
                    height: 100%;
                    overflow: hidden;
                    align-items: center;

                    img {
                        position: relative;
                        width: auto;
                        height: auto;
                        max-width: 100%;
                        left: 50%;
                        transform: translateX(-50%);
                    }
                }

                .all-products {
                    color: $TOTAL_GREY;
                    text-decoration: underline;
                    white-space: nowrap;
                    margin: 1rem 0;
                }

                .description {
                    color: $TOTAL_DARK_GREY;
                }
            }


        }
    }

    &.plain-tab {
        padding: 0;

        & > div > .h-line {
            display: none;
        }

        .tab-title {
            text-transform: uppercase;
            border-right: 1px solid $TOTAL_LIGHT_GREY;
            margin: 0;

            .tab-h4 {
                color: $TOTAL_DARK_GREY;
                background-color: $TOTAL_LIGHT_GREY;
                padding: 1em 1em;
                font-size: 0.8em;
                margin-bottom: 0;
                height: 100%;
                font-weight: 900;
                word-break: break-word;

                @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
                    font-size: 0.8125rem;
                }

                &.active {
                    color: $TOTAL_BLUE;
                    background-color: $WHITE;
                    border-bottom: none;
                }
            }
        }

        .tab-content {
            margin-top: 0;

            .content-list {
                background-color: $WHITE;
            }
        }

        @if $breakpoint != $BREAKPOINT_TABLET {
            @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
                .tab-header {
                    display: flex;
                    width: 100%;
                    padding-bottom: 0;

                    .tab-h4 {
                        text-align: center;
                    }
                }
            }
        }
    }

    .empty-tab {
        padding: 35px;
    }
}