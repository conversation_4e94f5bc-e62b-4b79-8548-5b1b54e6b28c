<?php

namespace Open\IzbergBundle\Model;

class UserIdentity
{
    private string $uuid;
    private string $email;

    /**
     * @return string
     */
    public function getUuid(): string
    {
        return $this->uuid;
    }

    /**
     * @param string $uuid
     * @return UserIdentity
     */
    public function setUuid(string $uuid): UserIdentity
    {
        $this->uuid = $uuid;
        return $this;
    }

    /**
     * @return string
     */
    public function getEmail(): string
    {
        return $this->email;
    }

    /**
     * @param string $email
     * @return UserIdentity
     */
    public function setEmail(string $email): UserIdentity
    {
        $this->email = $email;
        return $this;
    }


}