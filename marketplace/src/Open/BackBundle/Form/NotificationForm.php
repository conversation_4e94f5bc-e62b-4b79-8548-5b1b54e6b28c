<?php
/**
 * Created by PhpStorm.
 * User: PPH04712
 * Date: 29/01/2018
 * Time: 09:44
 */

namespace Open\BackBundle\Form;


use AppBundle\Entity\Node;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\CollectionType;
use Symfony\Component\Form\Extension\Core\Type\EmailType;
use Symfony\Component\Form\Extension\Core\Type\HiddenType;
use Symfony\Component\Form\Extension\Core\Type\SubmitType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

class NotificationForm extends AbstractType
{

    const LABEL = 'label';

    public function buildForm(FormBuilderInterface $builder, array $options)
    {

        // only the value can be changed from this form
        $builder
            ->add('id', HiddenType::class)
            ->add(
                'slug',
                TextType::class,
                array(
                    'label' => 'back.notification.edit.slug',
                    'attr' => array (
                        'readonly' => true
                    )
                )
            )
            ->add(
                "email",
                EmailType::class,
                array('label' => 'back.notification.edit.test',
                    'mapped' => false
                )
            )
            ->add(
                'content',
                CollectionType::class,
                array(
                    'entry_type' => NodeContentForm::class,
                    'by_reference' => false
                )
            )->add("saveAndSend",
                SubmitType::class,
                 array(
                     'label' => 'back.notification.edit.send_button'
                 ));
    }

    public function configureOptions(OptionsResolver $resolver)
    {
        $resolver->setDefaults([
            'translation_domain' => 'AppBundle',
            'data_class' => Node::class,
            'validation_groups' => array('registration')
        ]);
    }



}