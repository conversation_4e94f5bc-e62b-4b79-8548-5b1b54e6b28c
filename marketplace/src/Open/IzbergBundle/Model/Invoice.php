<?php

namespace Open\IzbergBundle\Model;

use <PERSON><PERSON>\Serializer\Annotation as JSM;
use <PERSON><PERSON>\Serializer\Annotation\Type;

/**
 * Object to map an izberg invoice object
 * @JSM\ExclusionPolicy("none")
 */
class Invoice
{
    /**
     * @Type("integer")
     */
    private $id = 0;

    /**
     * @TYPE("string")
     */
    private $pdf_file;

    /**
     * @TYPE("string")
     */
    private $id_number;

    /**
     * @TYPE("string")
     */
    private $issuer_name;

    /**
     * @TYPE("string")
     */
    private $created_on;

    /**
     * @TYPE("string")
     */
    private $orderId;

    /**
     * @TYPE("string")
     */
    private $numOrder;

    /**
     * @TYPE("double")
     */
    private $total_amount_with_taxes;

    /**
     * @TYPE("string")
     */
    private $currency;

    /**
     * @TYPE("string")
     */
    private $due_on;

    /**
     * @TYPE("double")
     */
    private $remaining_amount;

    /**
     * @var IzbergUser
     *
     * @Type("Open\IzbergBundle\Model\IzbergUser")
     */
    private $receiver;

    /**
     * @Type("string")
     */
    private $payment_status;

    /**
     * @Type("string")
     */
    private $status;

    /**
     * @Type("ArrayCollection<Open\IzbergBundle\Model\InvoiceLine>")
     */
    private $invoice_lines;

    /**
     * @return mixed
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * @param mixed $id
     */
    public function setId($id): void
    {
        $this->id = $id;
    }

    /**
     * @return mixed
     */
    public function getPdfFile()
    {
        return $this->pdf_file;
    }

    /**
     * @param mixed $pdf_file
     */
    public function setPdfFile($pdf_file): void
    {
        $this->pdf_file = $pdf_file;
    }

    /**
     * @return mixed
     */
    public function getIdNumber()
    {
        return $this->id_number;
    }

    /**
     * @param mixed $id_number
     */
    public function setIdNumber($id_number): void
    {
        $this->id_number = $id_number;
    }

    /**
     * @return mixed
     */
    public function getIssuerName()
    {
        return $this->issuer_name;
    }

    /**
     * @param mixed $issuer_name
     */
    public function setIssuerName($issuer_name): void
    {
        $this->issuer_name = $issuer_name;
    }

    /**
     * @return mixed
     */
    public function getCreatedOn()
    {
        return $this->created_on;
    }

    /**
     * @param mixed $created_on
     */
    public function setCreatedOn($created_on): void
    {
        $this->created_on = $created_on;
    }

    /**
     * @return mixed
     */
    public function getOrderId()
    {
        return $this->orderId;
    }

    /**
     * @param mixed $orderId
     */
    public function setOrderId($orderId): void
    {
        $this->orderId = $orderId;
    }

    /**
     * @return mixed
     */
    public function getNumOrder()
    {
        return $this->numOrder;
    }

    /**
     * @param mixed $numOrder
     */
    public function setNumOrder($numOrder): void
    {
        $this->numOrder = $numOrder;
    }

    /**
     * @return mixed
     */
    public function getTotalAmountWithTaxes()
    {
        return $this->total_amount_with_taxes;
    }

    /**
     * @param mixed $total_amount_with_taxes
     */
    public function setTotalAmountWithTaxes($total_amount_with_taxes): void
    {
        $this->total_amount_with_taxes = $total_amount_with_taxes;
    }

    /**
     * @return mixed
     */
    public function getCurrency()
    {
        return $this->currency;
    }

    /**
     * @param mixed $currency
     */
    public function setCurrency($currency): void
    {
        $this->currency = $currency;
    }

    /**
     * @return mixed
     */
    public function getDueOn()
    {
        return $this->due_on;
    }

    /**
     * @param mixed $due_on
     */
    public function setDueOn($due_on): void
    {
        $this->due_on = $due_on;
    }

    /**
     * @return mixed
     */
    public function getRemainingAmount()
    {
        return $this->remaining_amount;
    }

    /**
     * @param mixed $remaining_amount
     */
    public function setRemainingAmount($remaining_amount): void
    {
        $this->remaining_amount = $remaining_amount;
    }

    /**
     * @return IzbergUser
     */
    public function getReceiver(): IzbergUser
    {
        return $this->receiver;
    }

    /**
     * @param IzbergUser $receiver
     */
    public function setReceiver(IzbergUser $receiver): void
    {
        $this->receiver = $receiver;
    }

    /**
     * @return mixed
     */
    public function getPaymentStatus()
    {
        return $this->payment_status;
    }

    /**
     * @param mixed $payment_status
     */
    public function setPaymentStatus($payment_status): void
    {
        $this->payment_status = $payment_status;
    }

    /**
     * @return mixed
     */
    public function getStatus()
    {
        return $this->status;
    }

    /**
     * @param mixed $status
     */
    public function setStatus($status): void
    {
        $this->status = $status;
    }

    /**
     * @return mixed
     */
    public function getInvoiceLines()
    {
        return $this->invoice_lines;
    }

    /**
     * @param mixed $invoice_lines
     */
    public function setInvoiceLines($invoice_lines): void
    {
        $this->invoice_lines = $invoice_lines;
    }
}
