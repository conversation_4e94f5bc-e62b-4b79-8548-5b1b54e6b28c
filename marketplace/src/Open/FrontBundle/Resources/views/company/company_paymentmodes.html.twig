{% extends '@OpenFront/menu/buyer_menu.html.twig' %}
{% form_theme form 'Form/appli_layout.html.twig' %}
{% trans_default_domain 'AppBundle' %}
{% block title %}
    {{ 'payment_mode.title' | trans }}
{% endblock %}

{% block body %}

    <div class="Form CompanyForm PaymentModeForm{{ showBuyerMenu is defined and showBuyerMenu != true or showBuyerMenu is not defined ? ' user-active-background' }}">
        {{ form_start(form) }}
            <div class="form-title"><h1>{{ 'payment_mode.title'|trans }}</h1></div>

            <p class="info-payment">
                {{ 'payment_mode.info'|trans }}</br>
                {% if company.termpaymentMoneyTransfertEnabled %}
                    {{ 'payment_mode.enabled'|trans }}
                {% endif %}
                {%  if not company.termpaymentMoneyTransfertPending and not company.termpaymentMoneyTransfertEnabled %}
                    {{ 'payment_mode.click_button'|trans }}
                {% endif %}
            </p>

            <div class="invisible-checkboxes">
                <div class="form-row-container">
                    {{ form_row(form.prepaymentCreditcardEnabled) }}
                </div>
                <div class="form-row-container">
                    {{ form_row(form.prepaymentMoneyTransfertEnabled) }}
                </div>
                <div class="form-row-container">
                    {{ form_row(form.termpayment_moneytransfert_enabled) }}
                </div>
            </div>

            {% if company.termpaymentMoneyTransfertPending %}
                <h2>{{ 'payment_mode.pending'|trans }}</h2>
            {% endif %}

            {% if not company.termpaymentMoneyTransfertEnabled %}
                <div class="form-row Buttons">
                    <div class="col-md-12">
                        {% if disabled %}
                            <p>{{ 'payment.form.not_authorized' | trans }}</p>
                        {% else %}
                            {{ form_row(form.submit, {'attr': {'disabled': (disabled or company.termpaymentMoneyTransfertPending or company.termpaymentMoneyTransfertEnabled ? 'disabled' : false )}}) }}
                        {% endif %}
                    </div>
                </div>
            {% endif %}

        {{ form_end(form) }}

        {% if showBuyerMenu is defined and showBuyerMenu == true %}
            <div class="Form-group Buttons-group">
                <a href="{{ path('front.company.users') }}" class="button grey-button">{{ 'company.form.back'|trans }}</a>
                <a href="{{ path('homepage') }}" class="button">{{ 'company.form.finish'|trans }}</a>
            </div>
        {% endif %}
    </div>

{% endblock %}

