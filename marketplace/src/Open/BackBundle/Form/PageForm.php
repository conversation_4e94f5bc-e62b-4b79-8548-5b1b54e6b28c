<?php
/**
 * Created by PhpStorm.
 * User: CMA15450
 * Date: 08/03/2017
 * Time: 14:50
 */
namespace Open\BackBundle\Form;

use AppBundle\Entity\Node;
use AppBundle\Validator\Constraints\SlugHttp;
use AppBundle\Validator\Constraints\SlugHttpValidator;
use Doctrine\ORM\EntityRepository;
use Open\BackBundle\Form\Type\SlugType;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\CollectionType;
use Symfony\Component\Form\Extension\Core\Type\SubmitType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\Extension\Core\Type\FileType;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Form\FormBuilderInterface;
use AppB<PERSON>le\Entity\Setting;
use Open\BackBundle\Form\Type\SettingType;
use Symfony\Component\Form\Extension\Core\Type\HiddenType;

class PageForm extends AbstractType
{
	const LABEL = 'label';

    public function buildForm(FormBuilderInterface $builder, array $options)
    {

        // only the value can be changed from this form
        $builder
            ->add('id', HiddenType::class)
			->add(
				'slug',
				SlugType::class,
				array(
					'label' => 'node.form.slug.label'
				)
			)
			->add(
				'template',
				ChoiceType::class,
                array(
					self::LABEL => 'node.form.template.label',
					'choices' => array(
						'node.form.template.choices.default' => 'default',
						'node.form.template.choices.default_with_products' => 'defaultproducts',
						'node.form.template.choices.default_with_products_and_faq' => 'defaultproductsfaq',
						'node.form.template.choices.default_with_faq' => 'defaultfaq',
						'node.form.template.choices.fullwidth' => 'fullwidth',
						'node.form.template.choices.fullwidth_with_products' => 'fullwidthproducts',
						'node.form.template.choices.fullwidth_with_products_and_faq' => 'fullwidthproductsfaq',
						'node.form.template.choices.fullwidth_with_faq' => 'fullwidthfaq'
					),
					'choice_translation_domain' => 'AppBundle',
				)
			)
			->add(
				'author',
				EntityType::class,
				array(
					'class' => 'AppBundle\Entity\User',
					self::LABEL => 'node.form.author.label',
                    'query_builder' => function (EntityRepository $er) {

                        return $er->createQueryBuilder('u')
                        ->where("u.roles like '%ROLE_SUPER_ADMIN%' or u.roles like '%ROLE_OPERATOR%'");
                    },
                    'choice_translation_domain' => 'AppBundle',
				)
			)
			->add(
				'status',
				ChoiceType::class,
				array(
					self::LABEL => 'node.form.status.label',
					'choices' => array(
						'back.page.draft' => 'draft',
						'back.page.published' => 'published'
					),
                    'choice_translation_domain' => 'AppBundle',
				)
			)
			->add(
				'content',
				CollectionType::class,
				array(
					'entry_type' => NotificationContentForm::class,
					'by_reference' => false
				)
            )
			//->add('content.body', TextType::class)
        ;

    }

    public function configureOptions(OptionsResolver $resolver)
    {
        $resolver->setDefaults([
            'translation_domain' => 'AppBundle',
            'data_class' => Node::class,
			'validation_groups' => array('page')
        ]);
    }

}
