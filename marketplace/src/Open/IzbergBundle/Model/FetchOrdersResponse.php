<?php
/**
 * Created by PhpStorm.
 * User: PPH04712
 * Date: 19/05/2017
 * Time: 16:53
 */

namespace Open\IzbergBundle\Model;

use Doctrine\Common\Collections\ArrayCollection;
use <PERSON><PERSON>\Serializer\Annotation as JSM;
use <PERSON><PERSON>\Serializer\Annotation\Type;

/**
 * @JSM\ExclusionPolicy("none")
 */
class FetchOrdersResponse
{
    /**
     * @Type("Open\IzbergBundle\Model\Meta")
     * @var Meta
     */
    private $meta;

    /**
     * @Type("ArrayCollection<Open\IzbergBundle\Model\Order>")
     * @var ArrayCollection
     */
    private $objects;

    public function getObjects(): ArrayCollection
    {
        return $this->objects;
    }

    public function setObjects(ArrayCollection $objects)
    {
        $this->objects = $objects;
    }

    public function getMeta(): Meta
    {
        return $this->meta;
    }

    public function setMeta(Meta $meta): void
    {
        $this->meta = $meta;
    }

    public function hasOrder()
    {
        return ($this->objects->count());
    }

    public function first()
    {
        return $this->objects->first();
    }
}