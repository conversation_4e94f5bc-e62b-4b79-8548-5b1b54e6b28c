{% extends '@OpenFront/base.html.twig' %}
{% trans_default_domain 'AppBundle' %}

{% set quotesStatus = (status is defined and status is not empty) ? status : 'buyer_draft' %}

{% block title %} {{ 'quote.list.title' | trans }} {% endblock %}

{% block stylesheets %}
    <style>
        body[class*='Page--quotes'] .dataTables_wrapper .bottom {
            margin-top: 0;
        }

        body[class*="Page--quotes"] .quotes-table th {
            line-height: 1;
            padding: 10px !important;
            height: auto;
            white-space: nowrap;
        }

        body[class*="Page--quotes"] .quotes-table td:first-child {
            border-left: 1px solid #d7d7d7 !important;
        }

        body[class*='Page--quotes'] .dataTables_wrapper {
            font-size: 1rem;
        }

        body[class*='Page--quotes'] .dataTables_wrapper .dataTables_length {
            display: none;
        }

        body[class*='Page--quotes'] .dataTables_wrapper td.child {
            padding: 10px;
        }

        body[class*='Page--quotes'] .dataTables_wrapper ul {
            list-style: none;
            margin: 0;
            padding: 0;
        }

        body[class*='Page--quotes'] .dataTables_wrapper .dtr-title {
            font-weight: bold;
        }

        body[class*='Page--quotes'] .dataTables_wrapper tr:nth-child(even) {
            background-color: rgba(239, 239, 239, 0.5);
        }

        body[class*='Page--quotes'] .dataTables_wrapper .table td:last-child {
            background: transparent !important;
            border: none;
            border-top: 1px solid #dee2e6;
        }
        .quote-open-tab-icon {
            background: url('{{ asset('images/open-tab-icon.png') }}') no-repeat;
            position: absolute;
            top: 15px;
            right: 0;
        }
        .quote-tab-link {
            font-weight: bold;
            color: #353535;
            text-transform: uppercase;
            padding: 15px 0;
            border-top: 1px solid rgba(151, 151, 151, 0.59);
            display: block;
            position: relative;
        }
        body[class*='Page--quotes'] .dataTables_wrapper .dataTables_processing.card {
            height: auto;
        }
    </style>
{% endblock %}

{% block body %}
    {% block searchbar %}
        {% if formSearchBar is defined %}
            {% include('@OpenFront/component/search-bar.html.twig') %}
        {% else %}
            {{ render(controller("Open\\FrontBundle\\Controller\\SearchBarController::formAction")) }}
        {% endif %}
    {% endblock %}

    <div class="Page-inner">
        <div class="Page-content">
            <div style="text-align:center">
                <section class="account-header account-title">{{ 'quote.detail.page_title' | trans }}</section>
            </div>
            {# ############################# DESKTOP ############################# #}
            <div class="desktop-only">
                <ul class="tabs__items desktop-only">
                    {% for tab in tabs %}
                        <li class="tab__item no-js">
                            <a class="{% if tab.isActive %}active{% endif %}"
                               href="{{ path('front.quotes.list.status', {'status': tab.status }) }}">
                                {{ tab.label | trans | upper }}
                                {% if unreads[tab.status] is defined %}
                                    <span class="count-badge">{{ unreads[tab.status] }}</span>
                                {% endif %}
                            </a>
                        </li>
                    {% endfor %}
                </ul>

                <div class="tabs__content desktop-only">
                    <div class="tab__content active">
                        {% include '@OpenFront/quote/quotes_list_table.html.twig' %}
                    </div>
                </div>
            </div>

            {# ############################# MOBILE ############################# #}
            <div class="quotes-types-list mobile-only" style="border-bottom: 1px solid rgba(151, 151, 151, 0.59);">
                {% for tab in tabs %}
                    <a href="{{ path('front.quotes.list.status', {'status': tab.status}) }}" class="quote-tab-link">
                        <section class="quotes-type">
                            {{ tab.label | trans | upper }}
                            {% if unreads[tab.status] is defined %}
                                <span class="count-badge">{{ unreads[tab.status] }}</span>
                            {% endif %}
                        </section>
                        <div class="open-tab-icon quote-open-tab-icon"
                             style="{% if not tab.isActive %}transform: rotate(180deg){% endif %}"
                        ></div>
                    </a>
                    {% if tab.isActive %}
                        <div>
                            {% include '@OpenFront/quote/quotes_list_table.html.twig' with {'suffix': 'mobile'} %}
                        </div>
                    {% endif %}
                {% endfor %}
            </div>
        </div>
    </div>
{% endblock %}

{% block javascripts %}
    <script>
        $(document).ready(function () {
            const QUOTES_STATUS = '{{ quotesStatus }}';
            let tableDateColumn;
            switch (QUOTES_STATUS) {
                case 'validated':
                    tableDateColumn = "validation_date";
                    break;
                case 'buyer_cancelled':
                    tableDateColumn = "cancel_date";
                    break;
                default:
                    tableDateColumn = "creation_date";
                    break;
            }

            const quoteTable = $('.quotes-table').DataTable({
                responsive: {
                    details: {
                        display: $.fn.dataTable.Responsive.display.childRowImmediate,
                        type: 'column',
                        target: '',
                        orthogonal: 'responsive'
                    }
                },
                "processing": true,
                "serverSide": true,
                "ajax": "{{ path('front.quotes.invitation.list.json', {'status': quotesStatus}) }}",
                "columns": [
                    {"data": "quote_number", responsivePriority: -1},
                    {"data": "supplier", responsivePriority: -1},
                    {"data": tableDateColumn, responsivePriority: -1},
                    {"data": "subject", responsivePriority: -1},
                    {"data": "status", responsivePriority: -1},
                    {"defaultContent": "", responsivePriority: -1}
                ],
                order: [[0, 'desc']],
                columnDefs: [
                    {"target": [0], "visible": false, "searchable": false},
                    {targets: '_all', className: "sorting_disabled", orderable: false}
                ],
                "dom": '<"top"i>rt<"bottom"fpl><"clear">',
                "lengthMenu": [[10, 25, 50, 100], ["{{ 'table.perPage'|trans({'%num%':10}, 'AppBundle') }}", "{{ 'table.perPage'|trans({'%num%':25}, 'AppBundle') }}", "{{ 'table.perPage'|trans({'%num%':50}, 'AppBundle') }}", "{{ 'table.perPage'|trans({'%num%':100}, 'AppBundle') }}"]],
                "language": {
                    "info": "",
                    "search": "",
                    "lengthMenu": "_MENU_",
                    searchPlaceholder: "{{ 'form.invoice.search'|trans([], 'AppBundle') }}",
                    "paginate": {
                        "next": "<img src='/images/shape_right.svg'>",
                        "previous": "<img src='/images/shape_left.svg'>"
                    }
                },
                fnDrawCallback: function () {

                },
                rowCallback: function (row, data, index) {
                    if (data.unread) {
                        $(row).addClass("unread");
                    }
                }
            });

            $('#all-quotes-desktop tbody').on('click', 'tr', function () {
                const index = $('#all-quotes-desktop tbody tr').index($(this));
                let data = quoteTable.row(index).data();
                let link = $('#all-quotes-desktop').data("link");

                link = link.replace('quoteId', data['quote_id']);
                window.location.href = link;
            });

            $('#all-quotes-mobile tbody').on('click', 'tr', function () {
                let index = $('#all-quotes-mobile tbody tr').index($(this));
                index =  Math.floor(parseInt(index)/2);

                let data = quoteTable.row(index).data();
                let link = $('#all-quotes-mobile').data("link");

                link = link.replace('quoteId', data['quote_id']);
                window.location.href = link;
            });

            $(window).on('resize', function () {
                quoteTable.ajax.reload();
            });
        });
    </script>
{% endblock %}

