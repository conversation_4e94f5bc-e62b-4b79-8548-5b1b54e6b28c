<?php

namespace Open\IzbergBundle\Api;

use AppBundle\Entity\Country;
use Open\IzbergBundle\Dto\Request;
use Open\IzbergBundle\Model\Merchant;
use Psr\Log\LoggerAwareInterface;
use Symfony\Component\HttpClient\Exception\ClientException;

class CreateMerchant<PERSON>pi extends ApiAbstract implements LoggerAwareInterface
{
    public function updateMerchantLanguage(int $merchantId, string $language)
    {
        $language = strtolower($language);

        $data = [
            'languages' => [$language],
            'prefered_language' => $language,

        ];

        $this->updateMerchant($merchantId, $data);
    }

    public function updateMerchant(int $merchantId, array $data)
    {
        $this->httpClient->request(
            'PATCH',
            'merchant/' . $merchantId . '/',
            [
            'json' => $data
            ]
        );
    }

    public function getMerchantCompany(int $merchantId): ?array
    {
        $response = $this->httpClient
            ->request('GET', sprintf('merchant/%d/company/', $merchantId));

        return json_decode($response->getContent(), true);
    }

    public function updateCompany(int $companyId, string $countryId): void
    {
        $data = [
            'country' => sprintf('/v1/country/%d/', $countryId)
        ];

        $this->patch(
            (new Request(sprintf('company/%d/', $companyId)))
                ->setData($data)
        );

    }

    public function createMerchant(string $companyName, string $countryIzbergId, string $locale): string
    {
        $data = [
            "name" => $companyName,
            "company" => [
                "country" => sprintf('/v1/country/%s/', $countryIzbergId)
            ],
            "offer_type" => "product_offers",
            "languages"=>[$locale],
            "prefered_language"=>$locale

        ];


        return $this->post(
            (new Request('merchant/'))
                ->setData($data)
                ->setReturnData(true)
        );
    }

    public function getMerchant(int $merchantId): array
    {

        $response = $this->httpClient->request(
            'GET',
            'merchant/' . $merchantId . '/'
        );
        return json_decode($response->getContent(), true);
    }

    public function getById(int $merchantId): ?Merchant
    {
        $response = $this->httpClient->request(
            'GET',
            sprintf('merchant/%d', $merchantId),
            ['query' => ['return_data' => 'true']]
        );

        try {
            /** @var Merchant $merchant */
            $merchant = $this->serializer->deserialize($response->getContent(), Merchant::class, 'json');
            return $merchant;
        } catch (ClientException | \Exception $exception) {
            $this->logger->alert($exception->getMessage(), ['EXCEPTION HTTP CLIENT']);
            $this->logger->info($response->getContent(false), ['RESPONSE API']);

            return null;
        }
    }

    public function updateMerchantCurrency(int $merchantId, string $currency): void
    {
        $data = array(
            'currencies' => ["/v1/currency/" . $currency . "/"],
            'default_currency' => $currency,

        );

        $this->updateMerchant($merchantId, $data);
    }

    public function addMerchantAddress(
        int $merchantId,
        bool $billingAddress,
        bool $returnAddress,
        string $firstName,
        string $lastName,
        string $address,
        string $city,
        Country $country,
        string $contactEmail,
        string $phone
    ): void {
        $data = [
            'merchant' => "/v1/merchant/" . $merchantId . "/",
            "billing_address" => $billingAddress,
            "return_address" => $returnAddress,
            "country" => "/v1/country/" . $country->getIzbergId() . "/",
            "contact_first_name" => $firstName,
            "contact_last_name" => $lastName,
            "address" => $address,
            "city" => $city,
            "contact_email" => $contactEmail,
            "phone" => $phone
        ];
        $this->httpClient->request(
            'POST',
            'merchant_address/',
            [
                'json' => $data
            ]
        );
    }
}
