{% trans_default_domain 'AppBundle' %}

<div class="Product">

    {% set offerTitle =  '- Undefined -' %}
    {% if previousUrl is defined %}
        {% set frontOfferDetailUrl = path('front.offer.detail.short', {'ref': product.id, 'previousURL' : previousUrl}) %}
        {% if pagination is defined %}
            {% set frontOfferDetailUrl = path('front.offer.detail.short', {'ref': product.id , 'page' : pagination.current_page, 'previousURL' : previousUrl}) %}
        {% endif %}

        {% if product.name is not empty %}
            {% set offerTitle = product.name %}
            {% set frontOfferDetailUrl = path('front.offer.detail', {'productName' : product.name|e('url'), 'offerId': product.id, 'previousURL' : previousUrl}) %}
            {% if pagination is defined %}
                {% set frontOfferDetailUrl = path('front.offer.detail', {'productName' : product.name|e('url'), 'offerId': product.id , 'previousURL' : previousUrl}) %}
            {% endif %}
        {% endif %}
    {% else %}
        {% set frontOfferDetailUrl = path('front.offer.detail.short', {'ref': product.id}) %}

        {% if product.name is not empty %}
            {% set offerTitle = product.name %}
            {% set frontOfferDetailUrl = path('front.offer.detail', {'productName' : product.name|e('url'), 'offerId': product.id}) %}
        {% endif %}
    {% endif %}

    {% if product.picture is not empty  %}
        {% if frontOfferDetailUrl is not empty %}
            <a href="{{ frontOfferDetailUrl  }}">
                <div class="Product-image" style="background-image:url(' {{ asset ( product.picture ) }} ');"></div>
            </a>
         {% else %}
             <div class="Product-image" style="background-image:url(' {{ asset ( product.picture ) }} ');"></div>
         {% endif %}
    {% else %}
        <div class="Product-image" style="background-image:url(' {{ asset ( '/images/noVisual.png' ) }} ');"></div>
    {% endif %}

   <div class="Product-body">

        <div class="Product-reference">{# TODO add this field in the catalog of offer {{ product.sellerRef }}#}</div>

        <div class="tooltip-total tooltip-product-name">
           <div class="Product-name">
              <a href="{{ frontOfferDetailUrl }}">{{ offerTitle }}</a>
           </div>
            {% if offerTitle|length > 25 %}
               <div class="tooltiptext">
                   {{ offerTitle }}
               </div>
           {% endif %}
        </div>
      <div class="h-line"></div>
      <div class="Product-info">
         <div class="details">{{ product.merchant.name }}</div>
          {% if not product.priceGivenOnQuotation %}
              <div class="Product-price">{{ product.price | number_format('2', '.', ',') }}  {{ product.currency }}</div>
          {% endif %}
      </div>
       <div class="Product-merchant-rating">
           <p class='starrr' id="rate-merchant" data-rating="{{ product.merchant.rating }}"></p>
       </div>

      <div class="display-mobile">

      </div>
   </div>
</div>
