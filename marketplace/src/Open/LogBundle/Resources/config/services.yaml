services:
  _defaults:
    autowire: true
    autoconfigure: true

  Open\LogBundle\:
    resource: '../../*'

  Open\LogBundle\Service\LogService: '@logger.service'

  logger.service:
    class: Open\LogBundle\Service\LogService
    configurator: ['@logger.configurator', 'configure']

  logger.configurator:
    class: Open\LogBundle\Service\LoggerConfigurator
    arguments: [ '@logger.front', '@logger.cron' ]

  logger.front:
    class: Open\LogBundle\Service\Logger
    arguments: [ '@logger', '@security.token_storage' ]
    tags:
      - { name: monolog.logger, channel: analysed_logs }

  logger.cron:
    class: Open\LogBundle\Service\Logger
    arguments: [ '@logger', '@security.token_storage' ]
    tags:
      - { name: monolog.logger, channel: cron_analysed_logs }
