<?php

namespace AppBundle\Entity;

use DateTime;
use Doctrine\ORM\Mapping as ORM;
use Exception;
use Symfony\Component\Validator\Constraints as Assert;


/**
 * @ORM\Table(name="stat_offer")
 * @ORM\Entity()
 * @ORM\HasLifecycleCallbacks()
 */
class StatOffer
{

    /**
     * @var int
     *
     * @ORM\Column(name="id", type="integer")
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="AUTO")
     */
    private $id;

    /**
     * @ORM\Column(name="created_at", type="datetime", nullable=false)
     */
    protected DateTime $createdAt;

    /**
     * @ORM\Column(name="started_at", type="datetime", nullable=false)
     */
    protected DateTime $startDate;

    /**
     * @ORM\Column(name="ended_at", type="datetime", nullable=true)
     */
    protected ?DateTime $endDate = null;

    /**
     * @ORM\Column(name="min_last_sync", type="datetime", nullable=true)
     */
    private ?DateTime $minLastSync = null;

    /**
     * @ORM\Column(name="max_last_sync", type="datetime", nullable=true)
     */
    private ?DateTime $maxLastSync = null;


    /**
     *
     * @ORM\Column(name="index_name", type="string", length=255, nullable=false)
     */
    private $index;

    /**
     *
     * @ORM\Column(name="nb_offer", type="integer", nullable=false)
     */
    private $count;

    /**
     * @return int
     */
    public function getId(): int
    {
        return $this->id;
    }

    /**
     * @param int $id
     * @return StatOffer
     */
    public function setId(int $id): StatOffer
    {
        $this->id = $id;
        return $this;
    }

    /**
     * @return mixed
     */
    public function getIndex()
    {
        return $this->index;
    }

    /**
     * @param mixed $index
     * @return StatOffer
     */
    public function setIndex($index)
    {
        $this->index = $index;
        return $this;
    }

    /**
     * @return mixed
     */
    public function getCount()
    {
        return $this->count;
    }

    /**
     * @param mixed $count
     * @return StatOffer
     */
    public function setCount($count)
    {
        $this->count = $count;
        return $this;
    }


    /**
     * @ORM\PrePersist
     * @throws Exception
     */
    public function setCreatedAt()
    {
        $this->createdAt = new DateTime();
    }

    /**
     * @return DateTime
     */
    public function getStartDate(): DateTime
    {
        return $this->startDate;
    }

    /**
     * @param DateTime $startDate
     * @return StatOffer
     */
    public function setStartDate(DateTime $startDate): StatOffer
    {
        $this->startDate = $startDate;
        return $this;
    }

    /**
     * @return DateTime|null
     */
    public function getEndDate(): ?DateTime
    {
        return $this->endDate;
    }

    /**
     * @param DateTime|null $endDate
     * @return StatOffer
     */
    public function setEndDate(?DateTime $endDate): StatOffer
    {
        $this->endDate = $endDate;
        return $this;
    }

    /**
     * @return DateTime|null
     */
    public function getMinLastSync(): ?DateTime
    {
        return $this->minLastSync;
    }

    /**
     * @param DateTime|null $minLastSync
     * @return StatOffer
     */
    public function setMinLastSync(?DateTime $minLastSync): StatOffer
    {
        $this->minLastSync = $minLastSync;
        return $this;
    }

    /**
     * @return DateTime|null
     */
    public function getMaxLastSync(): ?DateTime
    {
        return $this->maxLastSync;
    }

    /**
     * @param DateTime|null $maxLastSync
     * @return StatOffer
     */
    public function setMaxLastSync(?DateTime $maxLastSync): StatOffer
    {
        $this->maxLastSync = $maxLastSync;
        return $this;
    }

}