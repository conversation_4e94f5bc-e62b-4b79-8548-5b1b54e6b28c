<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Maintenance en cours | Click&Buy</title>
    <style>
        body {
            font-family: sans-serif;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100vh;
            margin: 0;
            background-color: #f4f4f4;
            color: #333;
        }

        #logo {
            width: 300px;
            margin-bottom: 20px;
        }

        .message-container {
            text-align: center;
            padding: 20px;
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }

        .message {
            display: none;
        }

        .message.active {
            display: block;
        }
    </style>
</head>
<body>
<svg id="logo" data-name="Calque 1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 787.93 164.05">
    <defs>
        <style>
            .cls-1 {
                fill: #285aff;
            }

            .cls-2 {
                fill: #374649;
            }

            .cls-3 {
                fill: #ffc800;
            }

            .cls-4 {
                fill: red;
            }
        </style>
    </defs>
    <path class="cls-1" d="M837.92,191.54H815v43.1q0,10.13-2.21,16.29-4.28,11.37-15.81,11.38-9,0-12.09-8-1.74-4.53-1.74-13.49V191.54H748.32v19.05h11.6v30.22q0,18,3.72,27.28c4.43,11.18,13,14.5,25.82,14.5,17.28,0,16.64,0,26.46,0h22Z" transform="translate(-143.21 -153.54)" />
    <circle class="cls-1" cx="779.96" cy="306.58" r="11.01" transform="translate(55.03 756.3) rotate(-67.5)" />
    <circle class="cls-1" cx="685.11" cy="153.03" r="11.01" />
    <path class="cls-2" d="M184.3,263.36q6.64,0,10.79-3.65a12.79,12.79,0,0,0,4.32-9.71H221.9a30.65,30.65,0,0,1-5,16.72,32.76,32.76,0,0,1-13.4,11.79,42,42,0,0,1-18.8,4.19q-19.26,0-30.38-12.24t-11.13-33.83v-1.57q0-20.76,11-33.12t30.3-12.37q16.84,0,27,9.59t10.33,25.52H199.41a16.47,16.47,0,0,0-4.32-11.39,14.38,14.38,0,0,0-11-4.38q-8.38,0-12.66,6.11t-4.28,19.83v2.49q0,13.89,4.24,20T184.3,263.36Z" transform="translate(-143.21 -153.54)" />
    <path class="cls-2" d="M260.92,281H236.85V153.54h24.07Z" transform="translate(-143.21 -153.54)" />
    <path class="cls-2" d="M280.51,168a11.82,11.82,0,0,1,3.61-8.89q3.61-3.48,9.84-3.48t9.79,3.48a12.69,12.69,0,0,1,0,17.85,15.38,15.38,0,0,1-19.51,0A11.77,11.77,0,0,1,280.51,168ZM306,281H281.92V191.23H306Z" transform="translate(-143.21 -153.54)" />
    <path class="cls-2" d="M363.1,263.36a15.75,15.75,0,0,0,10.79-3.65,12.79,12.79,0,0,0,4.32-9.71H400.7a30.65,30.65,0,0,1-5,16.72,32.76,32.76,0,0,1-13.4,11.79,42,42,0,0,1-18.8,4.19q-19.26,0-30.38-12.24T322,236.63v-1.57q0-20.76,11-33.12t30.3-12.37q16.84,0,27,9.59t10.33,25.52H378.21a16.47,16.47,0,0,0-4.32-11.39,14.4,14.4,0,0,0-11-4.38q-8.37,0-12.65,6.11T346,234.85v2.49q0,13.89,4.23,20T363.1,263.36Z" transform="translate(-143.21 -153.54)" />
    <path class="cls-2" d="M447,245l-8.63,8.63V281h-24V153.54h24v70.64l4.65-6,23-27h28.8l-32.45,37.43L497.66,281H470.1Z" transform="translate(-143.21 -153.54)" />
    <path class="cls-3" d="M524.22,248.5A27.76,27.76,0,0,1,528.91,233q4.7-7.14,17.23-15.85a84.56,84.56,0,0,1-8.47-13.58,30,30,0,0,1-3.07-13.24q0-14.1,8.92-22.95t24-8.84q13.53,0,22.21,8a26.3,26.3,0,0,1,8.67,20.09q0,14.44-14.61,25.49l-9.29,6.72,20.83,24.24a46.12,46.12,0,0,0,4.9-21.25h20.42q0,23.56-10.87,38.18l18.09,21H600.67l-6.22-7.14q-13.37,8.81-30.71,8.8-17.94,0-28.72-9.42T524.22,248.5Zm40.68,14.86a30.48,30.48,0,0,0,16.35-4.8l-23.82-27.49-1.75,1.24q-7.38,6.3-7.38,14.82a15.67,15.67,0,0,0,4.6,11.72Q557.51,263.37,564.9,263.36ZM556.1,190q0,6.22,7.3,15.69l6.4-4.23a21.78,21.78,0,0,0,6.76-6.14,13.19,13.19,0,0,0,1.87-7.14,9.93,9.93,0,0,0-3.07-7.23,11.81,11.81,0,0,0-16.15.25A12.38,12.38,0,0,0,556.1,190Z" transform="translate(-143.21 -153.54)" />
    <path class="cls-2" d="M742.37,237q0,21.57-9.21,33.65T707.43,282.7q-14.61,0-23.33-11.2L683,281H661.44V153.54h24v45.74q8.3-9.72,21.83-9.71,16.44,0,25.78,12.08t9.33,34Zm-24-1.75q0-13.6-4.31-19.88t-12.87-6.27q-11.46,0-15.77,9.38V253.9q4.4,9.46,15.94,9.46t15.27-11.45Q718.38,246.42,718.38,235.22Z" transform="translate(-143.21 -153.54)" />
    <path class="cls-2" d="M888.81,247.09l16.6-55.86h25.73L895,295l-2,4.73q-8,17.59-26.56,17.6a37.76,37.76,0,0,1-10.62-1.58V297.56l3.65.08q6.81,0,10.17-2.07a13.65,13.65,0,0,0,5.27-6.89l2.82-7.39-31.46-90.06h25.82Z" transform="translate(-143.21 -153.54)" />
    <polygon class="cls-4" points="305.97 111.11 337.15 154.61 315.05 152.43 296 163.83 305.97 111.11" />
</svg>

<div class="message-container">
    <p class="message" data-lang="fr">Click&Buy sera indisponible lundi 30 juin de 11h à 15h en raison d’une mise à jour technique.</p>
    <p class="message" data-lang="en">Click&Buy will be unavailable on Monday June 30th from 11 AM to 15 PM due to a technical upgrade.</p>
    <p class="message" data-lang="es">Click&Buy no estará disponible el lunes 30 de junio de 11 a 15 horas debido a una actualización técnica.</p>
</div>

<script>
    const lang = navigator.language || navigator.userLanguage;
    const messages = document.querySelectorAll('.message');

    // Langues supportées et leur correspondance
    const supportedLanguages = {
        'fr': 'fr',
        'en': 'en',
        'es': 'es',
    };

    let activeLang = 'en';
    for (const code in supportedLanguages) {
        if (lang.startsWith(code)) {
            activeLang = supportedLanguages[code];
            break;
        }
    }

    messages.forEach(message => {
        if (message.dataset.lang === activeLang) {
            message.classList.add('active');
        }
    });
</script>
</body>
</html>