<?php

namespace Open\IdealBundle\Model;

class ParentLocation
{
    private ?string $Id = null;
    private ?string $Name = null;
    private ?string $Name_fr = null;
    private ?string $Uid = null;
    private ?ParentLocation $ParentLocation = null;

    /**
     * @return string|null
     */
    public function getId(): ?string
    {
        return $this->Id;
    }

    /**
     * @param string|null $Id
     * @return ParentLocation
     */
    public function setId(?string $Id): ParentLocation
    {
        $this->Id = $Id;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getName(): ?string
    {
        return $this->Name;
    }

    /**
     * @param string|null $Name
     * @return ParentLocation
     */
    public function setName(?string $Name): ParentLocation
    {
        $this->Name = $Name;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getUid(): ?string
    {
        return $this->Uid;
    }

    /**
     * @param string|null $Uid
     * @return ParentLocation
     */
    public function setUid(?string $Uid): ParentLocation
    {
        $this->Uid = $Uid;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getNameFr(): ?string
    {
        return $this->Name_fr;
    }

    /**
     * @param string|null $Name_fr
     * @return ParentLocation
     */
    public function setNameFr(?string $Name_fr): ParentLocation
    {
        $this->Name_fr = $Name_fr;
        return $this;
    }

    /**
     * @return ParentLocation|null
     */
    public function getParentLocation(): ?ParentLocation
    {
        return $this->ParentLocation;
    }

    /**
     * @param ParentLocation|null $ParentLocation
     * @return ParentLocation
     */
    public function setParentLocation(?ParentLocation $ParentLocation): ParentLocation
    {
        $this->ParentLocation = $ParentLocation;
        return $this;
    }
    

}