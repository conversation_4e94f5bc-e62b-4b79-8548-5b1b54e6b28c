<?php

namespace Open\FrontBundle\Service;

use Symfony\Component\String\Slugger\AsciiSlugger;

class SuggestionMapper
{
    public static function elasticToDomain(
        array $hits,
        ?string $search,
        string $locale
    ): array {

        $suggestionCategoryP = new SuggestionCategory();
        $suggestionCategoryP->setCategoryName(SuggestionCategory::TYPE_PRODUCT);
        $suggestionsP = [];
        foreach ($hits as $hit) {
            if (array_key_exists("name",$hit['_source'])) {
                $suggestion = new SuggestionResult();
                $suggestion->setType(SuggestionCategory::TYPE_PRODUCT);
                $suggestion->setId($hit['_source']['id']);
                $name = $hit['_source']['name'];
                $suggestion->setTerm($name);
                $suggestion->setImage($hit['_source']['default_image']);
                $suggestion->setNameSlug($name);

                $suggestionsP [] = $suggestion;
            }
        }

        $suggestionCategoryP->setSuggestionResults($suggestionsP);

        return [$suggestionCategoryP];
    }

    public static function elasticAggToDomain(
        array $subAgg,
        string $categoryName,
        string $idKey,
        string $nameKey
    ): ?SuggestionCategory {
        $suggestionCategory = new SuggestionCategory();
        $suggestionCategory->setCategoryName($categoryName);
        $suggestions = [];
        foreach ($subAgg[$idKey]["buckets"] as $bucket) {
            $id = $bucket["key"];
            $name = $bucket[$nameKey] ["buckets"][0]["key"];
            $suggestion = new SuggestionResult();
            $suggestion->setType($categoryName);
            $suggestion->setTerm($name);
            $suggestion->setId($id);
            $suggestions[] = $suggestion;
        }
        $suggestionCategory->setSuggestionResults($suggestions);
        return $suggestionCategory;
    }

}
