@import "../variables";
@import "~breakpoint-sass/stylesheets/breakpoint";

.Disclaimer {
    background: $PRIMARY_COLOR;
    color: $WHITE;

    &-inner {
        max-width: $SITE_WIDTH_WIDE;
        margin: 0 auto;
        padding: 40px 0 30px 0;
    }

    &-section {
        text-align: center;

        margin-bottom: 40px;

        h2 {
            font-size: 23px;
            color: $WHITE;
            margin-bottom: 20px;
        }

        p {
            font-size: 15px;
            margin: 5px 0;
        }
    }

    .Disclaimer--video {
        width: 100%;
        margin-top: 20px;

        button {
            margin: 30px auto 20px auto;
            background: $APPLI_DARK_YELLOW;
            width: 110px;
            height: 110px;
            border-radius: 50%;
            position: relative;


            .Icon {
                width: 50px;
                height: 50px;
                fill: $WHITE;
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-20px, -50%);
            }

            span {
                white-space: nowrap;
                position: absolute;
                top: 100%;
                margin-top: 10px;
                left: 50%;
                transform: translateX(-50%);
            }

            &:hover {
                background: $WHITE;

                span {
                    color: $APPLI_DARK_YELLOW;
                }

                .Icon {
                    fill: $APPLI_DARK_YELLOW;
                }
            }
        }
    }

    @include breakpoint($BREAKPOINT_DESKTOP) {

        &-inner {
            display: flex;
            justify-content: space-between;
            flex-wrap: wrap;
            padding-bottom: 60px;
        }

        &-section {
            width: 33%;
            margin-bottom: 0;

            h2 {
                margin-bottom: 30px;
            }

        }
    }
}