<?php

namespace AppBundle\Entity;

class SearchFacetValue
{
    /**
     * @var string
     */
    private $value;

    /**
     * @var string
     */
    private $label;

    /**
     * @var array
     */
    private $linkParameters;

    /**
     * @var int
     */
    private $total;

    public function getValue(): string
    {
        return $this->value;
    }

    public function setValue(string $value): self
    {
        $this->value = $value;

        return $this;
    }

    public function getLabel(): string
    {
        return $this->label;
    }

    public function setLabel(string $label): self
    {
        $this->label = $label;

        return $this;
    }

    public function getTotal(): int
    {
        return $this->total;
    }

    public function setTotal(int $total): self
    {
        $this->total = $total;

        return $this;
    }

    public function getLinkParameters(): array
    {
        return $this->linkParameters;
    }

    public function setLinkParameters(array $linkParameters): self
    {
        $this->linkParameters = $linkParameters;

        return $this;
    }
}
