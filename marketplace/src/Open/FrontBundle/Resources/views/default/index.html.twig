{% extends '@OpenFront/base.html.twig' %}
{% trans_default_domain 'AppBundle' %}

{% block title %}{{ 'home.title'|trans }}{% endblock %}

{% block meta_description %}{{ 'home.description'|trans }}{% endblock %}

{% block body %}

    {% include ('@OpenFront/merchant/alert_merchant.html.twig') %}

    {% include ('@OpenFront/home/<USER>') %}

    {% include '@OpenFront/home/<USER>' with { class: '' }%}

    <div class="Homepage-why-sell">
        <h1>{{ 'home.why_sell.title'|trans }}</h1>
        <div class="why-container">
            <div class="why-bloc">
                <svg class="Icon">
                    <use xlink:href="#icon-why-simple"></use>
                </svg>
                <h2>
                    {{ 'home.why_sell.simple'|trans }}
                </h2>
                <p>{{ 'home.why_sell.simple_text'|trans }}</p>
            </div>
            <div class="why-bloc">
                <svg class="Icon">
                    <use xlink:href="#icon-why-fast"></use>
                </svg>
                <h2>
                    {{ 'home.why_sell.efficiency'|trans }}
                </h2>
                <p>{{ 'home.why_sell.efficiency_text'|trans }}</p>
            </div>
            <div class="why-bloc">
                <svg class="Icon">
                    <use xlink:href="#icon-why-freedom"></use>
                </svg>
                <h2>
                    {{ 'home.why_sell.visibility'|trans }}
                </h2>
                <p>{{ 'home.why_sell.visibility_text'|trans }}</p>
            </div>
            </div>
    </div>

    {% include ('@OpenFront/home/<USER>') %}

{% endblock %}

{% block javascripts %}
    <script src="{{ asset('js/rater.min.js') }}"></script>
    <script type="text/javascript">
        $('.starrr').each(function() {
            var rating = $(this).data('rating');
            $(this).rate({
                rating: rating,
                max_value: 5,
                step_size: 0.5,
                readonly: true,
                initial_value: rating,
            });
        });
    </script>
    <script type="text/javascript">

      /**
       * Initialize homepage slider with the correct ratio
       * @param screenWidth
       * @param screenHeight
       * @param navHeight
       * @param orientation
       * @param sliderOptions
       */

      [].forEach.call(document.querySelectorAll('.sp-image[data-source]'), function(img) {
          img.setAttribute('src', img.getAttribute('data-source'));
          img.onload = function() {
              img.removeAttribute('data-source');
          };
      });

      document.addEventListener('DOMContentLoaded', function () {
        var $w = $(window);
        var screenWidth = $('body').width();
        var screenHeight = $w.height();
        var navHeight = $('#js-header').height();
        var $slider;
        var orientation = window.UI.Utils.getScreenOrientation();

        var sliderOptions = {
          width : '100%',
          slideDistance : 0,
          fade : true,
          fadeOutPreviousSlide : true,
          fadeDuration: 1000,
          fadeArrows : true,
          buttons: true,
          autoplayDelay : 5000
        };

        window.UI.BrowserDetect.init();

        if (window.UI.BrowserDetect.getBrowser() === 'IE' && window.UI.BrowserDetect.getVersion() === 11 ) {
            $('html').addClass('ie11');
        }

        var $arrow = $('#js-scroll-arrow');

        var checkScroll = function () {
          // If user scrolled a bit then stop listening and hide the arrow
          if ($w.scrollTop() > 100) {
            $(window).off('scroll', checkScroll);
            $arrow.fadeOut('fast');
          }
        };

        // Bind click event on the arrow
        $arrow.on('click', function () {

          // Scroll a bit
          $('html, body').animate({ scrollTop:  $arrow.offset().top + 30 }, 'slow');

          // kill scroll listener
          $w.off('scroll', checkScroll);

          // hide the arrow
          $arrow.fadeOut('fast');
        });

        // Bind click event on the window to hide the arrow
        $w.on('scroll', checkScroll);

        // Listen for resize changes
        $w.on("resize", function() {
            var $w = $(window);
            var screenWidth = $('body').width();
            var screenHeight = $w.height();
            var navHeight = $('#js-header').height();
            var $slider;
            var orientation = window.UI.Utils.getScreenOrientation();

            var sliderOptions = {
                width : '100%',
                slideDistance : 0,
                fadeArrows : true,
                buttons: false,
                autoplayDelay : 5000
            };
            initSlider(screenWidth, screenHeight, navHeight, orientation, sliderOptions);
        });

          // Show video modal when clicking onthe slides buttons
          $('.js-video-play').on('click', function (ev) {
              var $videoContent;

              ev.preventDefault();

              var src = $(ev.currentTarget).data('videoSrc');

              $videoTemplate = Handlebars.compile($('#js-video-tpl').html());


              if (screenWidth >= 960 && UI.Utils.getScreenOrientation() === 'landscape') {

                  $videoContent = $videoTemplate({
                      'src' : src,
                      'width' : 960,
                      'height' : 540
                  });

              } else {

                  var height = (screenWidth * 9) / 16;

                  if (height >= screenHeight) {
                      height = screenHeight - 50;
                  }

                  $videoContent = $videoTemplate({
                      'src': src,
                      'width' : screenWidth,
                      'height' : height
                  });


              }

              $slider.stopAutoplay();

              UI.Modal.show('js-video-modal', 'Modal--homepageVideo', $videoContent, true, null, function () {
                  $slider.startAutoplay();
              });

          });

      });
    </script>
{% endblock %}