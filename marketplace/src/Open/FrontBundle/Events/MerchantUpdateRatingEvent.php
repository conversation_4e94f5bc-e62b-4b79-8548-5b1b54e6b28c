<?php

namespace Open\FrontBundle\Events;

use Symfony\Contracts\EventDispatcher\Event;

class MerchantUpdateRatingEvent extends Event
{
    public const EVENT_NAME = "MERCHANT_UPDATE_RATING_EVENT";

    private int $merchantId;

    public function getMerchantId(): int
    {
        return $this->merchantId;
    }

    public function setMerchantId(int $merchantId): MerchantUpdateRatingEvent
    {
        $this->merchantId = $merchantId;
        return $this;
    }
}
