<?php

namespace AppBundle\Entity;

use Doctrine\ORM\Mapping as ORM;

/**
 * MerchantOrder
 *
 * @ORM\Table(name="merchant_order")
 * @ORM\Entity(repositoryClass="AppBundle\Repository\MerchantOrderRepository")
 * @ORM\HasLifecycleCallbacks()
 *
 */
class MerchantOrder
{
    public const STATUS_PENDING_MANAGER_VALIDATION = 'pending-manager-validation';

    public const STATUS_PENDING_SUPPLIER_VALIDATION = 'pending-supplier-validation';

    public const STATUS_CONFIRMED_BY_SUPPLIER = 'confirmed-by-supplier';

    public const STATUS_CANCELLED = 'cancelled';

    public const STATUS_ABANDONED = 'abandoned';

    /**
     * @var int
     *
     * @ORM\Id
     * @ORM\Column(name="id", type="integer")
     */
    private $id;

    /**
     * @var int
     *
     * @ORM\Column(name="merchant_id", type="integer")
     */
    private $merchantId;

    /**
     * @var string
     *
     * @ORM\Column(name="merchant_name", type="string", nullable=true)
     */
    private $merchantName;

    /**
     * @var float
     * @ORM\Column(name="amount", type="float")
     */
    private $amount;

    /**
     * @var float
     * @ORM\Column(name="amount_delivery", type="float")
     */
    private $amountCurrencyDelivery;

    /**
     * @var float
     * @ORM\Column(name="amount_mkp", type="float")
     */
    private $amountCurrencyMarketPlace;

    /**
     * @var float
     *
     * @ORM\Column(name="amount_vat_included", type="float")
     */
    private $amountVatIncluded;

    /**
     * @var string
     *
     * @ORM\Column(name="currency", type="string", length=20)
     */
    private $currency;

    /**
     * @var int
     * @ORM\Column(name="izberg_status", type="integer")
     */
    private $izbergStatus;

    /**
     * @var string
     * @ORM\Column(name="status", type="string", length=50)
     */
    private $status;

    /**
     * @var MetaCart
     *
     * @ORM\ManyToOne(targetEntity="AppBundle\Entity\MetaCart")
     */
    private $metaCart;

    /**
     * @var User
     *
     * @ORM\ManyToOne(targetEntity="AppBundle\Entity\User")
     */
    private $buyer;

    /**
     * @var string|null $costCenter
     *
     * @ORM\Column(name="cost_center", type="string", nullable=true)
     */
    private $costCenter;
    /**
     * @var \DateTimeImmutable
     *
     * @ORM\Column(type = "datetime_immutable", name="created_at")
     */
    private $createdAt;

    /**
     * @var \DateTimeImmutable
     *
     * @ORM\Column(type="datetime_immutable", name="manager_validation_date", nullable=true)
     */
    private $managerValidationDate;

    /**
     * @var \DateTimeImmutable
     *
     * @ORM\Column(type = "datetime_immutable", name="supplier_confirm_at", nullable=true)
     */
    private $supplierConfirmAt;

    /**
     * @var \DateTimeImmutable
     *
     * @ORM\Column(type = "datetime_immutable", name="cancelled_at", nullable=true)
     */
    private $cancelledAt;

    /**
     * @var string
     * @ORM\Column(name="tracking_number", type="string", length=50, nullable=true)
     */
    private $trackingNumber;

    /**
     * @var int
     *
     * @ORM\Column(name="order_id", type="integer")
     */
    private $orderId;

    /**
     * @var  User
     *
     * @ORM\ManyToOne(targetEntity="AppBundle\Entity\User")
     */
    private $cancelBy;

    /**
     * @var float
     * @ORM\Column( type="decimal", name="currency_rate_country", precision=25, scale=15, nullable=false)
     */
    private $currencyRateCountryOfdelivery;

    /**
     * @var float
     * @ORM\Column( type="decimal", name="currency_rate_mkp",precision=25, scale=15, nullable=false)
     */
    private $currencyRateMarketPlace;

    /**
     * @var int
     */
    private $orderIdNumber;

    /**
     * @var bool
     *
     * @ORM\Column(name="gift", type="boolean", nullable=false)
     */
    private $gift;

    /**
     * @var bool
     *
     * @ORM\Column(name="quote", type="boolean", nullable=false)
     */
    private $quote = false;

    /**
     * @var float
     *
     * @ORM\Column(name="shipping", type="float")
     */
    private $shipping;

    /**
     * just to print info in reports, as it must be the organisation a the date of merchant order
     * @var string|null
     * @ORM\Column(name="user_invoice_entity", type="string", nullable=true)
     */
    private $userInvoiceEntity;


    /**
     * just to print info in reports, as it must be the organisation a the date of merchant order
     * @var string|null
     * @ORM\Column(name="organization", type="string", nullable=true)
     */
    private $organisation;

    /**
     * just to print info in reports, as it must be the organisation a the date of merchant order
     * @var string|null
     * @ORM\Column(name="cancel_reason", type="text", nullable=true)
     */
    private $cancelReason;

    /**
     * @var Site|null
     * @ORM\ManyToOne(targetEntity="AppBundle\Entity\Site")
     * @ORM\JoinColumn(nullable=true)
     */
    private $site;

    /**
     * @var bool
     *
     * @ORM\Column(name="risk", type="boolean", nullable=true, options={"default":null})
     */
    private $risk;

    /**
     * @return int|null
     */
    public function getOrderIdNumber(): ?int
    {
        return $this->orderIdNumber;
    }

    /**
     * @param int $orderIdNumber
     */
    public function setOrderIdNumber(?int $orderIdNumber): void
    {
        $this->orderIdNumber = $orderIdNumber;
    }



    public function __construct()
    {
        $this->createdAt = new \DateTimeImmutable();
    }

    public function getId(): int
    {
        return $this->id;
    }

    public function setId(int $id): self
    {
        $this->id = $id;
        return $this;
    }

    public function getMerchantId(): int
    {
        return $this->merchantId;
    }

    public function setMerchantId(int $merchantId): self
    {
        $this->merchantId = $merchantId;
        return $this;
    }

    public function getMerchantName(): string
    {
        return $this->merchantName;
    }

    public function setMerchantName(string $merchantName): self
    {
        $this->merchantName = $merchantName;
        return $this;
    }

    public function getAmount(): ?float
    {
        return $this->amount;
    }

    public function setAmount(float $amount): self
    {
        $this->amount = $amount;
        return $this;
    }

    public function getAmountVatIncluded(): ?float
    {
        return $this->amountVatIncluded;
    }

    public function setAmountVatIncluded(float $amountVatIncluded): self
    {
        $this->amountVatIncluded = $amountVatIncluded;
        return $this;
    }

    public function getCurrency(): string
    {
        return $this->currency;
    }

    public function setCurrency(string $currency): self
    {
        $this->currency = $currency;
        return $this;
    }

    public function getIzbergStatus(): int
    {
        return $this->izbergStatus;
    }

    public function setIzbergStatus(int $izbergStatus): self
    {
        $this->izbergStatus = $izbergStatus;
        return $this;
    }

    public function getStatus(): string
    {
        return $this->status;
    }

    public function setStatus(string $status): self
    {
        if (!self::isValidStatus($status)) {
            throw new \InvalidArgumentException(
                sprintf(
                    'status "%s" not valid. [%s] statuses are valid',
                    $status,
                    implode(',', self::getValidStatuses())
                )
            );
        }
        $this->status = $status;
        return $this;
    }

    public function getMetaCart(): MetaCart
    {
        return $this->metaCart;
    }

    public function setMetaCart(MetaCart $metaCart): self
    {
        $this->metaCart = $metaCart;
        return $this;
    }

    public function getBuyer(): User
    {
        return $this->buyer;
    }

    public function setBuyer(User $buyer): self
    {
        $this->buyer = $buyer;
        return $this;
    }

    public function getCostCenter(): ?string
    {
        return $this->costCenter;
    }

    public function setCostCenter(?string $costCenter): self
    {
        $this->costCenter = $costCenter;
        return $this;
    }

    public function getCreatedAt(): \DateTimeImmutable
    {
        return $this->createdAt;
    }

    public function setCreatedAt(\DateTimeImmutable $createdAt): self
    {
        $this->createdAt = $createdAt;
        return $this;
    }

    public function getManagerValidationDate(): ?\DateTimeImmutable
    {
        return $this->managerValidationDate;
    }

    public function setManagerValidationDate(?\DateTimeImmutable $managerValidationDate): self
    {
        $this->managerValidationDate = $managerValidationDate;
        return $this;
    }

    public function getSupplierConfirmAt(): ?\DateTimeImmutable
    {
        return $this->supplierConfirmAt;
    }
    public function setSupplierConfirmAt(?\DateTimeImmutable $supplierConfirmAt): self
    {
        $this->supplierConfirmAt = $supplierConfirmAt;
        return $this;
    }

    public function getCancelledAt(): ?\DateTimeImmutable
    {
        return $this->cancelledAt;
    }

    public function setCancelledAt(?\DateTimeImmutable $cancelledAt): self
    {
        $this->cancelledAt = $cancelledAt;
        return $this;
    }

    public function isPendingForManagerValidation(): bool
    {
        return ($this->status === self::STATUS_PENDING_MANAGER_VALIDATION);
    }

    public function isPendingForSupplierValidation(): bool
    {
        return ($this->status === self::STATUS_PENDING_SUPPLIER_VALIDATION);
    }

    public function isCancelled(): bool
    {
        return ($this->status === self::STATUS_CANCELLED);
    }

    public function isAbandoned(): bool
    {
        return ($this->status === self::STATUS_ABANDONED);
    }

    public function isConfirmedBySupplier(): bool
    {
        return ($this->status === self::STATUS_CONFIRMED_BY_SUPPLIER);
    }

    public static function isValidStatus(string $status): bool
    {
        return in_array(
            $status,
            self::getValidStatuses()
        );
    }

    public function getTrackingNumber(): ?string
    {
        return $this->trackingNumber;
    }

    public function setTrackingNumber(?string $trackingNumber): self
    {
        $this->trackingNumber = $trackingNumber;
        return $this;
    }

    public function hasTrackingNumber(): bool
    {
        return (empty($this->trackingNumber));
    }

    public function getOrderId(): int
    {
        return $this->orderId;
    }

    public function setOrderId(int $orderId): self
    {
        $this->orderId = $orderId;
        return $this;
    }

    /**
     * @return User
     */
    public function getCancelBy(): ?User
    {
        return $this->cancelBy;
    }

    /**
     * @param User $cancelBy
     */
    public function setCancelBy(User $cancelBy): void
    {
        $this->cancelBy = $cancelBy;
    }

    public function isGift(): bool
    {
        return $this->gift;
    }
    /**
     * @return bool
     */
    public function isQuote(): bool
    {
        return $this->quote;
    }

    /**
     * @param bool $quote
     * @return self
     */
    public function setQuote(bool $quote): self
    {
        $this->quote = $quote;
        return $this;
    }

    public function setGift(bool $gift): self
    {
        $this->gift = $gift;
        return $this;
    }

    public static function getValidStatuses(): array
    {
        return [
            self::STATUS_ABANDONED,
            self::STATUS_CANCELLED,
            self::STATUS_CONFIRMED_BY_SUPPLIER,
            self::STATUS_PENDING_MANAGER_VALIDATION,
            self::STATUS_PENDING_SUPPLIER_VALIDATION
        ];
    }

    /**
     * @return float
     */
    public function getAmountCurrencyDelivery(): float
    {
        return $this->amountCurrencyDelivery;
    }

    /**
     * @param float $amountCurrencyDelivery
     */
    public function setAmountCurrencyDelivery(float $amountCurrencyDelivery): self
    {
        $this->amountCurrencyDelivery = $amountCurrencyDelivery;
        return $this;
    }

    /**
     * @return float
     */
    public function getAmountCurrencyMarketPlace(): float
    {
        return $this->amountCurrencyMarketPlace;
    }

    /**
     * @param float $amountCurrencyMarketPlace
     */
    public function setAmountCurrencyMarketPlace(float $amountCurrencyMarketPlace): self
    {
        $this->amountCurrencyMarketPlace = $amountCurrencyMarketPlace;
        return $this;
    }


    /**
     * @return float
     */
    public function getCurrencyRateCountryOfdelivery(): float
    {
        return $this->currencyRateCountryOfdelivery;
    }

    /**
     * @param float $currencyRateCountryOfdelivery
     */
    public function setCurrencyRateCountryOfdelivery(float $currencyRateCountryOfdelivery): self
    {
        $this->currencyRateCountryOfdelivery = $currencyRateCountryOfdelivery;
        return $this;
    }

    /**
     * @return float
     */
    public function getCurrencyRateMarketPlace(): float
    {
        return $this->currencyRateMarketPlace;
    }

    /**
     * @param float $currencyRateMarketPlace
     */
    public function setCurrencyRateMarketPlace(float $currencyRateMarketPlace): self
    {
        $this->currencyRateMarketPlace = $currencyRateMarketPlace;
        return $this;
    }

    /**
     * @return float
     */
    public function getShipping(): float
    {
        return $this->shipping;
    }

    /**
     * @param float $shipping
     */
    public function setShipping(float $shipping): self
    {
        $this->shipping = $shipping;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getUserInvoiceEntity(): ?string
    {
        return $this->userInvoiceEntity;
    }

    /**
     * @param string|null $userInvoiceEntity
     * @return MerchantOrder
     */
    public function setUserInvoiceEntity(?string $userInvoiceEntity): self
    {
        $this->userInvoiceEntity = $userInvoiceEntity;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getOrganisation(): ?string
    {
        return $this->organisation;
    }

    /**
     * @param string|null $organisation
     * @return MerchantOrder
     */
    public function setOrganisation(?string $organisation): self
    {
        $this->organisation = $organisation;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getCancelReason(): ?string
    {
        return $this->cancelReason;
    }

    /**
     * @param string|null $cancelReason
     */
    public function setCancelReason(?string $cancelReason): void
    {
        $this->cancelReason = $cancelReason;
    }

    /**
     * @return Site|null
     */
    public function getSite(): ?Site
    {
        return $this->site;
    }

    /**
     * @param Site|null $site
     */
    public function setSite(?Site $site): self
    {
        $this->site = $site;
        return $this;
    }

    /**
     * @return bool|null
     */
    public function isRisk(): ?bool
    {
        return $this->risk;
    }

    /**
     * @param bool|null $risk
     */
    public function setRisk(?bool $risk): self
    {
        $this->risk = $risk;
        return $this;
    }
}
