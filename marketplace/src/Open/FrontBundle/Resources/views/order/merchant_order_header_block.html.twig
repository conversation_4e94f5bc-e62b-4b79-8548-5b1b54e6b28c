{% set locale = app.request.locale %}
{% set shippingDelay = 2 %}
{% if order.merchantOrderEntity.metaCart.validatedAt is not null %}
    {% set shippingDate = order.merchantOrderEntity.metaCart.validatedAt|date_modify("+"~shippingDelay~"day")| localizeddate('short', 'none', locale) %}
{% endif %}
{% if order.merchantOrderEntity.metaCart.buyerShippingAddress %}
{#  Bad display **** 1  84 2 0 10 1 0  for +33 1 84 20 10 10  #}
{#    {% set splitPhone = order.merchantOrderEntity.metaCart.buyerShippingAddress.phone|split('', 2) %}#}
{#    {% set recipientPhone = splitPhone|join(' ') %}#}
    {% set recipientPhone = order.merchantOrderEntity.metaCart.buyerShippingAddress.phone %}
{% endif %}
{% trans_default_domain 'AppBundle' %}

{# Hide link back to orders list
<div class="detail-order-header-back">

    <section class="detail-order-header-back-link desktop-only">
        <a class="previous-page" href="{{ path('front.orders.list.status', {'status': order.merchantOrderEntity.status }) }}?rid={{ order.id }}"><i
                    class="arrow left"></i> {{ 'orders.detail.go_back'|trans }}</a>
    </section>

    <section class="detail-order-header-back-link mobile-only">
        <a class="previous-page"
           href="{{ path('front.orders.list.status', {'status': order.merchantOrderEntity.status, 'isMobile': true }) }}">
            <img alt="go back" src="{{ asset('images/red-arrow.svg') }}">
            <img alt="go back" src="{{ asset('images/red-arrow.svg') }}">
            {{ 'orders.list.back'|trans | upper }}
        </a>
    </section>
    <div style="text-align:center" class="mobile-only">
        <section class="account-header account-title">{{ 'orders.detail.mobile_title' | trans }}</section>
    </div>
</div>
 #}

<div class="detail-order-info">
    <section class="detail-order-info-col">
        {% set shippingAddress = order.merchantOrderEntity.metaCart.buyerShippingAddress %}
        {% if shippingAddress is not null %}
            <section class="detail-order-info-row">
                <span>{{ 'orders.detail.recipient' | trans }} : </span>
                <section>{% if shippingAddress.contact is not null %}{{ shippingAddress.contact }}{% endif %}</section>
            </section>
            <section class="detail-order-info-row">
                <span>{{ 'orders.detail.tel' | trans }} : </span>
                <section>{{ recipientPhone }}</section>
            </section>
            <section class="detail-order-info-row f-dc-m">
                <span>{{ 'orders.detail.shipping_address' | trans }} : </span>
                <section class="uppercase ">
                    {{ shippingAddress.address }}
                    {% if shippingAddress.address2 is not null %}{{ shippingAddress.address2 }}{% endif %}
                    ,
                    {{ shippingAddress.zipcode }}
                    {{ shippingAddress.city }}

                    {% set country = 'country.'~shippingAddress.country.code %}
                    {{ country | trans }}
                </section>
            </section>

            {% if shippingAddress.comment is not null and shippingAddress.comment|trim is not empty %}
            <section class="detail-order-info-row">
                <span>{{ 'orders.detail.comments' | trans }} : </span>
                <section>{{ shippingAddress.comment }}</section>
            </section>
            {% endif %}
        {% endif %}

        <!--  Statut : Validé par le fournisseur -->
        {% if order.merchantOrderEntity.confirmedBySupplier and order.merchantOrderEntity.trackingNumber %}
            <section class="detail-order-info-row">
                <span>{{ 'orders.detail.tracking_number' | trans }} : </span> {{ order.merchantOrderEntity.trackingNumber }}
            </section>
        {% endif %}
        <!--  Statut : Validé par le fournisseur -->
        {% if feedback is defined %}
            <section class="detail-order-info-row">
                <span>{{ 'orders.detail.total_without_vat'|trans }} : </span> {{ order.merchant.total }}
            </section>
        {% endif %}
        {% if order.merchantOrderEntity.metaCart.status == "REJECTED" %}
            <section class="detail-order-info-row">
                <span>{{ 'orders.detail.reason' | trans }} : </span> {{ order.merchantOrderEntity.metaCart.rejectReason }}
            </section>
        {% endif %}
        {% if order.merchantOrderEntity.metaCart.status == "CANCELLED" %}
            <section class="detail-order-info-row">
                <span>{{ 'orders.detail.reason_cancel' | trans }} : </span> {{ order.merchantOrderEntity.metaCart.cancelReason }}
            </section>
        {% endif %}
    </section>

    <section class="detail-order-info-col f-ai-fe">
        <section class="detail-order-info-row j-c-right">
            <span>{{ 'orders.detail.order_date'|trans }} : </span> {{ order.merchantOrderEntity.createdAt | localizeddate('short', 'none', locale) }}
        </section>
        <section class="detail-order-info-row">
            <span>{{ 'orders.detail.order_id' | trans }} : </span> {{ order.id }}
        </section>
        {% if order.merchantOrderEntity.confirmedBySupplier and order.merchantOrderEntity.trackingNumber %}
            <section class="detail-order-info-row">
                <span>{{ 'orders.detail.tracking_number' | trans }} : </span> {{ order.merchantOrderEntity.trackingNumber }}
            </section>
        {% endif %}
        {% if not order.merchantOrderEntity.isConfirmedBySupplier %}
            <section class="detail-order-info-row">
                <span>{{ 'orders.detail.cart_id' | trans }} : </span> {{ order.merchantOrderEntity.metaCart.id }}
            </section>
            <section class="detail-order-info-row">
                <span>{{ 'orders.detail.shipping_delay' | trans }} : </span> {{ shippingDelay }} {{ 'generic.day' | trans }}{{ shippingDelay > 1 ?'s':'' }}
            </section>
        {% else %}
            <section class="detail-order-info-row">
                <span>{{ 'orders.detail.shipping_date' | trans }} : </span> {{ shippingDate }}
            </section>
        {% endif %}

        <section class="detail-order-info-row f-dc-m">
            <span>{{ 'orders.detail.status' | trans }} : </span> {{ ('orders.header.'~order.merchantOrderEntity.status) | trans }}
        </section>
        {% if order.merchantOrderEntity.cancelled %}
        <section class="detail-order-info-row f-dc-m">
            <span>{{ 'orders.detail.cancel_reason' | trans }} : </span> {{ order.merchantOrderEntity.cancelReason | trans }}
        </section>
        {% endif %}
        {% if userIsMerchantOrderBuyer %}
        <section class="detail-order-info-btn j-c-right" style="text-align: right;">
            {% if order.merchantOrderEntity.pendingForManagerValidation or order.merchantOrderEntity.pendingForSupplierValidation %}
                <a href="#" onclick="event.stopPropagation(); window.UI.Modal.onCancel('{{ path('cart.details.cancel') }}', {{ order.merchantOrderEntity.metaCart.id }},{{ order.merchantOrderEntity.id }}, '{{ 'orders.modal.confirm_cancel'|trans({}, 'AppBundle')|raw }}', '{{ 'orders.modal.field.reminder'|trans({}, 'AppBundle')|raw }}', '{{ 'wishlist.item.delete.error'|trans({}, 'AppBundle')|raw }}', '{{ path('front.orders.list', {status:'pending-manager-validation'}) }}','{{ order.merchantOrderEntity.status }}', '{{ csrf_token('cart_cancel') }}')"
                   class="btn-white">{{ 'orders.detail.cancel_order' | trans | upper }}</a>
            {% endif %}
            {% if order.merchantOrderEntity.pendingForManagerValidation %}
                <a href="#" id="notify-manager-again"
                   class="btn-white">{{ 'orders.detail.notify_manager_again' | trans | upper }}</a>
            {% endif %}

            {#            {% if order.merchantOrderEntity.pendingForManagerValidation %}#}
            {#                <a href="#" class="btn-white">{{ 'orders.detail.cancel_order' | trans | upper }}</a>#}
            {#            {% endif %}#}
            {% if order.merchantOrderEntity.confirmedBySupplier %}
                <a href="{{ path('claim.order', {'merchantOrderId': order.merchantOrderEntity.id }) }}"
                   class="btn-white">{{ 'orders.detail.claim' | trans | upper }}</a>
            {% endif %}

            {# Reorder for all pages #}
            {% if not order.merchantOrderEntity.quote %}
                <a href="#" data-reorder="{{ path('front.order.reorder', {'merchantOrderId': order.id}) }}" data-checkReOrder="{{ path('front.order.reorder.check', {'merchantOrderId': order.id}) }}"
                   class="btn-white">{{ 'orders.detail.order_again' | trans | upper }}</a>
            {% endif %}

        </section>
        {% endif %}
    </section>
</div>
