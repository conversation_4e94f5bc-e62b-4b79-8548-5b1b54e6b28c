<?php

namespace AppBundle\Model;
use AppBundle\Entity\Country;

class Merchant
{

    /**
     * @var int $id
     */
    private $id;

    /**
     * @var string logo
     */
    private $logo;

    /**
     * @var string $name
     */
    private $name;

    /**
     * @var string $shortDescription
     */
    private $shortDescription;

    /**
     * @deprecated
     * @var string $longDescription
     */
    private $longDescription;

    /**
     * @var string $tacLink
     */
    private $tacLink;

    /**
     * @var Country $country
     */
    private $country;

    /**
     * @var string $status
     */
    private $status;

    /**
     * @var string $mainContactFirstName
     */
    private $mainContactFirstName;

    /**
     * @var string $mainContactLastName
     */
    private $mainContactLastName;

    /**
     * @var string $mainContactEmail
     */
    private $mainContactEmail;

    /**
     * @var string $phoneNumber
     */
    private $phoneNumber;

    /**
     * @var float $rating
     */
    private $rating;

    /**
     * @var string $language
     */
    private $language;

    /**
     * @var bool $adaptedCompany
     */
    private $adaptedCompany;

    /**
     * @var String[]
     */
    private $branches;

    /**
     * @var String slug
     */
    private $slug;

    /**
     * @var array
     */
    private $notificationOrderContacts;

    /**
     * @var array
     */
    private $notificationQuoteContacts;

    /**
     * @var array
     */
    private $notificationThreadContacts;

    /**
     * @var array
     */
    private $notificationCommercialContacts;

    private ?float $minimumOrderAmount = null;

    public function __construct()
    {
        $this->adaptedCompany = false;
        $this->branches = [];
        $this->notificationOrderContacts = [];
        $this->notificationQuoteContacts = [];
        $this->notificationThreadContacts = [];
        $this->notificationCommercialContacts = [];
    }

    /**
     * @return int
     */
    public function getId(): ?int
    {
        return $this->id;
    }

    /**
     * @param int $id
     */
    public function setId(?int $id): self
    {
        $this->id = $id;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getMainContactFirstName(): ?string
    {
      return $this->mainContactFirstName;
    }

    /**
     * @param string|null $mainContactFirstName
     */
    public function setMainContactFirstName(?string $mainContactFirstName): void
    {
      $this->mainContactFirstName = $mainContactFirstName;
    }

    /**
     * @return string|null
     */
    public function getMainContactLastName(): ?string
    {
      return $this->mainContactLastName;
    }

    /**
     * @param string|null $mainContactLastName
     */
    public function setMainContactLastName(?string $mainContactLastName): void
    {
      $this->mainContactLastName = $mainContactLastName;
    }

    /**
     * @return string|null
     */
    public function getMainContactEmail(): ?string
    {
      return $this->mainContactEmail;
    }

    /**
     * @param string|null $mainContactEmail
     */
    public function setMainContactEmail(?string $mainContactEmail): self
    {
      $this->mainContactEmail = $mainContactEmail;
      return $this;
    }

    /**
     * @return Country
     */
    public function getCountry(): ?Country
    {
      return $this->country;
    }

  /***
   * @param Country|null $country
   */
    public function setCountry(?Country $country): void
    {
      $this->country = $country;
    }

    /**
     * @return string
     */
    public function getLogo(): ?string
    {
        return $this->logo;
    }

    /**
     * @param string $logo
     */
    public function setLogo(?string $logo): void
    {
        $this->logo = $logo;
    }

    /**
     * @return string
     */
    public function getName(): ?string
    {
        return $this->name;
    }

    /**
     * @param string $name
     */
    public function setName(?string $name): void
    {
        $this->name = $name;
    }

    /**
     * @return string
     */
    public function getShortDescription(): ?string
    {
        return $this->shortDescription;
    }

    /**
     * @param string $shortDescription
     */
    public function setShortDescription(?string $shortDescription): void
    {
        $this->shortDescription = $shortDescription;
    }

    /**
     * @deprecated
     * @return string
     */
    public function getLongDescription(): ?string
    {
        return $this->longDescription;
    }

    /**
     * @deprecated
     * @param string $longDescription
     */
    public function setLongDescription(?string $longDescription): void
    {
        $this->longDescription = $longDescription;
    }

    /**
     * @return string
     */
    public function getTacLink(): ?string
    {
        return $this->tacLink;
    }

    /**
     * @param string $tacLink
     */
    public function setTacLink(?string $tacLink): void
    {
        $this->tacLink = $tacLink;
    }

    /**
     * @return mixed
     */
    public function getStatus()
    {
        return $this->status;
    }

    /**
     * @param mixed $status
     */
    public function setStatus($status)
    {
        $this->status = $status;
    }

    /**
     * @return string
     */
    public function getPhoneNumber(): string
    {
        return $this->phoneNumber;
    }

    /**
     * @param string $phoneNumber
     */
    public function setPhoneNumber(string $phoneNumber): void
    {
        $this->phoneNumber = $phoneNumber;
    }

    /**
     * @return float
     */
    public function getRating(): ?float
    {
        return $this->rating;
    }

    /**
     * @param float $rating
     */
    public function setRating(?float $rating): void
    {
        $this->rating = $rating;
    }

    /**
     * @return string|null
     */
    public function getLanguage(): ?string
    {
        return $this->language;
    }

    /**
     * @param string|null $language
     * @return Merchant
     */
    public function setLanguage(?string $language): self
    {
        $this->language = $language;
        return $this;
    }

    public function isAdaptedCompany(): bool
    {
        return $this->adaptedCompany;
    }

    public function setAdaptedCompany(bool $adaptedCompany): self
    {
        $this->adaptedCompany = $adaptedCompany;
        return $this;
    }

    public function getBranches(): array
    {
        return $this->branches;
    }

    public function setBranches(array $branches): self
    {
        $this->branches = $branches;
        return $this;
    }

    /**
     * @return String
     */
    public function getSlug(): String
    {
        return $this->slug;
    }

    /**
     * @param String $slug
     */
    public function setSlug(String $slug): void
    {
        $this->slug = $slug;
    }

    /**
     * @return array
     */
    public function getNotificationOrderContacts(): array
    {
        return $this->notificationOrderContacts;
    }

    /**
     * @param array $notificationOrderContacts
     *
     * @return $this
     */
    public function setNotificationOrderContacts(array $notificationOrderContacts): self
    {
        $this->notificationOrderContacts = $notificationOrderContacts;

        return $this;
    }

    /**
     * @return array
     */
    public function getNotificationQuoteContacts(): array
    {
        return $this->notificationQuoteContacts;
    }

    /**
     * @param array $notificationQuoteContacts
     *
     * @return $this
     */
    public function setNotificationQuoteContacts(array $notificationQuoteContacts): self
    {
        $this->notificationQuoteContacts = $notificationQuoteContacts;

        return $this;
    }

    /**
     * @return array
     */
    public function getNotificationThreadContacts(): array
    {
        return $this->notificationThreadContacts;
    }

    /**
     * @param array $notificationThreadContacts
     *
     * @return $this
     */
    public function setNotificationThreadContacts(array $notificationThreadContacts): self
    {
        $this->notificationThreadContacts = $notificationThreadContacts;

        return $this;
    }

    /**
     * @return array
     */
    public function getNotificationCommercialContacts(): array
    {
        return $this->notificationCommercialContacts;
    }

    /**
     * @param array $notificationCommercialContacts
     *
     * @return $this
     */
    public function setNotificationCommercialContacts(array $notificationCommercialContacts): self
    {
        $this->notificationCommercialContacts = $notificationCommercialContacts;
        return $this;
    }


    public function getMinimumOrderAmount(): ?float
    {
        return $this->minimumOrderAmount;
    }

    public function setMinimumOrderAmount(?float $minimumOrderAmount): self
    {
        $this->minimumOrderAmount = $minimumOrderAmount;
        return $this;
    }
}
