<?php

namespace AppBundle\Twig;

use Symfony\Component\HttpFoundation\RequestStack;
use Twig\Extension\AbstractExtension;
use Twig\TwigFilter;

class LocalizeUrlExtension extends AbstractExtension
{
	private $locale;

	/**
	 * CountryExtension constructor.
	 * @param $request_stack
	 */
	public function __construct(RequestStack $request_stack)
	{
	    $request = $request_stack->getCurrentRequest();
	    if($request){
            $this->locale = $request_stack->getCurrentRequest()->getLocale();
        }else{
	        $this->locale = "en";
        }

	}

	public function getFilters()
	{
		return array(
			new TwigFilter('localize', array($this, 'localizeUrl')),
		);
	}

	public function localizeUrl($url)
	{
		return '/' . $this->locale . '/' . preg_replace('%^/%','',$url);
	}
}