<?php

namespace Open\IzbergBundle\Api;

use AppBundle\Entity\Country;
use AppBundle\Entity\User;
use AppBundle\Services\CountryService;
use Open\IzbergBundle\Api;
use Open\IzbergBundle\Model\Merchant;
use Unirest;

/**
 * Provide Wrapper for Merchant API
 * Class MerchantApi
 * @package Open\IzbergBundle\Api
 */
class MerchantApi extends Api
{
    private const MERCHANT = 'merchant';
    private const MERCHANT_SLUG = 'merchant/';
    private const ERROR_UNKNOWN_COUNTRY = '"Unknwon Country: "';

    public function  getUri(){
        return 'merchant';
    }

    public function getItemClass(){
        return Merchant::class;
    }

    /**
     * @var CountryService
     */
    private $countryService;

    public function setCountryService(CountryService $countryService): void
    {
        $this->countryService = $countryService;
    }

    /**
     * Return Merchant entiry Schema
     * @link https://documenter.getpostman.com/view/653426/izberg-api-official-collection/Jsix#bc4faf-411f-0a23-e6f5-163dac395249
     * @return mixed
     */
    public function getSchema()
    {
        $response = $this->sendApiRequest(
            self::HTTP_GET_OPERATION,
            'merchant/schema/',
            null,
            [],
            [
                self::OPTION_IS_OPERATOR => true,
            ]
        );

        return $response->body;
    }

    public function activateMerchant($merchantId)
    {
        $this->sendApiRequest(
            self::HTTP_POST_OPERATION,
            self::MERCHANT_SLUG . $merchantId . '/activate/',
            '{}',
            [],
            [
                self::OPTION_IS_OPERATOR => true,
                self::OPTION_NO_DATA_RETURN => true,
            ]
        );
    }

    /**
     * create and assign an address to a merchant
     * @param $merchantId
     * @param boolean $billing_address
     * @param boolean $return_address
     * @param string $first_name
     * @param string $last_name
     * @param string $address
     * @param string $city
     * @param int $country_id
     * @param string $contact_email
     * @param string $phone
     * @param integer $social_reason
     * @throws ApiException
     */
    public function addMerchantAddress(
        $merchantId,
        $billing_address,
        $return_address,
        $first_name,
        $last_name,
        $address,
        $city,
        $country_id,
        $contact_email,
        $phone,
        $social_reason
    )
    {
        // Get selected country to find the code
        /**
         * @var Country $country
         */
        $country = $this->countryService->getCountryById($country_id);
        if ($country === null){
            /**
             * @var User $user
             */
            $this->writeGenericErrorLog(self::ERROR_UNKNOWN_COUNTRY . $country_id, []);
            throw new ApiException(self::ERROR_UNKNOWN_COUNTRY . $country_id);
        }


        $data = [
            self::MERCHANT => "/v1/merchant/".$merchantId."/",
            "billing_address" => $billing_address,
            "return_address" => $return_address,
            "country" => "/v1/country/" . $country->getIzbergId() . "/",
            "contact_first_name" => $first_name,
            "contact_last_name" => $last_name,
            "address" => $address,
            "city" => $city,
            "contact_email" => $contact_email,
            "phone" => $phone,
            "contact_social_reason" => $social_reason,
        ];

        $this->sendApiRequest(
            self::HTTP_POST_OPERATION,
            'merchant_address/',  //don't forget the ending slash
            $data,
            [],
            [
                self::OPTION_IS_OPERATOR => true,
                self::OPTION_NO_DATA_RETURN => true,
            ]
        );
    }

    /**
     * add default transport assignment for merchant
     * @param int $merchantId the identifier of the merchant
     * @param int $carrier
     * @param int $shipping_provider
     * @param int $zone
     * @throws ApiException
     */
    public function addDefaultTransportAssignment(
        $merchantId,
        $carrier,
        $shipping_provider,
        $zone
    )
    {
        $data = [
            'rank' => "1",
            self::MERCHANT => "/v1/merchant/".$merchantId."/",
            'options' => array(
                "collection_within_hours" => 24,
                "delivery_within_hours" => 48,
                "infinite" => null,
                "limits" => []
            ),
            "status" => "enabled",
            "application_category" => null,
            "brand" => null,
            "carrier" => "/v1/active_carrier/" . $carrier . "/",
            "provider" => "/v1/shipping_provider/" . $shipping_provider . "/",
            "zone" => "/v1/zone/" . $zone . "/"
        ];

        $this->sendApiRequest(
            self::HTTP_POST_OPERATION,
            'shipping_provider_assignment/',  //don't forget the ending slash
            $data,
            [],
            [
                self::OPTION_IS_OPERATOR => true,
                self::OPTION_NO_DATA_RETURN => true,
            ]
        );
    }

    /**
     * Get list of merchants
     * @link https://documenter.getpostman.com/view/653426/izberg-api-official-collection/Jsix#fbc0e19-4ec1-7d8f-21de-47fca0288e4e
     * @return mixed
     */
    public function getMerchants()
    {
        $application_id = $this->getApplicationId();

        $response = $this->sendApiRequest(
            self::HTTP_GET_OPERATION,
            'application/' . $application_id . '/merchants/',
            null,
            [],
            [
                self::OPTION_IS_OPERATOR => true,
            ]
        );

        return $response->body;
    }

    /**
     * set the merchant status to disable to pending
     * @param integer $id merchant id
     */
    public function checkActivation($id)
    {
        $this->sendApiRequest(
            self::HTTP_GET_OPERATION,
            'merchant/' . $id . '/check_activation/',
            null,
            [],
            [
                self::OPTION_IS_OPERATOR => true,
                self::OPTION_NO_DATA_RETURN => true,
            ]
        );
    }


    /**
     * Fetch a merchant by it's id
     * @link https://documenter.getpostman.com/view/653426/izberg-api-official-collection/Jsix#beb75d6-78f8-7bac-30d0-4da8555ab4ac
     * @param $id
     * @return \stdClass
     * @throws ApiException
     */
    public function getMerchant($id)
    {
        $response = $this->sendApiRequest(
            self::HTTP_GET_OPERATION,
            self::MERCHANT_SLUG . $id . '/',
            null,
            [],
            [
                self::OPTION_IS_OPERATOR => true,
            ]
        );

        return $response->body;
    }

    /**
     * fetch custom attributes of a merchant
     * @param $id
     * @param $key
     * @return mixed
     */
    public function getMerchantCustomAttribute($id, $key)
    {
        $response = $this->sendApiRequest(
            self::HTTP_GET_OPERATION,
            self::MERCHANT_SLUG . $id . '/attributes/?key='.$key,
            null,
            [],
            [
                self::OPTION_IS_OPERATOR => true,
            ]
        );

        $attributes = $response->body->objects;
        foreach($attributes as $attribute){
            if ($attribute->key === $key){
                return $attribute->value;
            }
        }
        //if attribute not found, we return null
        return null;
    }

    /**
     * just create a merchant on izberg (without user)
     * @return \stdClass
     */
    public function createMerchant($name, $countryIzbergId, $offerType, $identification)
    {
        $data = [
            "name" => $name,
            "company" => [
                "country" => sprintf('/v1/country/%s/', $countryIzbergId)
            ],
            "offer_type" => $offerType,
        ];

        $response = $this->sendApiRequest(
            self::HTTP_POST_OPERATION,
            self::MERCHANT_SLUG,
            $data,
            [],
            [
                self::OPTION_IS_OPERATOR => true,
            ]
        );

        return $response->body;
    }

    /**
     * Build an izberg merchant: Contains all the business rules to build an izberg merchant
     * @param $raison_sociale
     * @param $email
     * @param $password
     * @param $first_name
     * @param $last_name
     * @param $identification
     * @param $country
     * @param string|null $environment
     * @param string|null $custom_environment
     * @param bool $tvaChecker
     * @return array the merchant to create
     */
    public function buildIzbergMerchant(
        $raison_sociale,
        $email,
        $password,
        $first_name,
        $last_name,
        $identification,
        $country,
        string $environment = null,
        string $custom_environment = null,
        bool $tvaChecker = true
    ): array
    {
        if ($country === null){
            /**
             * @var User $user
             */
            $this->writeGenericErrorLog("country must not be null", []);
            throw new ApiException("country must not be null");
        }

        $data =  array(
            "email" => $email,
            "first_name" => $first_name,
            "last_name" => $last_name,
            "password" => $password,
            "merchant" => array(
                "name" => $raison_sociale,
                "company" => [
                    "country" => sprintf('/v1/country/%s/', $country->getIzbergId())
                ],
                "offer_type" => "product_offers",
            ),
        );
        //if country is in EU, create custom izberg attribute (only if TVA Number is setup)
        if ($tvaChecker && $country->isInEU()){
            $data['merchant']['vat_number'] = $identification;
        }

        if($environment != null){
            $data['environment'] = $environment;
        }

        if($custom_environment != null){
            $data['custom_environment'] = $custom_environment;
        }

        return $data;
    }

    /**
     * @param \stdClass $merchant
     * @param array $supportedLanguages
     * @return string detected merchant language or "en" if not found
     */
    public static function getMerchantLanguage ($merchant, $supportedLanguages){
        if (property_exists($merchant, "prefered_language") && !empty($merchant->prefered_language) && in_array($merchant->prefered_language, $supportedLanguages)){
            return $merchant->prefered_language;
        }
        //return en as default
        return "en";
    }

    public function updateMerchantCurrency($merchantId, $currency){
        $data = array(
            'currencies' => ["/v1/currency/".$currency."/"],
            'default_currency' => $currency,

        );

        $this->updateMerchant($merchantId, $data);
    }

    public function updateMerchantLanguage(int $merchantId, string $language)
    {
        $language = strtolower($language);
//        if(!in_array($language, ['en', 'fr'])) {
//            return;
//        }

        $data = [
            'languages' => [$language],
            'prefered_language' => $language,

        ];

        $this->updateMerchant($merchantId, $data);
    }

    public function updateMerchant($merchantId, $data)
    {
        $this->sendApiRequest(
            self::HTTP_PATCH_OPERATION,
            self::MERCHANT_SLUG . $merchantId . '/',
            $data,
            [],
            [
                self::OPTION_IS_OPERATOR => true,
                self::OPTION_NO_DATA_RETURN => true,
            ]
        );
    }

    public function updateMerchantTaxRate(int $merchantId, int $taxRate)
    {
        $commissionSettingsId = null;
        $response = $this->sendApiRequest(
            self::HTTP_GET_OPERATION,
            self::MERCHANT_SLUG . $merchantId . '/commission_settings/',
            null,
            [],
            [
                self::OPTION_IS_OPERATOR => true,
            ]
        );

        $commissionSettingsId = $response->body->id ?? null;

        if (!$commissionSettingsId) {
            throw new ApiException(
                sprintf(
                    'Cannot found commission settings id for merchant %s',
                    $merchantId
                )
            );
        }

        $this->sendApiRequest(
            self::HTTP_PUT_OPERATION,
            'commission_settings/' . $commissionSettingsId . '/',
            [
                'applicable_tax_rate' => $taxRate,
            ],
            [],
            [
                self::OPTION_IS_OPERATOR => true,
                self::OPTION_NO_DATA_RETURN => true,
            ]
        );
    }

    public function addOrUpdateMerchantReview(int $merchantId, int $merchantOrderId, string $comment, float $rating, ?string $reviewId = null)
    {
        $applicationId = $this->getApplicationId();
        $data = [
            "application" => "/v1/application/".$applicationId."/",
            "merchant"=> "/v1/merchant/".$merchantId."/",
            "merchant_order"=> "/v1/merchant_order/".$merchantOrderId."/",
            "title"=> "",
            "body"=> $comment,
            "score"=> $rating,
        ];

        $method = ($reviewId) ? self::HTTP_PATCH_OPERATION : self::HTTP_POST_OPERATION;
        $url = ($reviewId) ? 'merchant-review/'.$reviewId.'/' : 'merchant-review/';

        $response = $this->sendApiRequest(
            $method,
            $url,
            $data,
            [],
            [
                self::OPTION_NO_DATA_RETURN => true,
            ]
        );
        return $response->body;
    }

    public function getMerchantReviews(int $merchantId)
    {
        $response = $this->sendApiRequest(
            self::HTTP_GET_OPERATION,
            'merchant-review/?merchant_id='.$merchantId,
            null,
            [],
            []
        );

        return $response->body;
    }
}
