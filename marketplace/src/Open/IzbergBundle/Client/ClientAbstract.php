<?php

namespace Open\IzbergBundle\Client;


use Open\Izberg\IzbergConfiguration;
use Open\IzbergBundle\Api\ApiConfiguration;
use Open\IzbergBundle\ApiInterface;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpClient\HttpClient;
use Symfony\Component\HttpClient\ScopingHttpClient;
use Symfony\Contracts\HttpClient\HttpClientInterface;

abstract class ClientAbstract implements LoggerAwareInterface
{
    protected LoggerInterface $logger;
    protected ?string $bearer = null;
    protected HttpClientInterface $httpClient;
    protected ApiConfiguration $izbergConfiguration;


    public function __construct(
         ApiConfiguration $izbergConfiguration
    ) {
        $this->izbergConfiguration = $izbergConfiguration;
    }

    public function isInit(): bool
    {
        return $this->bearer !== null;
    }

    public function initClientHttp(string $bearer)
    {
        if ($bearer === $this->bearer) {
            return;
        }

        $httpClient = HttpClient::create();
        $this->httpClient = ScopingHttpClient::forBaseUri($httpClient, $this->izbergConfiguration->getApiUrl(), [
            'auth_bearer' => $bearer,
        ]);
        $this->bearer = $bearer;
    }

    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }
    public function callIzbergApiEvent()
    {
    }

    protected function callApi(string $classApi): ApiInterface
    {
        $this->callIzbergApiEvent();

        $classApi = explode('\\', $classApi);
        $classApi = array_pop($classApi);
        $classApi = lcfirst($classApi);

        $this->$classApi->setHttpClient($this->httpClient);
        $this->$classApi->setLogger($this->logger);
        $this->$classApi->setApplicationId($this->izbergConfiguration->getApplicationId());
        $this->$classApi->setSecretKey($this->izbergConfiguration->getSecretKey());

        return $this->$classApi;
    }
}
